#!/usr/bin/env python3
"""
Indexing Validation Script

Validates that code indexing is actually working by checking:
1. GitHub repository file count
2. Database indexing claims vs reality
3. Weaviate vector storage verification
4. Files ignored by .gitignore
5. Comprehensive comparison and validation

Usage: python validate-indexing.py <analysis_id>
"""

import sys
import asyncio
import tempfile
import subprocess
import os
from pathlib import Path
from typing import Dict, List, Any, Set
import json

# Add the backend path to import our services
sys.path.append('/app')

from app.database import SessionLocal
from app.models.analysis import RepoAnalysis
from app.services.weaviate_vector_service import WeaviateVectorService


class IndexingValidator:
    """Comprehensive indexing validation"""
    
    def __init__(self, analysis_id: int):
        self.analysis_id = analysis_id
        self.db = SessionLocal()
        self.analysis = None
        self.supported_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs', '.cpp', '.c', '.h',
            '.php', '.rb', '.swift', '.kt', '.scala', '.sh', '.sql', '.yaml', '.yml',
            '.json', '.xml', '.html', '.css', '.md', '.txt', '.toml', '.ini', '.cfg', '.conf'
        }
        
    async def validate_complete_indexing(self) -> Dict[str, Any]:
        """Run complete indexing validation"""
        print(f"🔍 INDEXING VALIDATION FOR ANALYSIS {self.analysis_id}")
        print("=" * 60)
        
        try:
            # Step 1: Get analysis from database
            validation_result = await self._get_analysis_data()
            if not validation_result['success']:
                return validation_result
            
            # Step 2: Clone and analyze GitHub repository
            github_data = await self._analyze_github_repository()
            validation_result['github_analysis'] = github_data
            
            # Step 3: Validate Weaviate storage
            weaviate_data = await self._validate_weaviate_storage()
            validation_result['weaviate_analysis'] = weaviate_data
            
            # Step 4: Compare and validate
            comparison = self._compare_and_validate(github_data, weaviate_data)
            validation_result['comparison'] = comparison
            
            # Step 5: Generate final report
            self._generate_validation_report(validation_result)
            
            return validation_result
            
        except Exception as e:
            print(f"❌ Validation failed: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}
        finally:
            self.db.close()
    
    async def _get_analysis_data(self) -> Dict[str, Any]:
        """Get analysis data from database"""
        print("📊 Step 1: Checking database analysis data...")
        
        self.analysis = self.db.query(RepoAnalysis).filter(RepoAnalysis.id == self.analysis_id).first()
        if not self.analysis:
            return {'success': False, 'error': f'Analysis {self.analysis_id} not found'}
        
        # Extract database claims
        db_data = {
            'repository': f"{self.analysis.repo_owner}/{self.analysis.repo_name}",
            'status': self.analysis.status,
            'files_indexed_claim': self.analysis.files_indexed or 0,
            'created_at': self.analysis.created_at,
            'completed_at': self.analysis.completed_at
        }
        
        # Check intermediate data
        if self.analysis.intermediate_data:
            indexing_result = self.analysis.intermediate_data.get('indexing_result', {})
            db_data.update({
                'total_chunks_claim': indexing_result.get('total_chunks', 0),
                'indexing_status': indexing_result.get('status', 'unknown'),
                'code_hash': indexing_result.get('code_hash', 'none')
            })
        
        print(f"✅ Database claims: {db_data['files_indexed_claim']} files, {db_data.get('total_chunks_claim', 0)} chunks")
        return {'success': True, 'database_claims': db_data}
    
    async def _analyze_github_repository(self) -> Dict[str, Any]:
        """Clone and analyze the actual GitHub repository"""
        print("📁 Step 2: Analyzing GitHub repository...")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            repo_path = os.path.join(temp_dir, 'repo')
            repo_url = f"https://github.com/{self.analysis.repo_owner}/{self.analysis.repo_name}"
            
            try:
                # Clone repository
                print(f"   Cloning {repo_url}...")
                result = subprocess.run([
                    'git', 'clone', '--depth', '1', repo_url, repo_path
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode != 0:
                    return {'error': f'Failed to clone repository: {result.stderr}'}
                
                # Analyze repository
                return self._analyze_repository_files(repo_path)
                
            except Exception as e:
                return {'error': f'Repository analysis failed: {e}'}
    
    def _analyze_repository_files(self, repo_path: str) -> Dict[str, Any]:
        """Analyze files in the cloned repository"""
        
        # Parse .gitignore
        gitignore_patterns = self._parse_gitignore(repo_path)
        
        # Count all files
        total_files = 0
        code_files = 0
        ignored_files = 0
        large_files = 0
        indexable_files = 0
        
        file_types = {}
        ignored_examples = []
        large_examples = []
        
        for root, dirs, files in os.walk(repo_path):
            # Skip .git directory
            dirs[:] = [d for d in dirs if d != '.git']
            
            for file in files:
                total_files += 1
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, repo_path)
                
                # Check if file should be ignored
                if self._should_ignore_by_gitignore(rel_path, gitignore_patterns):
                    ignored_files += 1
                    if len(ignored_examples) < 5:
                        ignored_examples.append(rel_path)
                    continue
                
                # Check if it's a code file
                file_ext = Path(file).suffix.lower()
                if file_ext in self.supported_extensions:
                    code_files += 1
                    file_types[file_ext] = file_types.get(file_ext, 0) + 1
                    
                    # Check file size
                    try:
                        file_size = os.path.getsize(file_path)
                        if file_size > 5 * 1024 * 1024:  # 5MB
                            large_files += 1
                            if len(large_examples) < 5:
                                large_examples.append(f"{rel_path} ({file_size // 1024 // 1024}MB)")
                        else:
                            indexable_files += 1
                    except:
                        pass
        
        return {
            'total_files': total_files,
            'code_files': code_files,
            'indexable_files': indexable_files,
            'ignored_files': ignored_files,
            'large_files': large_files,
            'gitignore_patterns': len(gitignore_patterns),
            'file_types': dict(sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]),
            'ignored_examples': ignored_examples,
            'large_examples': large_examples
        }
    
    def _parse_gitignore(self, repo_path: str) -> Set[str]:
        """Parse .gitignore file and return patterns"""
        gitignore_patterns = set()
        gitignore_path = os.path.join(repo_path, '.gitignore')
        
        if os.path.exists(gitignore_path):
            try:
                with open(gitignore_path, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and not line.startswith('!'):
                            gitignore_patterns.add(line.rstrip('/'))
            except Exception:
                pass
        
        return gitignore_patterns
    
    def _should_ignore_by_gitignore(self, rel_path: str, gitignore_patterns: Set[str]) -> bool:
        """Check if file should be ignored based on gitignore patterns"""
        if not gitignore_patterns:
            return False
        
        rel_path = rel_path.replace('\\', '/')
        
        for pattern in gitignore_patterns:
            if pattern == rel_path or rel_path.startswith(pattern + '/'):
                return True
            if '*' in pattern:
                import fnmatch
                if fnmatch.fnmatch(rel_path, pattern):
                    return True
        
        return False

    async def _validate_weaviate_storage(self) -> Dict[str, Any]:
        """Validate actual Weaviate storage"""
        print("🔍 Step 3: Validating Weaviate vector storage...")

        try:
            weaviate_service = WeaviateVectorService()
            await weaviate_service.connect()

            if not weaviate_service.client:
                return {'error': 'Weaviate client not connected'}

            # Check if CodeChunk class exists
            schema = weaviate_service.client.schema.get()
            classes = [cls['class'] for cls in schema.get('classes', [])]

            if 'CodeChunk' not in classes:
                return {'error': 'CodeChunk class not found in Weaviate schema'}

            # Get total count first
            aggregate_result = weaviate_service.client.query.aggregate('CodeChunk').with_where({
                'path': ['analysis_id'],
                'operator': 'Equal',
                'valueInt': self.analysis_id
            }).with_meta_count().do()

            total_chunks = aggregate_result.get('data', {}).get('Aggregate', {}).get('CodeChunk', [{}])[0].get('meta', {}).get('count', 0)

            # Get ALL chunks for this analysis to count unique files properly
            # Use a high limit to ensure we get all chunks
            limit = max(10000, total_chunks + 100)  # Ensure we get all chunks
            query_result = weaviate_service.client.query.get('CodeChunk', ['file_path', 'language', 'content', 'chunk_index']).with_where({
                'path': ['analysis_id'],
                'operator': 'Equal',
                'valueInt': self.analysis_id
            }).with_limit(limit).do()

            chunks = query_result.get('data', {}).get('Get', {}).get('CodeChunk', [])

            # Analyze chunk data
            file_paths = set()
            languages = set()
            sample_chunks = []

            for chunk in chunks:
                if chunk.get('file_path'):
                    file_paths.add(chunk['file_path'])
                if chunk.get('language'):
                    languages.add(chunk['language'])

                sample_chunks.append({
                    'file_path': chunk.get('file_path', 'unknown'),
                    'language': chunk.get('language', 'unknown'),
                    'content_length': len(chunk.get('content', '')),
                    'chunk_index': chunk.get('chunk_index', 0)
                })

            print(f"✅ Weaviate storage: {total_chunks} chunks from {len(file_paths)} files")

            return {
                'total_chunks_actual': total_chunks,
                'unique_files_actual': len(file_paths),
                'languages_detected': list(languages),
                'sample_chunks': sample_chunks[:5],
                'weaviate_connected': True
            }

        except Exception as e:
            print(f"❌ Weaviate validation failed: {e}")
            return {'error': f'Weaviate validation failed: {e}'}

    def _compare_and_validate(self, github_data: Dict[str, Any], weaviate_data: Dict[str, Any]) -> Dict[str, Any]:
        """Compare GitHub repository data with Weaviate storage"""
        print("⚖️  Step 4: Comparing and validating data...")

        if 'error' in github_data:
            return {'validation_status': 'failed', 'reason': f"GitHub analysis failed: {github_data['error']}"}

        if 'error' in weaviate_data:
            return {'validation_status': 'failed', 'reason': f"Weaviate validation failed: {weaviate_data['error']}"}

        # Get database claims from analysis
        db_claims = {
            'files_indexed_claim': self.analysis.files_indexed or 0,
            'total_chunks_claim': 0
        }

        if self.analysis.intermediate_data:
            indexing_result = self.analysis.intermediate_data.get('indexing_result', {})
            db_claims['total_chunks_claim'] = indexing_result.get('total_chunks', 0)

        # Compare data
        github_indexable = github_data.get('indexable_files', 0)
        weaviate_files = weaviate_data.get('unique_files_actual', 0)
        weaviate_chunks = weaviate_data.get('total_chunks_actual', 0)

        db_files_claim = db_claims.get('files_indexed_claim', 0)
        db_chunks_claim = db_claims.get('total_chunks_claim', 0)

        # Validation checks
        validations = {
            'files_match_github': abs(weaviate_files - github_indexable) <= 5,  # Allow 5 file difference
            'files_match_database': weaviate_files == db_files_claim,
            'chunks_match_database': weaviate_chunks == db_chunks_claim,
            'chunks_reasonable': weaviate_chunks > 0 and weaviate_chunks >= weaviate_files,
            'gitignore_respected': github_data.get('ignored_files', 0) > 0
        }

        # Overall validation
        overall_valid = all(validations.values())

        return {
            'validation_status': 'passed' if overall_valid else 'failed',
            'validations': validations,
            'comparison': {
                'github_total_files': github_data.get('total_files', 0),
                'github_indexable_files': github_indexable,
                'github_ignored_files': github_data.get('ignored_files', 0),
                'weaviate_files_actual': weaviate_files,
                'weaviate_chunks_actual': weaviate_chunks,
                'database_files_claim': db_files_claim,
                'database_chunks_claim': db_chunks_claim
            }
        }

    def _generate_validation_report(self, validation_result: Dict[str, Any]):
        """Generate comprehensive validation report"""
        print("\n" + "=" * 60)
        print("📋 INDEXING VALIDATION REPORT")
        print("=" * 60)

        # Database claims
        db_claims = validation_result.get('database_claims', {})
        print(f"🗄️  Database Claims:")
        print(f"   Repository: {db_claims.get('repository', 'unknown')}")
        print(f"   Status: {db_claims.get('status', 'unknown')}")
        print(f"   Files Claimed: {db_claims.get('files_indexed_claim', 0)}")
        print(f"   Chunks Claimed: {db_claims.get('total_chunks_claim', 0)}")

        # GitHub analysis
        github_data = validation_result.get('github_analysis', {})
        if 'error' not in github_data:
            print(f"\n📁 GitHub Repository Analysis:")
            print(f"   Total Files: {github_data.get('total_files', 0)}")
            print(f"   Code Files: {github_data.get('code_files', 0)}")
            print(f"   Indexable Files: {github_data.get('indexable_files', 0)}")
            print(f"   Ignored Files: {github_data.get('ignored_files', 0)}")
            print(f"   Large Files (>5MB): {github_data.get('large_files', 0)}")
            print(f"   Gitignore Patterns: {github_data.get('gitignore_patterns', 0)}")

            if github_data.get('file_types'):
                print(f"   Top File Types: {dict(list(github_data['file_types'].items())[:5])}")

        # Weaviate validation
        weaviate_data = validation_result.get('weaviate_analysis', {})
        if 'error' not in weaviate_data:
            print(f"\n🔍 Weaviate Storage Validation:")
            print(f"   Chunks Stored: {weaviate_data.get('total_chunks_actual', 0)}")
            print(f"   Files Indexed: {weaviate_data.get('unique_files_actual', 0)}")
            print(f"   Languages: {', '.join(weaviate_data.get('languages_detected', []))}")

        # Validation results
        comparison = validation_result.get('comparison', {})
        if comparison:
            print(f"\n⚖️  Validation Results:")
            status = comparison.get('validation_status', 'unknown')
            print(f"   Overall Status: {'✅ PASSED' if status == 'passed' else '❌ FAILED'}")

            validations = comparison.get('validations', {})
            for check, result in validations.items():
                icon = '✅' if result else '❌'
                print(f"   {icon} {check.replace('_', ' ').title()}: {result}")

            comp_data = comparison.get('comparison', {})
            print(f"\n📊 Data Comparison:")
            print(f"   GitHub → Weaviate: {comp_data.get('github_indexable_files', 0)} → {comp_data.get('weaviate_files_actual', 0)} files")
            print(f"   Database → Weaviate: {comp_data.get('database_files_claim', 0)} → {comp_data.get('weaviate_files_actual', 0)} files")
            print(f"   Chunks: {comp_data.get('database_chunks_claim', 0)} claimed → {comp_data.get('weaviate_chunks_actual', 0)} actual")


async def main():
    """Main validation function"""
    if len(sys.argv) != 2:
        print("Usage: python validate-indexing.py <analysis_id>")
        print("Example: python validate-indexing.py 1")
        sys.exit(1)
    
    try:
        analysis_id = int(sys.argv[1])
    except ValueError:
        print("❌ Error: analysis_id must be a number")
        sys.exit(1)
    
    validator = IndexingValidator(analysis_id)
    result = await validator.validate_complete_indexing()
    
    if result.get('success'):
        print("\n🎉 Validation completed successfully!")
    else:
        print(f"\n❌ Validation failed: {result.get('error', 'Unknown error')}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
