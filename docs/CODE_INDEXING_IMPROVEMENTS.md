# Code Indexing Improvements for Large Repositories

## Problem Identified

For large repositories like `i-am-bee/beeai-framework`, the code indexing was severely limited, showing only **17 files** and **33 chunks** indexed. This is inadequate for comprehensive MCP analysis and capability detection.

## Root Causes Found

### 1. **Conflicting File Size Limits**
- **IndexingService**: 500KB limit
- **RedisVectorService**: 100KB limit ⚠️
- **Result**: RedisVectorService rejected files that IndexingService would accept

### 2. **Very Low File Count Limits**
- **IndexingService**: 200 files maximum
- **IntelligentAnalysisService**: 150 files (regular), 100 files (large repos)
- **For AI Frameworks**: These limits are far too restrictive

### 3. **Shallow Repository Depth**
- **GitHub Tree Depth**: Limited to 3 levels deep
- **AI Frameworks**: Important code often 4-6 levels deep (e.g., `src/core/agents/management/`)

### 4. **Large Repository Penalties**
- **Large Repo Threshold**: 1000 files
- **Large Repo Limit**: Only 100 files processed
- **AI Frameworks**: Penalized for being comprehensive

### 5. **Missing File Extensions**
- **RedisVectorService**: Missing `.md`, `.dockerfile`, etc.
- **AI Frameworks**: Often have important documentation and config files

## Solutions Implemented

### ✅ **1. Aligned File Size Limits**

**Before:**
```python
# IndexingService
self.max_file_size = 500000  # 500KB

# RedisVectorService  
if len(content) > 100000:  # 100KB
    continue
```

**After:**
```python
# IndexingService
self.max_file_size = 1000000  # 1MB

# RedisVectorService
if len(content) > 1000000:  # 1MB
    continue
```

### ✅ **2. Increased File Count Limits**

**Before:**
```python
# IndexingService
self.max_total_files = 200

# IntelligentAnalysisService
self.max_files_to_analyze = 150
self.large_repo_max_files = 100
```

**After:**
```python
# IndexingService
self.max_total_files = 1000

# IntelligentAnalysisService
self.max_files_to_analyze = 500
self.large_repo_max_files = 300
```

### ✅ **3. Increased Repository Depth**

**Before:**
```python
repo_tree = self.github_service.get_repository_tree_sync(
    repo_owner, repo_name, github_token, max_depth=3
)
```

**After:**
```python
repo_tree = self.github_service.get_repository_tree_sync(
    repo_owner, repo_name, github_token, max_depth=6
)
```

### ✅ **4. Raised Large Repository Thresholds**

**Before:**
```python
self.large_repo_threshold = 1000  # Files count threshold
self.large_repo_max_files = 100   # Reduced limit for large repos
```

**After:**
```python
self.large_repo_threshold = 2000  # Files count threshold  
self.large_repo_max_files = 300   # Increased limit for large repos
```

### ✅ **5. Expanded File Extensions**

**Before:**
```python
indexable_extensions = {
    '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs', 
    '.cpp', '.c', '.h', '.php', '.rb', '.swift', '.kt', '.scala'
}
```

**After:**
```python
indexable_extensions = {
    '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs',
    '.cpp', '.c', '.h', '.php', '.rb', '.swift', '.kt', '.scala',
    '.md', '.vue', '.svelte', '.dart', '.lua', '.perl', '.sh',
    '.bash', '.zsh', '.dockerfile'  # Added for AI frameworks
}
```

## Expected Improvements

### **Before Fixes:**
- **Files Indexed**: 17
- **Code Chunks**: 33
- **Coverage**: Very limited, missing most repository code
- **Depth**: Only 3 levels deep
- **File Types**: Limited extensions

### **After Fixes:**
- **Files Indexed**: 300-1000 (depending on repository size)
- **Code Chunks**: 600-2000+ (significantly more comprehensive)
- **Coverage**: Comprehensive coverage of repository capabilities
- **Depth**: Up to 6 levels deep (captures nested AI framework code)
- **File Types**: Expanded to include documentation and config files

## Impact on MCP Analysis

### **✅ Better Capability Detection:**
- More comprehensive code analysis
- Detection of nested AI framework components
- Better understanding of repository architecture

### **✅ Improved MCP Suggestions:**
- More accurate capability matrix
- Better tool extraction from actual code
- Enhanced feasibility scoring

### **✅ Enhanced Conversation Quality:**
- More context for validation
- Better requirement matching
- Reduced false negatives

## Files Modified

1. **`backend/app/services/indexing_service.py`**
   - Increased `max_file_size` to 1MB
   - Increased `max_total_files` to 1000

2. **`backend/app/services/redis_vector_service.py`**
   - Aligned file size limit to 1MB
   - Added missing file extensions

3. **`backend/app/services/intelligent_analysis_service.py`**
   - Increased file analysis limits
   - Raised large repository thresholds
   - Increased repository tree depth to 6 levels

## Testing Recommendations

To verify the improvements:

1. **Re-run Analysis**: Trigger re-analysis of `i-am-bee/beeai-framework`
2. **Check Indexing Stats**: Should show 200-500+ files indexed
3. **Verify Depth**: Check that nested framework files are included
4. **Test MCP Analysis**: Should show more comprehensive capabilities
5. **Enhanced Conversation**: Should provide better feasibility scores

## Configuration for Different Repository Types

### **Small Repositories** (<500 files):
- All files processed
- Full depth analysis
- Comprehensive indexing

### **Medium Repositories** (500-2000 files):
- Up to 500 files processed
- Intelligent prioritization
- Good coverage

### **Large Repositories** (>2000 files):
- Up to 300 most important files
- Priority-based selection
- Focus on core functionality

These improvements ensure that large AI frameworks like `i-am-bee/beeai-framework` receive comprehensive analysis while maintaining performance for smaller repositories.
