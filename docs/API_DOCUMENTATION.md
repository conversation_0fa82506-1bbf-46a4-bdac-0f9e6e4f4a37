# SuperMCP API Documentation

## Overview

SuperMCP provides a comprehensive REST API for repository analysis and MCP server generation with complete workflow integration.

## Base URL
- Development: `http://localhost:8000`
- Production: `https://your-domain.com`

## Authentication

All API endpoints require JWT authentication obtained through GitHub OAuth.

### Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Complete Workflow API

### 1. Repository Analysis

#### Start Analysis
```http
POST /api/v1/analysis
```

**Request Body:**
```json
{
  "repo_url": "https://github.com/owner/repo",
  "analysis_type": "comprehensive"
}
```

**Response:**
```json
{
  "analysis_id": 1,
  "status": "started",
  "workflow_stage": "repository_analysis"
}
```

#### Get Analysis Status
```http
GET /api/v1/analysis/{analysis_id}
```

**Response:**
```json
{
  "id": 1,
  "repo_url": "https://github.com/owner/repo",
  "status": "completed",
  "workflow_stage": "mcp_analysis",
  "created_at": "2024-01-01T00:00:00Z",
  "completed_at": "2024-01-01T00:05:00Z"
}
```

### 2. Workflow State Management

#### Get Workflow State
```http
GET /api/v1/analysis/{analysis_id}/workflow/state
```

**Response:**
```json
{
  "analysis_id": 1,
  "current_stage": "mcp_analysis",
  "completed_stages": ["repository_analysis", "code_indexing"],
  "available_actions": ["start_conversation", "view_mcp_suggestions"],
  "context_summary": {
    "repository_name": "example-repo",
    "primary_language": "Python",
    "capabilities_count": 15,
    "mcp_suggestions_count": 6
  },
  "stage_details": {
    "repository_analysis": {
      "status": "completed",
      "completion_time": "2024-01-01T00:02:00Z"
    },
    "code_indexing": {
      "status": "completed", 
      "completion_time": "2024-01-01T00:03:00Z"
    },
    "mcp_analysis": {
      "status": "in_progress",
      "started_time": "2024-01-01T00:03:00Z"
    }
  }
}
```

### 3. Enhanced Conversation

#### Start Enhanced Chat
```http
POST /api/v1/analysis/{analysis_id}/enhanced-chat
```

**Request Body:**
```json
{
  "message": "I want to create an MCP server for document parsing",
  "conversation_history": []
}
```

**Response:**
```json
{
  "response": "Based on your repository analysis, I can help you create a document parsing MCP server...",
  "context_used": {
    "repository_capabilities": 15,
    "relevant_functions": ["parse_document", "extract_fields"],
    "feasibility_score": 0.85
  },
  "validation_results": {
    "is_feasible": true,
    "confidence_score": 0.85,
    "capability_matches": [
      {
        "name": "document_parsing",
        "match_score": 0.9,
        "file_path": "src/parser.py"
      }
    ],
    "missing_capabilities": [],
    "suggestions": ["Focus on core document processing functions"]
  },
  "next_actions": ["validate_requirements", "prepare_playground"]
}
```

### 4. Requirements Validation

#### Validate Requirements
```http
POST /api/v1/analysis/{analysis_id}/validate-requirements
```

**Request Body:**
```json
{
  "requirements": "I want document parsing and field extraction tools"
}
```

**Response:**
```json
{
  "is_feasible": true,
  "feasibility_score": 0.85,
  "capability_matches": [
    {
      "name": "document_parsing",
      "description": "Parse documents using VisionAgent API",
      "match_score": 0.9,
      "file_path": "src/parser.py",
      "function_name": "parse_document"
    }
  ],
  "missing_capabilities": [],
  "suggestions": [
    "Repository has strong document processing capabilities",
    "Consider focusing on core parsing functions"
  ],
  "requirements_coverage": 0.95
}
```

### 5. MCP Generation

#### Generate from Workflow
```http
POST /api/v1/analysis/{analysis_id}/generate-from-workflow
```

**Request Body:**
```json
{
  "target_language": "python",
  "validated_requirements": "Document parsing and extraction tools"
}
```

**Response:**
```json
{
  "generation_id": "gen_123",
  "status": "completed",
  "files": {
    "main.py": "# Functional MCP Server\nfrom mcp.server import Server...",
    "requirements.txt": "mcp>=1.0.0\naiohttp>=3.8.0",
    "README.md": "# Document Parser MCP Server..."
  },
  "tools_generated": [
    {
      "name": "parse_document",
      "description": "Parse document using repository function",
      "source_function": "src.parser.parse_document",
      "parameters": ["document_path", "options"]
    }
  ],
  "validation_results": {
    "tools_count": 3,
    "requirements_coverage": 0.95,
    "uses_real_functions": true,
    "fallback_code": false
  },
  "download_url": "/api/v1/download/gen_123"
}
```

## Repository Management

### List User Repositories
```http
GET /api/v1/repositories
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 30)
- `search`: Search query for repository names

**Response:**
```json
{
  "repositories": [
    {
      "id": 123456,
      "name": "example-repo",
      "full_name": "owner/example-repo",
      "description": "Example repository",
      "language": "Python",
      "stargazers_count": 42,
      "private": false,
      "html_url": "https://github.com/owner/example-repo"
    }
  ],
  "total_count": 150,
  "page": 1,
  "per_page": 30
}
```

## Error Handling

### Standard Error Response
```json
{
  "error": "validation_failed",
  "message": "Requirements validation failed: insufficient repository capabilities",
  "details": {
    "feasibility_score": 0.15,
    "missing_capabilities": ["api_gateway", "load_balancing"],
    "suggestions": ["Consider focusing on document processing capabilities"]
  }
}
```

### HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation failed)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Unprocessable Entity (requirements not feasible)
- `500`: Internal Server Error

## Zero Fallbacks Policy

The API enforces a strict zero fallbacks policy:

- **No Mock Data**: All responses use real repository data
- **Fail-Fast**: API returns errors immediately when real data isn't available
- **Real Functions Only**: Generated MCP servers use actual repository functions
- **Validation Required**: All generation requires successful requirements validation

### Example Error (No Fallbacks)
```json
{
  "error": "insufficient_capabilities",
  "message": "Cannot generate MCP server: requirements not feasible with current repository",
  "details": {
    "feasibility_score": 0.20,
    "reason": "Repository lacks API gateway capabilities",
    "suggestion": "Consider requirements that match repository's document processing focus"
  }
}
```
