#!/usr/bin/env python3
"""
Fix Analysis Results Migration Script

This script fixes the issue where analysis results are stored in intermediate_data.enhanced_results
but the frontend expects them in analysis_results.repository_info.

It migrates the data to the correct location for existing analyses.
"""

import sys
import os
sys.path.append('/app')

from app.database import SessionLocal
from app.models.analysis import RepoAnalysis
import json

def fix_analysis_results():
    """Fix analysis results by copying data from intermediate_data to analysis_results"""
    
    db = SessionLocal()
    try:
        # Get all completed analyses that have intermediate_data but incomplete analysis_results
        analyses = db.query(RepoAnalysis).filter(
            RepoAnalysis.status == "completed",
            RepoAnalysis.intermediate_data.isnot(None)
        ).all()
        
        print(f"Found {len(analyses)} completed analyses to check")
        
        fixed_count = 0
        for analysis in analyses:
            try:
                # Check if analysis_results needs fixing
                analysis_results = analysis.analysis_results or {}
                intermediate_data = analysis.intermediate_data or {}
                
                # If analysis_results only has task metadata, it needs fixing
                if (not analysis_results.get('repository_info') and 
                    intermediate_data.get('enhanced_results')):
                    
                    print(f"Fixing analysis {analysis.id} ({analysis.repo_owner}/{analysis.repo_name})")
                    
                    enhanced_results = intermediate_data['enhanced_results']
                    
                    # Create proper analysis_results structure
                    new_analysis_results = {
                        "repository_info": enhanced_results.get('repository_info', {}),
                        "code_structure": {
                            "file_count": enhanced_results.get('technology_stack', {}).get('total_files', 0),
                            "languages": enhanced_results.get('technology_stack', {}).get('languages', {}),
                            "has_cli": enhanced_results.get('business_logic', {}).get('has_cli', False),
                            "has_api": enhanced_results.get('apis', {}).get('has_api', False),
                            "has_database": enhanced_results.get('integrations', {}).get('has_database', False)
                        },
                        "business_logic": enhanced_results.get('business_logic', {}),
                        "workflows": enhanced_results.get('workflows', {}),
                        "apis": enhanced_results.get('apis', {}),
                        "integrations": enhanced_results.get('integrations', {}),
                        "technology_stack": enhanced_results.get('technology_stack', {}),
                        "mcp_suggestions": enhanced_results.get('mcp_suggestions', {}),
                        "analysis_metadata": enhanced_results.get('analysis_metadata', {}),
                        "files_count": intermediate_data.get('files_count', 0),
                        "processing_method": "enhanced_analysis",
                        "data_source": "migrated_from_intermediate_data"
                    }
                    
                    # Update the analysis_results field
                    analysis.analysis_results = new_analysis_results
                    fixed_count += 1
                    
                else:
                    print(f"Analysis {analysis.id} already has proper analysis_results")
                    
            except Exception as e:
                print(f"Error fixing analysis {analysis.id}: {e}")
                continue
        
        # Commit all changes
        if fixed_count > 0:
            db.commit()
            print(f"Successfully fixed {fixed_count} analyses")
        else:
            print("No analyses needed fixing")
            
    except Exception as e:
        print(f"Error during migration: {e}")
        db.rollback()
    finally:
        db.close()

def verify_fix():
    """Verify that the fix worked"""
    
    db = SessionLocal()
    try:
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == 1).first()
        if analysis:
            print("\n=== VERIFICATION ===")
            print(f"Analysis {analysis.id} ({analysis.repo_owner}/{analysis.repo_name})")
            
            if analysis.analysis_results:
                results = analysis.analysis_results
                print(f"analysis_results keys: {list(results.keys())}")
                
                repo_info = results.get('repository_info', {})
                if repo_info:
                    print(f"repository_info keys: {list(repo_info.keys())}")
                    print(f"Language: {repo_info.get('language', 'Not found')}")
                    print(f"Description: {repo_info.get('description', 'Not found')}")
                else:
                    print("repository_info is still missing")
                    
                tech_stack = results.get('technology_stack', {})
                if tech_stack:
                    print(f"Primary language: {tech_stack.get('primary_language', 'Not found')}")
                    print(f"Total files: {tech_stack.get('total_files', 'Not found')}")
                else:
                    print("technology_stack is missing")
            else:
                print("analysis_results is still None")
                
    except Exception as e:
        print(f"Error during verification: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("Starting analysis results migration...")
    fix_analysis_results()
    verify_fix()
    print("Migration completed!")
