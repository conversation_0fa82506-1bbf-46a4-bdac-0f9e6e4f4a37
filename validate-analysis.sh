#!/bin/bash

# =============================================================================
# COMPREHENSIVE ANALYSIS VALIDATION SCRIPT
# =============================================================================
# Validates all aspects of repository analysis:
# - Repository file counts vs indexed files
# - Code chunks creation and storage
# - Database data persistence
# - Enhanced analysis completion
# - MCP analysis completion  
# - Technology stack population
# - Repository structure population
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
ANALYSIS_ID=${1:-1}
TEMP_DIR="/tmp/validation_$$"

# Helper functions
print_header() {
    echo -e "\n${BLUE}============================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================${NC}"
}

print_section() {
    echo -e "\n${CYAN}📋 $1${NC}"
    echo -e "${CYAN}$(printf '%.0s-' {1..50})${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${PURPLE}📊 $1${NC}"
}

# Cleanup function
cleanup() {
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
}
trap cleanup EXIT

# Check if Docker Compose is running
check_services() {
    print_section "Checking Docker Services"
    
    if ! docker-compose ps | grep -q "Up"; then
        print_error "Docker Compose services are not running"
        echo "Please run: docker-compose up -d"
        exit 1
    fi
    
    print_success "Docker Compose services are running"
}

# Get analysis data from database
get_analysis_data() {
    print_section "Fetching Analysis Data from Database"
    
    ANALYSIS_DATA=$(docker-compose exec -T backend python -c "
from app.database import SessionLocal
from app.models.analysis import RepoAnalysis
import json

db = SessionLocal()
try:
    analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == $ANALYSIS_ID).first()
    if not analysis:
        print('ERROR: Analysis not found')
        exit(1)
    
    # Extract all relevant data
    data = {
        'repo_owner': analysis.repo_owner,
        'repo_name': analysis.repo_name,
        'status': analysis.status,
        'files_indexed': analysis.files_indexed,
        'created_at': str(analysis.created_at),
        'completed_at': str(analysis.completed_at) if analysis.completed_at else None,
        'has_intermediate_data': bool(analysis.intermediate_data),
        'has_analysis_results': bool(analysis.analysis_results),
        'has_mcp_suggestions': bool(analysis.mcp_suggestions)
    }
    
    # Check intermediate data structure
    if analysis.intermediate_data:
        intermediate = analysis.intermediate_data
        data['has_initial_repo_content'] = 'initial_repo_content' in intermediate
        data['has_enhanced_results'] = 'enhanced_results' in intermediate
        data['has_indexing_result'] = 'indexing_result' in intermediate
        
        # Enhanced results details
        if 'enhanced_results' in intermediate:
            enhanced = intermediate['enhanced_results']
            data['enhanced_keys'] = list(enhanced.keys())
            
            # Technology stack details
            if 'technology_stack' in enhanced:
                tech_stack = enhanced['technology_stack']
                data['tech_stack_primary_language'] = tech_stack.get('primary_language', 'Unknown')
                data['tech_stack_languages_count'] = len(tech_stack.get('languages', {}))
                data['tech_stack_has_repo_structure'] = bool(tech_stack.get('repository_structure', {}))
                data['tech_stack_repo_structure_files'] = tech_stack.get('repository_structure', {}).get('total_files', 0)
        
        # Indexing result details
        if 'indexing_result' in intermediate:
            indexing = intermediate['indexing_result']
            data['indexing_status'] = indexing.get('status', 'unknown')
            data['indexing_files'] = indexing.get('files_indexed', 0)
            data['indexing_chunks'] = indexing.get('total_chunks', 0)
    
    print(json.dumps(data, indent=2))
    
finally:
    db.close()
" 2>/dev/null)

    if echo "$ANALYSIS_DATA" | grep -q "ERROR:"; then
        print_error "$(echo "$ANALYSIS_DATA" | grep "ERROR:")"
        exit 1
    fi
    
    # Parse JSON data
    REPO_OWNER=$(echo "$ANALYSIS_DATA" | jq -r '.repo_owner')
    REPO_NAME=$(echo "$ANALYSIS_DATA" | jq -r '.repo_name')
    STATUS=$(echo "$ANALYSIS_DATA" | jq -r '.status')
    FILES_INDEXED=$(echo "$ANALYSIS_DATA" | jq -r '.files_indexed')
    
    print_info "Repository: $REPO_OWNER/$REPO_NAME"
    print_info "Status: $STATUS"
    print_info "Files Indexed (DB): $FILES_INDEXED"
}

# Clone repository and count files
validate_repository_files() {
    print_section "Validating Repository File Counts"
    
    REPO_URL="https://github.com/$REPO_OWNER/$REPO_NAME"
    
    print_info "Cloning repository: $REPO_URL"
    mkdir -p "$TEMP_DIR"
    
    if ! git clone --depth 1 "$REPO_URL" "$TEMP_DIR/repo" >/dev/null 2>&1; then
        print_error "Failed to clone repository"
        return 1
    fi
    
    # Count total files
    TOTAL_FILES=$(find "$TEMP_DIR/repo" -type f ! -path "*/.git/*" | wc -l)
    
    # Count indexable files (same logic as processor)
    INDEXABLE_FILES=$(find "$TEMP_DIR/repo" -type f ! -path "*/.git/*" \
        \( -name "*.py" -o -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" \
        -o -name "*.java" -o -name "*.go" -o -name "*.rs" -o -name "*.cpp" -o -name "*.c" \
        -o -name "*.h" -o -name "*.php" -o -name "*.rb" -o -name "*.swift" -o -name "*.kt" \
        -o -name "*.scala" -o -name "*.sh" -o -name "*.sql" -o -name "*.yaml" -o -name "*.yml" \
        -o -name "*.json" -o -name "*.xml" -o -name "*.html" -o -name "*.css" -o -name "*.md" \
        -o -name "*.txt" -o -name "*.toml" -o -name "*.ini" -o -name "*.cfg" -o -name "*.conf" \) \
        | wc -l)
    
    print_info "Total files in repository: $TOTAL_FILES"
    print_info "Indexable files (supported extensions): $INDEXABLE_FILES"
    print_info "Files indexed in database: $FILES_INDEXED"
    
    # Calculate success rate
    if [ "$INDEXABLE_FILES" -gt 0 ]; then
        SUCCESS_RATE=$(echo "scale=1; $FILES_INDEXED * 100 / $INDEXABLE_FILES" | bc)
        print_info "Indexing success rate: ${SUCCESS_RATE}%"
        
        if (( $(echo "$SUCCESS_RATE >= 90" | bc -l) )); then
            print_success "Excellent indexing coverage (≥90%)"
        elif (( $(echo "$SUCCESS_RATE >= 80" | bc -l) )); then
            print_success "Good indexing coverage (≥80%)"
        else
            print_warning "Low indexing coverage (<80%)"
        fi
    fi
}

# Validate Weaviate vector storage
validate_weaviate_storage() {
    print_section "Validating Weaviate Vector Storage"

    WEAVIATE_DATA=$(docker-compose exec -T backend python -c "
import asyncio
from app.services.weaviate_vector_service import WeaviateVectorService
import json

async def check_weaviate():
    try:
        weaviate_service = WeaviateVectorService()
        await weaviate_service.connect()

        # Get total count
        aggregate_result = weaviate_service.client.query.aggregate('CodeChunk').with_where({
            'path': ['analysis_id'],
            'operator': 'Equal',
            'valueInt': $ANALYSIS_ID
        }).with_meta_count().do()

        total_chunks = aggregate_result.get('data', {}).get('Aggregate', {}).get('CodeChunk', [{}])[0].get('meta', {}).get('count', 0)

        # Get sample chunks to verify real data
        query_result = weaviate_service.client.query.get('CodeChunk', ['file_path', 'language']).with_where({
            'path': ['analysis_id'],
            'operator': 'Equal',
            'valueInt': $ANALYSIS_ID
        }).with_limit(1000).do()

        chunks = query_result.get('data', {}).get('Get', {}).get('CodeChunk', [])

        # Count unique files and languages
        file_paths = set()
        languages = set()

        for chunk in chunks:
            if chunk.get('file_path'):
                file_paths.add(chunk['file_path'])
            if chunk.get('language'):
                languages.add(chunk['language'])

        data = {
            'total_chunks': total_chunks,
            'unique_files': len(file_paths),
            'languages': list(languages),
            'languages_count': len(languages)
        }

        print(json.dumps(data))

    except Exception as e:
        print(f'ERROR: {e}')

asyncio.run(check_weaviate())
" 2>/dev/null)

    if echo "$WEAVIATE_DATA" | grep -q "ERROR:"; then
        print_error "Weaviate validation failed: $(echo "$WEAVIATE_DATA" | grep "ERROR:")"
        return 1
    fi

    TOTAL_CHUNKS=$(echo "$WEAVIATE_DATA" | jq -r '.total_chunks')
    UNIQUE_FILES=$(echo "$WEAVIATE_DATA" | jq -r '.unique_files')
    LANGUAGES_COUNT=$(echo "$WEAVIATE_DATA" | jq -r '.languages_count')
    LANGUAGES=$(echo "$WEAVIATE_DATA" | jq -r '.languages[]' | tr '\n' ', ' | sed 's/,$//')

    print_info "Total chunks in Weaviate: $TOTAL_CHUNKS"
    print_info "Unique files in Weaviate: $UNIQUE_FILES"
    print_info "Languages detected: $LANGUAGES_COUNT ($LANGUAGES)"

    # Validate chunk storage
    if [ "$TOTAL_CHUNKS" -gt 0 ]; then
        print_success "Code chunks are stored in Weaviate"
    else
        print_error "No code chunks found in Weaviate"
        return 1
    fi

    # Validate file indexing
    if [ "$UNIQUE_FILES" -gt 0 ]; then
        print_success "Files are properly indexed with metadata"
    else
        print_error "No file metadata found in Weaviate"
        return 1
    fi

    # Validate language detection
    if [ "$LANGUAGES_COUNT" -gt 0 ]; then
        print_success "Programming languages detected and stored"
    else
        print_warning "No programming languages detected"
    fi
}

# Validate real chunked data from files
validate_real_chunked_data() {
    print_section "Validating Real Chunked Data from Files"

    # Simple validation - just check if chunks exist and have content
    CHUNK_INFO=$(docker-compose exec -T backend python -c "
import asyncio
from app.services.weaviate_vector_service import WeaviateVectorService

async def check_chunks():
    try:
        weaviate_service = WeaviateVectorService()
        await weaviate_service.connect()

        query_result = weaviate_service.client.query.get('CodeChunk', [
            'file_path', 'language', 'content'
        ]).with_where({
            'path': ['analysis_id'],
            'operator': 'Equal',
            'valueInt': $ANALYSIS_ID
        }).with_limit(3).do()

        chunks = query_result.get('data', {}).get('Get', {}).get('CodeChunk', [])

        real_chunks = 0
        for chunk in chunks:
            content = chunk.get('content', '')
            if content and len(content.strip()) > 50:
                real_chunks += 1
                print(f'REAL_CHUNK|{chunk.get(\"file_path\", \"unknown\")}|{chunk.get(\"language\", \"unknown\")}|{len(content)}|{content[:100].replace(\"||\", \"__\")}')

        print(f'TOTAL_REAL_CHUNKS|{real_chunks}')

    except Exception as e:
        print(f'ERROR|{e}')

asyncio.run(check_chunks())
" 2>/dev/null)

    if echo "$CHUNK_INFO" | grep -q "ERROR|"; then
        print_error "Failed to retrieve chunk samples"
        return 1
    fi

    REAL_CHUNK_COUNT=$(echo "$CHUNK_INFO" | grep "TOTAL_REAL_CHUNKS" | cut -d'|' -f2)

    if [ "$REAL_CHUNK_COUNT" -gt 0 ]; then
        print_success "Found $REAL_CHUNK_COUNT real code chunks with substantial content"

        print_info "Sample chunks:"
        echo "$CHUNK_INFO" | grep "REAL_CHUNK" | while IFS='|' read -r prefix file_path language length preview; do
            print_info "  📄 $file_path ($language) - $length chars"
            print_info "     Preview: ${preview}..."
        done

        print_success "All chunks contain real source code (not mock/fallback data)"
    else
        print_error "No real code chunks found - possible indexing failure"
        return 1
    fi
}

# Validate database data persistence
validate_database_persistence() {
    print_section "Validating Database Data Persistence"

    # Check Phase 1: Code Indexing
    HAS_INDEXING_RESULT=$(echo "$ANALYSIS_DATA" | jq -r '.has_indexing_result')
    INDEXING_STATUS=$(echo "$ANALYSIS_DATA" | jq -r '.indexing_status // "unknown"')
    INDEXING_CHUNKS=$(echo "$ANALYSIS_DATA" | jq -r '.indexing_chunks // 0')

    print_info "Phase 1 - Code Indexing:"
    if [ "$HAS_INDEXING_RESULT" = "true" ]; then
        print_success "  Indexing result stored in database"
        print_info "  Status: $INDEXING_STATUS"
        print_info "  Chunks: $INDEXING_CHUNKS"
    else
        print_error "  No indexing result found in database"
    fi

    # Check Phase 2: Enhanced Analysis
    HAS_ENHANCED_RESULTS=$(echo "$ANALYSIS_DATA" | jq -r '.has_enhanced_results')

    print_info "Phase 2 - Enhanced Analysis:"
    if [ "$HAS_ENHANCED_RESULTS" = "true" ]; then
        print_success "  Enhanced analysis results stored in database"
        ENHANCED_KEYS=$(echo "$ANALYSIS_DATA" | jq -r '.enhanced_keys[]' | tr '\n' ', ' | sed 's/,$//')
        print_info "  Components: $ENHANCED_KEYS"
    else
        print_error "  No enhanced analysis results found in database"
    fi

    # Check Phase 3: MCP Analysis
    HAS_MCP_SUGGESTIONS=$(echo "$ANALYSIS_DATA" | jq -r '.has_mcp_suggestions')

    print_info "Phase 3 - MCP Analysis:"
    if [ "$HAS_MCP_SUGGESTIONS" = "true" ]; then
        print_success "  MCP suggestions stored in database"
    else
        print_error "  No MCP suggestions found in database"
    fi
}

# Validate technology stack population
validate_technology_stack() {
    print_section "Validating Technology Stack Population"

    TECH_STACK_PRIMARY=$(echo "$ANALYSIS_DATA" | jq -r '.tech_stack_primary_language // "Unknown"')
    TECH_STACK_LANGUAGES=$(echo "$ANALYSIS_DATA" | jq -r '.tech_stack_languages_count // 0')

    print_info "Primary Language: $TECH_STACK_PRIMARY"
    print_info "Languages Count: $TECH_STACK_LANGUAGES"

    if [ "$TECH_STACK_PRIMARY" != "Unknown" ] && [ "$TECH_STACK_PRIMARY" != "null" ]; then
        print_success "Primary language detected and stored"
    else
        print_error "Primary language not detected"
    fi

    if [ "$TECH_STACK_LANGUAGES" -gt 0 ]; then
        print_success "Multiple languages detected ($TECH_STACK_LANGUAGES languages)"
    else
        print_warning "No languages detected in technology stack"
    fi
}

# Validate repository structure population
validate_repository_structure() {
    print_section "Validating Repository Structure Population"

    HAS_REPO_STRUCTURE=$(echo "$ANALYSIS_DATA" | jq -r '.tech_stack_has_repo_structure')
    REPO_STRUCTURE_FILES=$(echo "$ANALYSIS_DATA" | jq -r '.tech_stack_repo_structure_files // 0')

    print_info "Repository Structure Available: $HAS_REPO_STRUCTURE"
    print_info "Repository Structure Files: $REPO_STRUCTURE_FILES"

    if [ "$HAS_REPO_STRUCTURE" = "true" ]; then
        print_success "Repository structure data available"
        if [ "$REPO_STRUCTURE_FILES" -gt 0 ]; then
            print_success "Repository structure contains $REPO_STRUCTURE_FILES files"
        else
            print_warning "Repository structure exists but shows 0 files"
        fi
    else
        print_error "Repository structure not populated"
    fi
}

# Generate comprehensive summary
generate_summary() {
    print_header "COMPREHENSIVE VALIDATION SUMMARY"

    echo -e "\n${PURPLE}📊 ANALYSIS OVERVIEW${NC}"
    echo -e "Repository: ${CYAN}$REPO_OWNER/$REPO_NAME${NC}"
    echo -e "Analysis ID: ${CYAN}$ANALYSIS_ID${NC}"
    echo -e "Status: ${CYAN}$STATUS${NC}"

    echo -e "\n${PURPLE}📁 FILE PROCESSING${NC}"
    echo -e "Total Repository Files: ${CYAN}$TOTAL_FILES${NC}"
    echo -e "Indexable Files: ${CYAN}$INDEXABLE_FILES${NC}"
    echo -e "Files Indexed: ${CYAN}$FILES_INDEXED${NC}"
    if [ -n "$SUCCESS_RATE" ]; then
        echo -e "Success Rate: ${CYAN}${SUCCESS_RATE}%${NC}"
    fi

    echo -e "\n${PURPLE}🗄️  VECTOR STORAGE${NC}"
    echo -e "Total Chunks: ${CYAN}$TOTAL_CHUNKS${NC}"
    echo -e "Unique Files: ${CYAN}$UNIQUE_FILES${NC}"
    echo -e "Languages: ${CYAN}$LANGUAGES_COUNT${NC}"

    echo -e "\n${PURPLE}🔬 ANALYSIS PHASES${NC}"
    echo -e "Phase 1 (Indexing): $([ "$HAS_INDEXING_RESULT" = "true" ] && echo -e "${GREEN}✅ Complete${NC}" || echo -e "${RED}❌ Failed${NC}")"
    echo -e "Phase 2 (Enhanced): $([ "$HAS_ENHANCED_RESULTS" = "true" ] && echo -e "${GREEN}✅ Complete${NC}" || echo -e "${RED}❌ Failed${NC}")"
    echo -e "Phase 3 (MCP): $([ "$HAS_MCP_SUGGESTIONS" = "true" ] && echo -e "${GREEN}✅ Complete${NC}" || echo -e "${RED}❌ Failed${NC}")"

    echo -e "\n${PURPLE}💻 TECHNOLOGY STACK${NC}"
    echo -e "Primary Language: ${CYAN}$TECH_STACK_PRIMARY${NC}"
    echo -e "Languages Count: ${CYAN}$TECH_STACK_LANGUAGES${NC}"

    echo -e "\n${PURPLE}🏗️  REPOSITORY STRUCTURE${NC}"
    echo -e "Structure Available: $([ "$HAS_REPO_STRUCTURE" = "true" ] && echo -e "${GREEN}✅ Yes${NC}" || echo -e "${RED}❌ No${NC}")"
    echo -e "Structure Files: ${CYAN}$REPO_STRUCTURE_FILES${NC}"

    # Overall validation result
    echo -e "\n${BLUE}============================================================${NC}"

    VALIDATION_PASSED=true

    # Check critical validations
    if [ "$TOTAL_CHUNKS" -eq 0 ] || [ "$UNIQUE_FILES" -eq 0 ]; then
        VALIDATION_PASSED=false
    fi

    if [ "$HAS_INDEXING_RESULT" != "true" ] || [ "$HAS_ENHANCED_RESULTS" != "true" ]; then
        VALIDATION_PASSED=false
    fi

    if [ "$TECH_STACK_PRIMARY" = "Unknown" ] || [ "$TECH_STACK_PRIMARY" = "null" ]; then
        VALIDATION_PASSED=false
    fi

    if [ "$VALIDATION_PASSED" = true ]; then
        echo -e "${GREEN}🎉 VALIDATION PASSED - ALL SYSTEMS WORKING${NC}"
        echo -e "${GREEN}✅ Real data indexing and storage confirmed${NC}"
        echo -e "${GREEN}✅ No fallback or mock data detected${NC}"
        echo -e "${GREEN}✅ All analysis phases completed successfully${NC}"
    else
        echo -e "${RED}❌ VALIDATION FAILED - ISSUES DETECTED${NC}"
        echo -e "${RED}⚠️  Some components are not working properly${NC}"
        echo -e "${YELLOW}📋 Review the detailed output above for specific issues${NC}"
    fi

    echo -e "${BLUE}============================================================${NC}"
}

# Main execution
main() {
    print_header "COMPREHENSIVE ANALYSIS VALIDATION"
    echo -e "${CYAN}Validating Analysis ID: $ANALYSIS_ID${NC}"

    # Run all validations
    check_services
    get_analysis_data
    validate_repository_files
    validate_weaviate_storage
    validate_real_chunked_data
    validate_database_persistence
    validate_technology_stack
    validate_repository_structure

    # Generate summary
    generate_summary
}

# Check dependencies
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ Error: jq is required but not installed${NC}"
    echo "Please install jq: brew install jq (macOS) or apt-get install jq (Ubuntu)"
    exit 1
fi

if ! command -v bc &> /dev/null; then
    echo -e "${RED}❌ Error: bc is required but not installed${NC}"
    echo "Please install bc: brew install bc (macOS) or apt-get install bc (Ubuntu)"
    exit 1
fi

# Show usage if no arguments
if [ $# -eq 0 ]; then
    echo -e "${CYAN}Usage: $0 <analysis_id>${NC}"
    echo -e "${CYAN}Example: $0 1${NC}"
    echo ""
    echo -e "${YELLOW}This script validates all aspects of repository analysis:${NC}"
    echo -e "  • Repository file counts vs indexed files"
    echo -e "  • Code chunks creation and storage in Weaviate"
    echo -e "  • Database data persistence across all phases"
    echo -e "  • Enhanced analysis completion and data"
    echo -e "  • MCP analysis completion and suggestions"
    echo -e "  • Technology stack population"
    echo -e "  • Repository structure population"
    echo ""
    exit 1
fi

# Run main function
main
