'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/hooks/use-auth'
import { Github, Loader2 } from 'lucide-react'

interface LoginButtonProps {
  size?: 'default' | 'sm' | 'lg'
  variant?: 'default' | 'outline' | 'ghost'
  className?: string
  disabled?: boolean
}

export function LoginButton({ size = 'default', variant = 'default', className, disabled = false }: LoginButtonProps) {
  const { login, loading } = useAuth()

  return (
    <Button
      onClick={login}
      disabled={loading || disabled}
      size={size}
      variant={variant}
      className={className}
    >
      {loading ? (
        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      ) : (
        <Github className="w-4 h-4 mr-2" />
      )}
      Sign in with GitHub
    </Button>
  )
}