import {
    ClipboardDocumentIcon,
    CodeBracketIcon,
    CpuChipIcon,
    DocumentTextIcon,
    QuestionMarkCircleIcon,
    ServerIcon,
    SparklesIcon
} from '@heroicons/react/24/outline';

interface MCPCapability {
  capability: string;
  underlying_tech: string;
  exposed_via_api: boolean | string;
  candidate_tool_name: string;
}

interface MCPServerSpec {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

interface ExistingMCPServer {
  server_name: string;
  overlapping_tools: string;
  when_to_reuse: string;
}

interface MCPAnalysisData {
  executive_summary: string;
  capability_matrix: MCPCapability[];
  new_mcp_server_specs: MCPServerSpec[];
  existing_mcp_servers: ExistingMCPServer[];
  gap_analysis: string;
  implementation_starter: string;
  client_config_snippet: string;
}

interface MCPAnalysisResultsProps {
  mcpAnalysis: MCPAnalysisData;
  onAskQuestion?: (question: string) => void;
}

export default function MCPAnalysisResults({ mcpAnalysis, onAskQuestion }: MCPAnalysisResultsProps) {
  // Show loading state if analysis is in progress
  if (!mcpAnalysis || !mcpAnalysis.executive_summary) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8">
        <div className="flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading MCP analysis...</p>
          </div>
        </div>
      </div>
    );
  }

  if (mcpAnalysis.executive_summary.includes('in progress')) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8">
        <div className="flex items-center justify-center">
          <div className="text-center">
            <div className="animate-pulse flex space-x-1 mb-4">
              <div className="h-2 w-2 bg-purple-600 rounded-full animate-bounce"></div>
              <div className="h-2 w-2 bg-purple-600 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="h-2 w-2 bg-purple-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
            <p className="text-gray-600">Analyzing repository for MCP opportunities...</p>
            <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Executive Summary */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center space-x-2 mb-3">
          <SparklesIcon className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold text-gray-900">Executive Summary</h3>
        </div>
        <p className="text-sm text-gray-700 leading-relaxed">{mcpAnalysis.executive_summary}</p>
      </div>

      {/* Capability Matrix */}
      {mcpAnalysis.capability_matrix && mcpAnalysis.capability_matrix.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <CpuChipIcon className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Capability Matrix</h3>
            <span className="text-xs text-gray-500">({mcpAnalysis.capability_matrix.length})</span>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2 px-3 font-medium text-gray-900">Capability</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-900">Underlying Tech</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-900">API/CLI</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-900">MCP Tool Name</th>
                </tr>
              </thead>
              <tbody>
                {mcpAnalysis.capability_matrix.map((capability, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-2 px-3 text-gray-700">{capability.capability}</td>
                    <td className="py-2 px-3 text-gray-600">{capability.underlying_tech}</td>
                    <td className="py-2 px-3">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        (capability.exposed_via_api === true || capability.exposed_via_api === 'true' || capability.exposed_via_api === 'Yes')
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {(capability.exposed_via_api === true || capability.exposed_via_api === 'true' || capability.exposed_via_api === 'Yes') ? '✅ Yes' : '❌ No'}
                      </span>
                    </td>
                    <td className="py-2 px-3 font-mono text-sm text-blue-600">{capability.candidate_tool_name}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* New MCP Server Specs */}
      {mcpAnalysis.new_mcp_server_specs && mcpAnalysis.new_mcp_server_specs.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <ServerIcon className="h-5 w-5 text-green-600" />
            <h3 className="font-semibold text-gray-900">New MCP Server Specs</h3>
            <span className="text-xs text-gray-500">({mcpAnalysis.new_mcp_server_specs.length})</span>
          </div>
          <div className="space-y-4">
            {mcpAnalysis.new_mcp_server_specs.map((spec, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 mb-1">{spec.name}</div>
                    <div className="text-sm text-gray-600">{spec.description}</div>
                  </div>
                  {onAskQuestion && (
                    <button
                      onClick={() => onAskQuestion(`Tell me more about the ${spec.name} MCP server. How would I implement and use it?`)}
                      className="ml-3 inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors duration-200"
                    >
                      <QuestionMarkCircleIcon className="h-3 w-3 mr-1" />
                      Ask a Question
                    </button>
                  )}
                </div>
                {spec.parameters && Object.keys(spec.parameters).length > 0 && (
                  <div className="text-xs">
                    <span className="font-medium text-gray-700">Parameters:</span>
                    <div className="mt-1 space-y-1">
                      {Object.entries(spec.parameters).map(([paramName, paramInfo]: [string, any]) => (
                        <div key={paramName} className="flex items-center space-x-2">
                          <span className="font-mono text-blue-600">{paramName}</span>
                          <span className="text-gray-500">({paramInfo.type})</span>
                          {paramInfo.required && <span className="text-red-500 text-xs">required</span>}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Existing MCP Servers */}
      {mcpAnalysis.existing_mcp_servers && mcpAnalysis.existing_mcp_servers.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <ServerIcon className="h-5 w-5 text-orange-600" />
            <h3 className="font-semibold text-gray-900">Existing MCP Servers to Re-use</h3>
            <span className="text-xs text-gray-500">({mcpAnalysis.existing_mcp_servers.length})</span>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2 px-3 font-medium text-gray-900">Server Name</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-900">Overlapping Tools</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-900">When to Re-use</th>
                </tr>
              </thead>
              <tbody>
                {mcpAnalysis.existing_mcp_servers.map((server, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-2 px-3 font-mono text-sm text-blue-600">{server.server_name}</td>
                    <td className="py-2 px-3 text-gray-700">{server.overlapping_tools}</td>
                    <td className="py-2 px-3 text-gray-600">{server.when_to_reuse}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Gap Analysis */}
      {mcpAnalysis.gap_analysis && !mcpAnalysis.gap_analysis.includes('completed') && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <DocumentTextIcon className="h-5 w-5 text-yellow-600" />
            <h3 className="font-semibold text-gray-900">Gap Analysis</h3>
          </div>
          <div className="text-sm text-gray-700 whitespace-pre-line">{mcpAnalysis.gap_analysis}</div>
        </div>
      )}

      {/* Implementation Starter */}
      {mcpAnalysis.implementation_starter && !mcpAnalysis.implementation_starter.includes('Analysis in progress') && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <CodeBracketIcon className="h-5 w-5 text-indigo-600" />
            <h3 className="font-semibold text-gray-900">Implementation Starter</h3>
          </div>
          <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre className="text-sm text-gray-100 whitespace-pre-wrap">
              <code>{mcpAnalysis.implementation_starter}</code>
            </pre>
          </div>
        </div>
      )}

      {/* Client Config Snippet */}
      {mcpAnalysis.client_config_snippet && !mcpAnalysis.client_config_snippet.includes('{}') && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <ClipboardDocumentIcon className="h-5 w-5 text-gray-600" />
            <h3 className="font-semibold text-gray-900">Client Config Snippet</h3>
          </div>
          <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre className="text-sm text-gray-100 whitespace-pre-wrap">
              <code>{mcpAnalysis.client_config_snippet}</code>
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
