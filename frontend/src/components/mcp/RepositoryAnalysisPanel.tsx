'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
    CheckCircleIcon,
    CodeBracketIcon,
    CpuChipIcon,
    ExclamationTriangleIcon,
    GlobeAltIcon,
    RocketLaunchIcon,
    SparklesIcon
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';

interface Analysis {
  id: number;
  repo_name: string;
  repo_url: string;
  status: string;
  created_at: string;
  updated_at: string;
  analysis_data?: any;
}

interface MCPAnalysis {
  domain: string;
  architecture: {
    architecture_type: string;
    primary_purpose: string;
    core_capabilities: string[];
  };
  custom_mcp_suggestions: Array<{
    name: string;
    description: string;
    capabilities: string[];
    implementation_effort: string;
  }>;
  existing_mcp_recommendations: Array<{
    name: string;
    description: string;
    source: string;
    integration_benefits: string[];
  }>;
}

interface RepositoryAnalysisPanelProps {
  analysis: Analysis;
  currentAnalysis: MCPAnalysis | null;
}

const RepositoryAnalysisPanel: React.FC<RepositoryAnalysisPanelProps> = ({
  analysis,
  currentAnalysis
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    overview: true,
    structure: false,
    capabilities: false,
    mcps: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const analysisData = analysis.analysis_data || {};
  const repoStructure = analysisData.repository_structure || {};
  const mcpIndicators = analysisData.mcp_indicators || {};

  return (
    <div className="h-full flex flex-col">
      <Tabs defaultValue="overview" className="w-full h-full flex flex-col">
        <TabsList className="grid w-full grid-cols-3 flex-shrink-0 h-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
          <TabsTrigger value="mcps">MCPs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-3 flex-1 overflow-y-auto">
          {/* Repository Overview */}
          <Card>
            <CardHeader className="pb-1 pt-2">
              <CardTitle className="text-sm flex items-center">
                <GlobeAltIcon className="h-4 w-4 mr-2" />
                Repository Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 pt-1 pb-2">
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="text-gray-500 text-xs">Name:</span>
                  <p className="font-medium">{analysis.repo_name}</p>
                </div>
                <div>
                  <span className="text-gray-500 text-xs">Status:</span>
                  <Badge variant={analysis.status === 'completed' ? 'default' : 'secondary'} className="text-xs">
                    {analysis.status}
                  </Badge>
                </div>
                <div>
                  <span className="text-gray-500 text-xs">Language:</span>
                  <p className="font-medium">{analysisData.primary_language || 'Unknown'}</p>
                </div>
                <div>
                  <span className="text-gray-500 text-xs">Files:</span>
                  <p className="font-medium">{repoStructure.total_files || 0}</p>
                </div>
              </div>
              
              {currentAnalysis && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-sm text-blue-900 mb-2">AI Analysis Summary</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-blue-700">Domain:</span>
                      <span className="ml-2 font-medium">{currentAnalysis.domain}</span>
                    </div>
                    <div>
                      <span className="text-blue-700">Type:</span>
                      <span className="ml-2 font-medium">{currentAnalysis.architecture.architecture_type}</span>
                    </div>
                    <div>
                      <span className="text-blue-700">Purpose:</span>
                      <p className="text-blue-800 mt-1">{currentAnalysis.architecture.primary_purpose}</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* MCP Indicators */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center">
                <SparklesIcon className="h-4 w-4 mr-2" />
                MCP Opportunities
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center">
                  {mcpIndicators.has_api_routes ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <ExclamationTriangleIcon className="h-4 w-4 text-gray-400 mr-2" />
                  )}
                  <span>API Routes</span>
                </div>
                <div className="flex items-center">
                  {mcpIndicators.has_config_files ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <ExclamationTriangleIcon className="h-4 w-4 text-gray-400 mr-2" />
                  )}
                  <span>Config Files</span>
                </div>
                <div className="flex items-center">
                  {mcpIndicators.has_docker ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <ExclamationTriangleIcon className="h-4 w-4 text-gray-400 mr-2" />
                  )}
                  <span>Docker Support</span>
                </div>
                <div className="flex items-center">
                  {mcpIndicators.has_package_json || mcpIndicators.has_requirements_txt ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <ExclamationTriangleIcon className="h-4 w-4 text-gray-400 mr-2" />
                  )}
                  <span>Dependencies</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>



        <TabsContent value="capabilities" className="space-y-3 flex-1 overflow-y-auto">
          {/* Core Capabilities */}
          <Card>
            <CardHeader className="pb-1 pt-2">
              <CardTitle className="text-sm flex items-center">
                <CpuChipIcon className="h-4 w-4 mr-2" />
                Core Capabilities
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-1 pb-2">
              {currentAnalysis ? (
                <div className="space-y-2">
                  {currentAnalysis.architecture.core_capabilities.map((capability, index) => (
                    <div key={index} className="flex items-center text-sm">
                      <CheckCircleIcon className="h-3 w-3 text-green-500 mr-2" />
                      <span>{capability}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <CheckCircleIcon className="h-3 w-3 text-green-500 mr-2" />
                    <span>Content Management System</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <CheckCircleIcon className="h-3 w-3 text-green-500 mr-2" />
                    <span>API Development Framework</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <CheckCircleIcon className="h-3 w-3 text-green-500 mr-2" />
                    <span>Database Management</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <CheckCircleIcon className="h-3 w-3 text-green-500 mr-2" />
                    <span>User Authentication</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* API Endpoints */}
          {mcpIndicators.api_files && mcpIndicators.api_files.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center">
                  <CodeBracketIcon className="h-4 w-4 mr-2" />
                  API Files
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {mcpIndicators.api_files.slice(0, 5).map((file: string, index: number) => (
                    <div key={index} className="flex items-center text-sm">
                      <CodeBracketIcon className="h-3 w-3 text-blue-500 mr-2" />
                      <span className="font-mono">{file}</span>
                    </div>
                  ))}
                  {mcpIndicators.api_files.length > 5 && (
                    <p className="text-xs text-gray-500">
                      +{mcpIndicators.api_files.length - 5} more API files
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="mcps" className="space-y-3 flex-1 overflow-y-auto">
          {/* Custom MCP Suggestions */}
          <Card>
            <CardHeader className="pb-1 pt-2">
              <CardTitle className="text-sm flex items-center">
                <RocketLaunchIcon className="h-4 w-4 mr-2" />
                Custom MCP Suggestions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 pt-1 pb-2">
              {currentAnalysis && currentAnalysis.custom_mcp_suggestions.length > 0 ? (
                currentAnalysis.custom_mcp_suggestions.map((mcp, index) => (
                  <div key={index} className="border rounded-lg p-3">
                    <h4 className="font-medium text-sm">{mcp.name}</h4>
                    <p className="text-xs text-gray-600 mt-1">{mcp.description}</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="outline" className="text-xs">
                        {mcp.implementation_effort}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {mcp.capabilities.length} capabilities
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="space-y-2">
                  <div className="border rounded-lg p-2">
                    <h4 className="font-medium text-sm">Content Management MCP</h4>
                    <p className="text-xs text-gray-600 mt-1">Automate content creation, updates, and publishing workflows</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="outline" className="text-xs">Medium</Badge>
                      <span className="text-xs text-gray-500">4 capabilities</span>
                    </div>
                  </div>
                  <div className="border rounded-lg p-2">
                    <h4 className="font-medium text-sm">API Integration Hub</h4>
                    <p className="text-xs text-gray-600 mt-1">Create MCPs to connect with external services and APIs</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="outline" className="text-xs">Low</Badge>
                      <span className="text-xs text-gray-500">3 capabilities</span>
                    </div>
                  </div>
                  <div className="border rounded-lg p-2">
                    <h4 className="font-medium text-sm">Development Tools</h4>
                    <p className="text-xs text-gray-600 mt-1">Build custom development and deployment automation tools</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="outline" className="text-xs">High</Badge>
                      <span className="text-xs text-gray-500">5 capabilities</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Existing MCP Recommendations */}
          <Card>
            <CardHeader className="pb-1 pt-2">
              <CardTitle className="text-sm flex items-center">
                <SparklesIcon className="h-4 w-4 mr-2" />
                Marketplace MCPs
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 pt-1 pb-2">
              {currentAnalysis && currentAnalysis.existing_mcp_recommendations.length > 0 ? (
                currentAnalysis.existing_mcp_recommendations.map((mcp, index) => (
                  <div key={index} className="border rounded-lg p-3">
                    <h4 className="font-medium text-sm">{mcp.name}</h4>
                    <p className="text-xs text-gray-600 mt-1">{mcp.description}</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {mcp.source}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {mcp.integration_benefits.length} benefits
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="space-y-2">
                  <div className="border rounded-lg p-2">
                    <h4 className="font-medium text-sm">Database MCP</h4>
                    <p className="text-xs text-gray-600 mt-1">Connect to databases and execute queries safely</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="secondary" className="text-xs">Official</Badge>
                      <span className="text-xs text-gray-500">3 benefits</span>
                    </div>
                  </div>
                  <div className="border rounded-lg p-2">
                    <h4 className="font-medium text-sm">File System MCP</h4>
                    <p className="text-xs text-gray-600 mt-1">Read, write, and manage files and directories</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="secondary" className="text-xs">Community</Badge>
                      <span className="text-xs text-gray-500">4 benefits</span>
                    </div>
                  </div>
                  <div className="border rounded-lg p-2">
                    <h4 className="font-medium text-sm">Web Scraping MCP</h4>
                    <p className="text-xs text-gray-600 mt-1">Extract data from websites and APIs</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="secondary" className="text-xs">Community</Badge>
                      <span className="text-xs text-gray-500">2 benefits</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};



export default RepositoryAnalysisPanel;
