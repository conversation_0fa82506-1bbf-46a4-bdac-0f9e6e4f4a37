'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { healthAPI } from '@/lib/api';
import { AlertTriangle, CheckCircle, ExternalLink, RefreshCw, XCircle } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

interface HealthCheckProps {
  onHealthChange?: (isHealthy: boolean) => void;
  showFullStatus?: boolean;
}

export function HealthCheck({ onHealthChange, showFullStatus = false }: HealthCheckProps) {
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const checkHealth = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const status = await healthAPI.getQuickHealth();
      setHealthStatus(status);
      
      // Notify parent component about health status
      if (onHealthChange) {
        onHealthChange(status.status === 'healthy');
      }
    } catch (err: any) {
      console.error('Health check failed:', err);
      setError('Unable to connect to backend services');
      setHealthStatus({ status: 'unhealthy', message: 'Backend unreachable' });
      
      if (onHealthChange) {
        onHealthChange(false);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkHealth();
    // Check health every 30 seconds
    const interval = setInterval(checkHealth, 30000);
    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading && !healthStatus) {
    return (
      <Alert>
        <RefreshCw className="h-4 w-4 animate-spin" />
        <AlertDescription>
          Checking system health...
        </AlertDescription>
      </Alert>
    );
  }

  if (error || healthStatus?.status === 'unhealthy') {
    return (
      <Alert variant="destructive">
        <XCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <div>
            <div className="font-semibold">System Unavailable</div>
            <div className="text-sm">
              {error || healthStatus?.message || 'Critical services are not responding'}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={checkHealth}
              disabled={loading}
            >
              <RefreshCw className={`h-3 w-3 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Retry
            </Button>
            <Link href="/health">
              <Button variant="outline" size="sm">
                <ExternalLink className="h-3 w-3 mr-1" />
                Details
              </Button>
            </Link>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (healthStatus?.status === 'healthy') {
    return showFullStatus ? (
      <Alert>
        <CheckCircle className="h-4 w-4 text-green-500" />
        <AlertDescription className="flex items-center justify-between">
          <div>
            <div className="font-semibold text-green-700">All Systems Operational</div>
            <div className="text-sm text-green-600">
              Core services are running normally
            </div>
          </div>
          <Link href="/health">
            <Button variant="outline" size="sm">
              <ExternalLink className="h-3 w-3 mr-1" />
              View Details
            </Button>
          </Link>
        </AlertDescription>
      </Alert>
    ) : null; // Don't show anything if healthy and not showing full status
  }

  // Degraded state
  return (
    <Alert variant="default" className="border-yellow-200 bg-yellow-50">
      <AlertTriangle className="h-4 w-4 text-yellow-600" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <div className="font-semibold text-yellow-800">System Degraded</div>
          <div className="text-sm text-yellow-700">
            Some services may be experiencing issues
          </div>
        </div>
        <Link href="/health">
          <Button variant="outline" size="sm">
            <ExternalLink className="h-3 w-3 mr-1" />
            Check Status
          </Button>
        </Link>
      </AlertDescription>
    </Alert>
  );
}

// Simplified health indicator for navigation/header
export function HealthIndicator() {
  const [isHealthy, setIsHealthy] = useState<boolean | null>(null);

  useEffect(() => {
    const checkHealth = async () => {
      try {
        const status = await healthAPI.getQuickHealth();
        setIsHealthy(status.status === 'healthy');
      } catch {
        setIsHealthy(false);
      }
    };

    checkHealth();
    const interval = setInterval(checkHealth, 60000); // Check every minute
    return () => clearInterval(interval);
  }, []);

  if (isHealthy === null) {
    return (
      <div className="flex items-center space-x-1 text-xs text-muted-foreground">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
        <span>Checking...</span>
      </div>
    );
  }

  return (
    <Link href="/health" className="flex items-center space-x-1 text-xs hover:underline">
      <div className={`w-2 h-2 rounded-full ${
        isHealthy ? 'bg-green-500' : 'bg-red-500'
      }`}></div>
      <span className={isHealthy ? 'text-green-600' : 'text-red-600'}>
        {isHealthy ? 'Healthy' : 'Issues'}
      </span>
    </Link>
  );
}
