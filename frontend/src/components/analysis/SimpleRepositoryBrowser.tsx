'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import apiClient from '@/lib/api';
import React, { useCallback, useEffect, useState } from 'react';

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
  language: string | null;
  stars: number;
  updated_at: string;
  html_url: string;
}

interface SimpleRepositoryBrowserProps {
  onRepositorySelect: (repoUrl: string) => void;
  className?: string;
}

export const SimpleRepositoryBrowser: React.FC<SimpleRepositoryBrowserProps> = ({
  onRepositorySelect,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const fetchRepositories = useCallback(async () => {
    if (!isExpanded) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.getUserRepositories({
        page: 1,
        per_page: 30,
        type_filter: 'all',
        sort: 'updated'
      });
      
      setRepositories(response.repositories);
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to fetch repositories';

      // Handle specific error cases
      if (err.response?.status === 401 || errorMessage.includes('Not authenticated')) {
        setError('Please log in with GitHub to browse your repositories');
      } else if (err.response?.status === 400 && errorMessage.includes('GitHub token not found')) {
        setError('GitHub authentication expired. Please log in again');
      } else {
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  }, [isExpanded]);

  useEffect(() => {
    fetchRepositories();
  }, [fetchRepositories]);

  const filteredRepositories = repositories.filter(repo =>
    repo.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (repo.description && repo.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleRepositoryClick = (repo: Repository) => {
    onRepositorySelect(repo.html_url);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <Card className={`border border-gray-200 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              📁 Browse Your Repositories
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              Select a repository from your GitHub account
            </p>
          </div>
          <div className="flex items-center gap-2">
            {isExpanded && (
              <Button
                variant="ghost"
                size="sm"
                onClick={fetchRepositories}
                disabled={loading}
                className="flex items-center gap-1"
              >
                <span className={loading ? 'animate-spin' : ''}>🔄</span>
                <span>Refresh</span>
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center gap-2"
            >
              {isExpanded ? (
                <>
                  <span>▲</span>
                  <span>Hide</span>
                </>
              ) : (
                <>
                  <span>▼</span>
                  <span>Browse</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          {/* Search and Summary */}
          <div className="mb-4 space-y-3">
            <Input
              placeholder="Search repositories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />

            {repositories.length > 0 && (
              <div className="flex items-center justify-between text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md">
                <span>
                  Showing {filteredRepositories.length} of {repositories.length} repositories
                </span>
                <span className="text-xs">
                  Click a repository to select it
                </span>
              </div>
            )}
          </div>

          {/* Repository List */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading repositories...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8 bg-red-50 rounded-lg border border-red-200">
              <div className="text-red-600 mb-3">
                <span className="text-2xl">⚠️</span>
                <p className="mt-2 font-medium">Failed to load repositories</p>
                <p className="text-sm text-red-500 mt-1">{error}</p>
              </div>
              <div className="flex gap-2 justify-center">
                {error.includes('log in') || error.includes('authentication') ? (
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/auth/login'}
                    size="sm"
                    className="border-blue-300 text-blue-700 hover:bg-blue-100"
                  >
                    Log in with GitHub
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    onClick={fetchRepositories}
                    size="sm"
                    className="border-red-300 text-red-700 hover:bg-red-100"
                  >
                    Try Again
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredRepositories.map((repo) => (
                <div
                  key={repo.id}
                  onClick={() => handleRepositoryClick(repo)}
                  className="p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 cursor-pointer transition-all duration-200 hover:shadow-sm"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm">
                          {repo.private ? '🔒' : '🌍'}
                        </span>
                        <span className="font-medium text-gray-900 truncate">
                          {repo.full_name}
                        </span>
                        {repo.language && (
                          <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {repo.language}
                          </span>
                        )}
                      </div>
                      
                      {repo.description && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {repo.description}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>⭐ {repo.stars}</span>
                        <span>Updated {formatDate(repo.updated_at)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {filteredRepositories.length === 0 && !loading && (
                <div className="text-center py-8 text-gray-500">
                  No repositories found matching your criteria.
                </div>
              )}
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default SimpleRepositoryBrowser;
