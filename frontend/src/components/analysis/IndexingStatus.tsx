'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import apiClient, { IndexingStatus as IndexingStatusType } from '@/lib/api';
import {
    ArrowPathIcon,
    CheckCircleIcon,
    CircleStackIcon,
    ClockIcon,
    CubeIcon,
    DocumentTextIcon,
    ExclamationTriangleIcon,
    XCircleIcon
} from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';

interface IndexingStatusProps {
  analysisId: number;
  analysisStatus: string;
  autoRefresh?: boolean;
  onStatusChange?: (status: IndexingStatusType) => void;
}

export default function IndexingStatus({ 
  analysisId, 
  analysisStatus, 
  autoRefresh = true,
  onStatusChange 
}: IndexingStatusProps) {
  const [indexingStatus, setIndexingStatus] = useState<IndexingStatusType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchIndexingStatus();
  }, [analysisId]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    let interval: NodeJS.Timeout;

    // Poll during analysis (when indexing might start) or during active indexing
    const shouldPoll = autoRefresh && (
      analysisStatus === 'analyzing' ||
      analysisStatus === 'pending' ||
      indexingStatus?.indexing_status === 'indexing' ||
      indexingStatus?.indexing_status === 'not_started'
    );

    if (shouldPoll) {
      interval = setInterval(() => {
        fetchIndexingStatus();
      }, 2000); // Refresh every 2 seconds during analysis/indexing
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [indexingStatus?.indexing_status, analysisStatus, autoRefresh]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchIndexingStatus = async () => {
    try {
      const status = await apiClient.getIndexingStatus(analysisId);
      setIndexingStatus(status);
      setError(null);
      
      if (onStatusChange) {
        onStatusChange(status);
      }
    } catch (err: any) {
      console.error('Failed to fetch indexing status:', err);

      // Provide better error messages for different error types
      if (err?.code === 'ECONNABORTED' || err?.message?.includes('timeout')) {
        setError('Request timed out - large repository analysis may still be in progress');
      } else if (err?.response?.status === 404) {
        setError('Analysis not found');
      } else if (err?.response?.status >= 500) {
        setError('Server error - please try again later');
      } else {
        setError('Failed to load indexing status');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleReindex = async () => {
    try {
      await apiClient.reindexRepository(analysisId);
      // Refresh status after triggering reindex
      setTimeout(() => fetchIndexingStatus(), 1000);
    } catch (err) {
      console.error('Failed to trigger reindex:', err);
      setError('Failed to start reindexing');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'indexing':
        return <ClockIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'not_started':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <CircleStackIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'success',
      failed: 'destructive',
      indexing: 'default',
      not_started: 'secondary'
    } as const;

    const labels = {
      completed: 'Indexed',
      failed: 'Failed',
      indexing: 'Indexing',
      not_started: 'Not Started'
    };

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <Card className="border-gray-200">
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-16">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !indexingStatus) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center">
            <XCircleIcon className="h-5 w-5 text-red-400 mr-2" />
            <span className="text-sm text-red-700">{error || 'Indexing status unavailable'}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show indexing status during analysis or when indexing has started
  if (analysisStatus === 'pending' && indexingStatus.indexing_status === 'not_started') {
    return (
      <Card className="border-0 bg-gradient-to-br from-white via-white to-blue-50/30 shadow-lg shadow-blue-100/50">
        <CardContent className="pt-6">
          <div className="flex items-center space-x-3">
            <ClockIcon className="h-5 w-5 text-blue-500 animate-pulse" />
            <div>
              <div className="font-medium text-gray-900">Waiting for Analysis</div>
              <div className="text-sm text-gray-600">Code indexing will begin once analysis starts</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 bg-gradient-to-br from-white via-white to-purple-50/30 shadow-lg shadow-purple-100/50">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold text-gray-900 flex items-center">
          <CircleStackIcon className="mr-3 h-6 w-6 text-purple-600" />
          Code Indexing Status
        </CardTitle>
        <CardDescription className="text-gray-600">
          Vector database indexing for enhanced MCP suggestions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Status Overview */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getStatusIcon(indexingStatus.indexing_status)}
              <div>
                <div className="font-medium text-gray-900">
                  {getStatusBadge(indexingStatus.indexing_status)}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Last indexed: {formatDate(indexingStatus.last_indexed_at)}
                </div>
              </div>
            </div>
            
            {indexingStatus.indexing_status === 'completed' && analysisStatus === 'completed' && (
              <Button 
                size="sm" 
                variant="outline" 
                onClick={handleReindex}
                className="border-purple-200 text-purple-600 hover:bg-purple-50"
              >
                <ArrowPathIcon className="mr-2 h-4 w-4" />
                Reindex
              </Button>
            )}
          </div>

          {/* Progress Bar for Active Indexing */}
          {indexingStatus.indexing_status === 'indexing' && (
            <div className="space-y-3">
              <Progress value={indexingStatus.indexing_progress} className="w-full" />
              <div className="flex justify-between text-sm text-gray-600">
                <span>Progress: {indexingStatus.indexing_progress}%</span>
                <span>Indexing repository code...</span>
              </div>
            </div>
          )}

          {/* Statistics */}
          {(indexingStatus.files_indexed !== undefined || indexingStatus.total_chunks !== undefined) && (
            <div className="grid grid-cols-2 gap-4">
              {indexingStatus.files_indexed !== undefined && (
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-white border border-gray-100">
                  <DocumentTextIcon className="h-5 w-5 text-blue-500" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Files Indexed</div>
                    <div className="text-lg font-bold text-blue-600">{indexingStatus.files_indexed}</div>
                  </div>
                </div>
              )}
              
              {indexingStatus.total_chunks !== undefined && (
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-white border border-gray-100">
                  <CubeIcon className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Code Chunks</div>
                    <div className="text-lg font-bold text-green-600">{indexingStatus.total_chunks}</div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Vector DB Info */}
          {indexingStatus.vector_db_id && (
            <div className="p-3 rounded-lg bg-purple-50 border border-purple-100">
              <div className="text-sm font-medium text-purple-800">Vector Database ID</div>
              <div className="text-xs text-purple-600 font-mono mt-1">{indexingStatus.vector_db_id}</div>
            </div>
          )}

          {/* Error Message */}
          {indexingStatus.error_message && (
            <div className="p-3 rounded-lg bg-red-50 border border-red-200">
              <div className="text-sm font-medium text-red-800">Indexing Error</div>
              <div className="text-sm text-red-700 mt-1">{indexingStatus.error_message}</div>
            </div>
          )}

          {/* Info Message */}
          {indexingStatus.indexing_status === 'completed' && (
            <div className="p-3 rounded-lg bg-green-50 border border-green-200">
              <div className="text-sm text-green-800">
                ✅ Code successfully indexed for enhanced MCP suggestions and semantic search
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
