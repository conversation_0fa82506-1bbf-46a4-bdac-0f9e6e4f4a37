'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import apiClient from '@/lib/api';
import {
    CalendarIcon,
    ChevronDownIcon,
    ChevronUpIcon,
    CodeBracketIcon,
    FunnelIcon,
    GlobeAltIcon,
    LockClosedIcon,
    MagnifyingGlassIcon,
    StarIcon
} from '@heroicons/react/24/outline';
import React, { useEffect, useState } from 'react';

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
  language: string | null;
  stars: number;
  forks: number;
  updated_at: string;
  created_at: string;
  pushed_at: string;
  html_url: string;
  clone_url: string;
  size: number;
}

interface RepositoryBrowserProps {
  onRepositorySelect: (repoUrl: string) => void;
  className?: string;
}

export const RepositoryBrowser: React.FC<RepositoryBrowserProps> = ({
  onRepositorySelect,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'public' | 'private'>('all');
  const [sortBy, setSortBy] = useState<'updated' | 'created' | 'pushed' | 'full_name'>('updated');
  const [summary, setSummary] = useState<{
    total_repos: number;
    private_repos: number;
    public_repos: number;
  } | null>(null);

  const fetchRepositories = useCallback(async () => {
    if (!isExpanded) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.getUserRepositories({
        page: 1,
        per_page: 50,
        type_filter: typeFilter,
        sort: sortBy
      });

      setRepositories(response.repositories);
      setSummary(response.summary);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch repositories');
    } finally {
      setLoading(false);
    }
  }, [isExpanded, typeFilter, sortBy]);

  useEffect(() => {
    fetchRepositories();
  }, [fetchRepositories]);

  const filteredRepositories = repositories.filter(repo =>
    repo.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (repo.description && repo.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleRepositoryClick = (repo: Repository) => {
    onRepositorySelect(repo.html_url);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getLanguageColor = (language: string | null) => {
    const colors: { [key: string]: string } = {
      'JavaScript': 'bg-yellow-500',
      'TypeScript': 'bg-blue-500',
      'Python': 'bg-green-500',
      'Java': 'bg-orange-500',
      'Go': 'bg-cyan-500',
      'Rust': 'bg-orange-600',
      'C++': 'bg-pink-500',
      'C#': 'bg-purple-500',
      'PHP': 'bg-indigo-500',
      'Ruby': 'bg-red-500',
    };
    return colors[language || ''] || 'bg-gray-500';
  };

  return (
    <Card className={`border border-gray-200 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center">
            <CodeBracketIcon className="mr-2 h-5 w-5" />
            Browse Your Repositories
            {summary && (
              <Badge variant="outline" className="ml-3">
                {summary.total_repos} repos
              </Badge>
            )}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center"
          >
            {isExpanded ? (
              <>
                <ChevronUpIcon className="h-4 w-4 mr-1" />
                Hide
              </>
            ) : (
              <>
                <ChevronDownIcon className="h-4 w-4 mr-1" />
                Browse
              </>
            )}
          </Button>
        </div>
        
        {summary && !isExpanded && (
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center">
              <LockClosedIcon className="h-4 w-4 mr-1" />
              {summary.private_repos} private
            </div>
            <div className="flex items-center">
              <GlobeAltIcon className="h-4 w-4 mr-1" />
              {summary.public_repos} public
            </div>
          </div>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search repositories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={typeFilter} onValueChange={(value: any) => setTypeFilter(value)}>
              <SelectTrigger>
                <FunnelIcon className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Repositories</SelectItem>
                <SelectItem value="public">Public Only</SelectItem>
                <SelectItem value="private">Private Only</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger>
                <CalendarIcon className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="updated">Recently Updated</SelectItem>
                <SelectItem value="created">Recently Created</SelectItem>
                <SelectItem value="pushed">Recently Pushed</SelectItem>
                <SelectItem value="full_name">Name (A-Z)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Repository List */}
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-600">{error}</p>
              <Button variant="outline" onClick={fetchRepositories} className="mt-2">
                Try Again
              </Button>
            </div>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredRepositories.map((repo) => (
                <div
                  key={repo.id}
                  onClick={() => handleRepositoryClick(repo)}
                  className="p-3 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        {repo.private ? (
                          <LockClosedIcon className="h-4 w-4 text-orange-500 flex-shrink-0" />
                        ) : (
                          <GlobeAltIcon className="h-4 w-4 text-green-500 flex-shrink-0" />
                        )}
                        <span className="font-medium text-gray-900 truncate">
                          {repo.full_name}
                        </span>
                        {repo.language && (
                          <Badge variant="outline" className="text-xs">
                            <div className={`w-2 h-2 rounded-full mr-1 ${getLanguageColor(repo.language)}`} />
                            {repo.language}
                          </Badge>
                        )}
                      </div>
                      
                      {repo.description && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {repo.description}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center">
                          <StarIcon className="h-3 w-3 mr-1" />
                          {repo.stars}
                        </div>
                        <span>Updated {formatDate(repo.updated_at)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {filteredRepositories.length === 0 && !loading && (
                <div className="text-center py-8 text-gray-500">
                  No repositories found matching your criteria.
                </div>
              )}
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default RepositoryBrowser;
