'use client';

import { cn } from '@/lib/utils';
import {
    CheckCircleIcon,
    ChevronDownIcon,
    ChevronRightIcon,
    CodeBracketIcon,
    CogIcon,
    CommandLineIcon,
    DocumentTextIcon,
    ExclamationTriangleIcon,
    FolderIcon,
    FolderOpenIcon
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import { Badge } from './badge';

interface TreeNode {
  name: string;
  path: string;
  type: 'file' | 'dir';
  size?: number;
  url?: string;
  children?: TreeNode[];
  total_files?: number;
}

interface RepositoryTreeProps {
  tree: TreeNode;
  indicators?: {
    has_api_routes: boolean;
    has_config_files: boolean;
    has_package_json: boolean;
    has_requirements_txt: boolean;
    has_docker: boolean;
    has_readme: boolean;
    api_files: string[];
    config_files: string[];
    key_files: string[];
  };
  className?: string;
}

const getFileIcon = (fileName: string, isOpen?: boolean) => {
  if (!fileName) {
    return <DocumentTextIcon className="h-4 w-4 text-gray-400" />;
  }
  
  const name = fileName.toLowerCase();
  const ext = name.split('.').pop();
  
  // Directory icons
  if (isOpen !== undefined) {
    return isOpen ? (
      <FolderOpenIcon className="h-4 w-4 text-blue-500" />
    ) : (
      <FolderIcon className="h-4 w-4 text-blue-600" />
    );
  }
  
  // Configuration files
  if (['package.json', 'composer.json', 'pom.xml', 'cargo.toml', 'requirements.txt'].includes(name)) {
    return <CogIcon className="h-4 w-4 text-orange-500" />;
  }
  
  // Docker files
  if (name.includes('dockerfile') || name.includes('docker-compose')) {
    return <CommandLineIcon className="h-4 w-4 text-blue-500" />;
  }
  
  // Code files by extension
  switch (ext) {
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
      return <CodeBracketIcon className="h-4 w-4 text-yellow-500" />;
    case 'py':
      return <CodeBracketIcon className="h-4 w-4 text-green-500" />;
    case 'json':
      return <CogIcon className="h-4 w-4 text-orange-500" />;
    case 'md':
    case 'txt':
      return <DocumentTextIcon className="h-4 w-4 text-gray-500" />;
    default:
      return <DocumentTextIcon className="h-4 w-4 text-gray-400" />;
  }
};

const getFileBadge = (fileName: string, indicators?: any) => {
  if (!fileName || !indicators) return null;
  
  const name = fileName.toLowerCase();
  const path = fileName.toLowerCase();
  
  // Check if this file is flagged as important
  if (indicators.api_files?.some((file: string) => file.includes(path))) {
    return <Badge variant="default" className="text-xs">API</Badge>;
  }
  
  if (indicators.config_files?.some((file: string) => file.includes(path))) {
    return <Badge variant="secondary" className="text-xs">Config</Badge>;
  }
  
  if (indicators.key_files?.some((file: string) => file.includes(path))) {
    return <Badge variant="outline" className="text-xs">Key</Badge>;
  }
  
  return null;
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

interface TreeNodeProps {
  node: TreeNode;
  level: number;
  indicators?: any;
  isLast?: boolean;
  prefix?: string;
}

const TreeNodeComponent: React.FC<TreeNodeProps> = ({ 
  node, 
  level, 
  indicators, 
  isLast = false, 
  prefix = '' 
}) => {
  const [isExpanded, setIsExpanded] = useState(level < 2); // Auto-expand first 2 levels
  const hasChildren = node.children && node.children.length > 0;
  
  const toggleExpanded = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
  };

  const currentPrefix = level === 0 ? '' : prefix + (isLast ? '└── ' : '├── ');
  const childPrefix = level === 0 ? '' : prefix + (isLast ? '    ' : '│   ');

  return (
    <div className="font-mono text-sm">
      <div 
        className={cn(
          "flex items-center gap-2 py-1 hover:bg-gray-50 rounded-sm cursor-pointer",
          level === 0 && "font-semibold"
        )}
        onClick={toggleExpanded}
        style={{ paddingLeft: `${level * 12}px` }}
      >
        {/* Tree lines and node structure */}
        {level > 0 && (
          <span className="text-gray-400 select-none">
            {currentPrefix}
          </span>
        )}
        
        {/* Expand/collapse icon for directories */}
        {hasChildren && (
          <div className="flex-shrink-0">
            {isExpanded ? (
              <ChevronDownIcon className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRightIcon className="h-4 w-4 text-gray-500" />
            )}
          </div>
        )}
        
        {/* File/folder icon */}
        <div className="flex-shrink-0">
          {getFileIcon(node.name || 'unknown', hasChildren ? isExpanded : undefined)}
        </div>
        
        {/* File/folder name */}
        <span className={cn(
          "flex-grow truncate",
          node.type === 'dir' ? "text-blue-700 font-medium" : "text-gray-700"
        )}>
          {node.name || 'Unknown'}
        </span>
        
        {/* Badges for important files */}
        <div className="flex-shrink-0">
          {getFileBadge(node.path || node.name || '', indicators)}
        </div>
        
        {/* File size */}
        {node.type === 'file' && node.size && (
          <span className="text-xs text-gray-500 flex-shrink-0">
            {formatFileSize(node.size)}
          </span>
        )}
      </div>
      
      {/* Render children if expanded */}
      {hasChildren && isExpanded && (
        <div>
          {node.children!.filter(child => child && typeof child === 'object').map((child, index) => (
            <TreeNodeComponent
              key={child.path || child.name || `node-${index}`}
              node={child}
              level={level + 1}
              indicators={indicators}
              isLast={index === node.children!.length - 1}
              prefix={childPrefix}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const MCPIndicators: React.FC<{ indicators: any }> = ({ indicators }) => {
  const indicatorItems = [
    { key: 'has_api_routes', label: 'API Routes', icon: CheckCircleIcon, color: 'text-green-500' },
    { key: 'has_config_files', label: 'Config Files', icon: CogIcon, color: 'text-blue-500' },
    { key: 'has_docker', label: 'Docker', icon: CommandLineIcon, color: 'text-blue-600' },
    { key: 'has_readme', label: 'Documentation', icon: DocumentTextIcon, color: 'text-gray-600' },
    { key: 'has_package_json', label: 'Node.js', icon: CheckCircleIcon, color: 'text-yellow-500' },
    { key: 'has_requirements_txt', label: 'Python', icon: CheckCircleIcon, color: 'text-green-600' },
  ];

  const activeIndicators = indicatorItems.filter(item => indicators[item.key]);
  const inactiveIndicators = indicatorItems.filter(item => !indicators[item.key]);

  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-900">MCP Potential Indicators</h4>
      
      {/* Active indicators */}
      <div className="grid grid-cols-2 gap-2">
        {activeIndicators.map(item => {
          const Icon = item.icon;
          return (
            <div key={item.key} className="flex items-center gap-2">
              <Icon className={cn("h-4 w-4", item.color)} />
              <span className="text-sm text-gray-700">{item.label}</span>
            </div>
          );
        })}
      </div>
      
      {/* Missing indicators */}
      {inactiveIndicators.length > 0 && (
        <div className="pt-2 border-t">
          <h5 className="text-xs font-medium text-gray-500 mb-2">Missing:</h5>
          <div className="grid grid-cols-2 gap-2">
            {inactiveIndicators.map(item => {
              const Icon = ExclamationTriangleIcon;
              return (
                <div key={item.key} className="flex items-center gap-2 opacity-50">
                  <Icon className="h-4 w-4 text-gray-400" />
                  <span className="text-xs text-gray-500">{item.label}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export const RepositoryTree: React.FC<RepositoryTreeProps> = ({ 
  tree, 
  indicators, 
  className 
}) => {
  // Defensive check for malformed or empty tree data
  if (!tree || typeof tree !== 'object' || (Array.isArray(tree) && tree.length === 0)) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="border rounded-lg p-4 bg-white">
          <div className="text-center py-8">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Repository tree unavailable</h3>
            <p className="mt-1 text-sm text-gray-500">
              Repository structure data could not be loaded or is empty.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* MCP Indicators */}
      {indicators && (
        <div className="p-4 bg-gray-50 rounded-lg">
          <MCPIndicators indicators={indicators} />
        </div>
      )}
      
      {/* Repository Tree */}
      <div className="border rounded-lg p-4 bg-white">
        <div className="flex items-center gap-2 mb-3 pb-2 border-b">
          <FolderIcon className="h-5 w-5 text-blue-600" />
          <h3 className="font-semibold text-gray-900">Repository Structure</h3>
          {tree.total_files && (
            <Badge variant="outline" className="text-xs">
              {tree.total_files} files
            </Badge>
          )}
        </div>
        
        <div className="max-h-96 overflow-y-auto">
          <TreeNodeComponent 
            node={tree} 
            level={0} 
            indicators={indicators}
          />
        </div>
      </div>
    </div>
  );
};

export default RepositoryTree;