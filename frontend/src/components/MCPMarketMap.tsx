import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Badge
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  GitHub as GitHubIcon,
  Star as StarIcon,
  Launch as LaunchIcon,
  Code as CodeIcon
} from '@mui/icons-material';
import { api } from '../services/api';

interface MCPServer {
  id: number;
  name: string;
  url: string;
  description: string;
  category: string;
  github_url?: string;
  npm_url?: string;
  stars?: number;
  language?: string;
  confidence_score: number;
  last_updated?: string;
}

interface MCPCategory {
  id: number;
  name: string;
  description: string;
  icon: string;
  color: string;
  server_count: number;
}

interface MCPStats {
  total_servers: number;
  category_distribution: Array<{ category: string; count: number }>;
  language_distribution: Array<{ language: string; count: number }>;
  top_servers: Array<{
    name: string;
    url: string;
    stars: number;
    category: string;
    language: string;
  }>;
}

const MCPMarketMap: React.FC = () => {
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [categories, setCategories] = useState<MCPCategory[]>([]);
  const [stats, setStats] = useState<MCPStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [discovering, setDiscovering] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [serversResponse, categoriesResponse, statsResponse] = await Promise.all([
        api.get('/mcp-discovery/servers'),
        api.get('/mcp-discovery/categories'),
        api.get('/mcp-discovery/stats')
      ]);

      setServers(serversResponse.data.servers);
      setCategories(categoriesResponse.data.categories);
      setStats(statsResponse.data);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load MCP data');
    } finally {
      setLoading(false);
    }
  };

  const triggerDiscovery = async () => {
    try {
      setDiscovering(true);
      setError(null);

      await api.post('/mcp-discovery/discover');
      
      // Wait a bit then reload data
      setTimeout(() => {
        loadData();
        setDiscovering(false);
      }, 3000);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to trigger discovery');
      setDiscovering(false);
    }
  };

  const searchServers = async () => {
    if (!searchQuery.trim()) {
      loadData();
      return;
    }

    try {
      setLoading(true);
      const response = await api.get('/mcp-discovery/search', {
        params: {
          q: searchQuery,
          category: selectedCategory || undefined,
          language: selectedLanguage || undefined
        }
      });
      setServers(response.data.servers);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Search failed');
    } finally {
      setLoading(false);
    }
  };

  const filterServers = async () => {
    try {
      setLoading(true);
      const response = await api.get('/mcp-discovery/servers', {
        params: {
          category: selectedCategory || undefined,
          language: selectedLanguage || undefined,
          min_confidence: 0.5
        }
      });
      setServers(response.data.servers);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Filter failed');
    } finally {
      setLoading(false);
    }
  };

  const getCategoryColor = (categoryName: string) => {
    const category = categories.find(c => c.name === categoryName);
    return category?.color || '#6B7280';
  };

  const getLanguageIcon = (language: string) => {
    const icons: { [key: string]: string } = {
      javascript: '🟨',
      typescript: '🔷',
      python: '🐍',
      go: '🐹',
      rust: '🦀',
      java: '☕'
    };
    return icons[language?.toLowerCase()] || '💻';
  };

  if (loading && servers.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ color: '#7C3AED', fontWeight: 'bold' }}>
          🗺️ MCP Market Map
        </Typography>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          Discover and explore active Model Context Protocol servers from the ecosystem
        </Typography>
        
        {stats && (
          <Box sx={{ mt: 2, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Chip 
              label={`${stats.total_servers} Total Servers`} 
              color="primary" 
              variant="outlined" 
            />
            <Chip 
              label={`${categories.length} Categories`} 
              color="secondary" 
              variant="outlined" 
            />
            <Button
              variant="contained"
              startIcon={discovering ? <CircularProgress size={16} /> : <RefreshIcon />}
              onClick={triggerDiscovery}
              disabled={discovering}
              sx={{ ml: 'auto' }}
            >
              {discovering ? 'Discovering...' : 'Discover New Servers'}
            </Button>
          </Box>
        )}
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search MCP servers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && searchServers()}
                InputProps={{
                  endAdornment: (
                    <IconButton onClick={searchServers}>
                      <SearchIcon />
                    </IconButton>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  label="Category"
                >
                  <MenuItem value="">All Categories</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.name}>
                      {category.icon} {category.name} ({category.server_count})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Language</InputLabel>
                <Select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  label="Language"
                >
                  <MenuItem value="">All Languages</MenuItem>
                  <MenuItem value="javascript">JavaScript</MenuItem>
                  <MenuItem value="typescript">TypeScript</MenuItem>
                  <MenuItem value="python">Python</MenuItem>
                  <MenuItem value="go">Go</MenuItem>
                  <MenuItem value="rust">Rust</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={filterServers}
                disabled={loading}
              >
                Filter
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* MCP Servers Grid */}
      <Grid container spacing={3}>
        {servers.map((server) => (
          <Grid item xs={12} md={6} lg={4} key={server.id}>
            <Card 
              sx={{ 
                height: '100%',
                border: `2px solid ${getCategoryColor(server.category)}20`,
                '&:hover': {
                  border: `2px solid ${getCategoryColor(server.category)}`,
                  transform: 'translateY(-2px)',
                  transition: 'all 0.2s ease-in-out'
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" component="h3" sx={{ fontWeight: 'bold' }}>
                    {server.name}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {server.github_url && (
                      <Tooltip title="View on GitHub">
                        <IconButton 
                          size="small" 
                          onClick={() => window.open(server.github_url, '_blank')}
                        >
                          <GitHubIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="Open Server">
                      <IconButton 
                        size="small" 
                        onClick={() => window.open(server.url, '_blank')}
                      >
                        <LaunchIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2, minHeight: '40px' }}>
                  {server.description}
                </Typography>

                <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                  <Chip
                    label={server.category}
                    size="small"
                    sx={{ 
                      backgroundColor: getCategoryColor(server.category),
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                  {server.language && (
                    <Chip
                      label={`${getLanguageIcon(server.language)} ${server.language}`}
                      size="small"
                      variant="outlined"
                    />
                  )}
                  <Chip
                    label={`${Math.round(server.confidence_score * 100)}% confidence`}
                    size="small"
                    color={server.confidence_score > 0.8 ? 'success' : 'default'}
                    variant="outlined"
                  />
                </Box>

                {server.stars && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <StarIcon fontSize="small" color="warning" />
                    <Typography variant="body2" color="text.secondary">
                      {server.stars} stars
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {servers.length === 0 && !loading && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary">
            No MCP servers found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Try adjusting your search criteria or discover new servers
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default MCPMarketMap;
