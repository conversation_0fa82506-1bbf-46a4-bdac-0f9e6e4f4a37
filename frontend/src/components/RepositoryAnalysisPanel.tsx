import {
    ChartBarIcon,
    CpuChipIcon,
    CubeIcon,
    DocumentTextIcon,
    ServerIcon
} from '@heroicons/react/24/outline';

interface RepositoryAnalysisPanelProps {
  analysis: any;
}

export default function RepositoryAnalysisPanel({ analysis }: RepositoryAnalysisPanelProps) {
  // Debug: Log the analysis object to see what data we have
  console.log('RepositoryAnalysisPanel received analysis:', analysis);
  console.log('Analysis keys:', analysis ? Object.keys(analysis) : 'No analysis');
  console.log('Analysis results:', analysis?.analysis_results);

  if (!analysis) {
    return (
      <div className="flex items-center justify-center h-full text-gray-600">
        <div className="text-center">
          <CpuChipIcon className="h-12 w-12 mx-auto mb-4 opacity-60 text-gray-500" />
          <p className="text-gray-600">No analysis data available</p>
        </div>
      </div>
    );
  }

  // Check if analysis_results exists and has data
  if (!analysis.analysis_results || Object.keys(analysis.analysis_results).length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-600">
        <div className="text-center">
          <CpuChipIcon className="h-12 w-12 mx-auto mb-4 opacity-60 text-gray-500" />
          <p className="text-gray-600">Repository analysis data not available</p>
          <p className="text-sm text-gray-500 mt-2">
            {analysis.status === 'completed' ? 'Analysis completed but no detailed results found' : 'Analysis may still be in progress'}
          </p>
          <div className="mt-4 text-xs text-gray-400 max-w-md bg-gray-100 p-3 rounded">
            <p><strong>Debug Info:</strong></p>
            <p>Status: {analysis.status}</p>
            <p>Analysis object keys: {Object.keys(analysis).join(', ')}</p>
            <p>Has analysis_results: {analysis.analysis_results ? 'Yes' : 'No'}</p>
            <p>Analysis_results type: {typeof analysis.analysis_results}</p>
            <p>Analysis_results keys: {analysis.analysis_results ? Object.keys(analysis.analysis_results).join(', ') : 'None'}</p>
          </div>
        </div>
      </div>
    );
  }

  const results = analysis.analysis_results;

  // Extract data from the actual structure returned by intelligent analysis
  const repositoryInfo = results.repository_info || {};
  const comprehensiveAnalysis = results.comprehensive_analysis || {};
  const businessLogic = comprehensiveAnalysis.business_logic || {};
  const apiCapabilities = comprehensiveAnalysis.api_capabilities || {};
  const integrationOpportunities = comprehensiveAnalysis.integration_opportunities || {};
  const detectedIntegrations = results.detected_integrations || [];
  const specificTools = results.specific_mcp_tools || [];
  const analysisMetadata = results.analysis_metadata || {};

  return (
    <div className="space-y-6">
      {/* Repository Overview */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center space-x-2 mb-3">
          <DocumentTextIcon className="h-5 w-5 text-blue-600" />
          <h3 className="font-semibold text-gray-900">Repository Overview</h3>
        </div>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Name:</span>
            <span className="font-medium">{analysis.repo_name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Owner:</span>
            <span className="font-medium">{analysis.repo_owner}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Status:</span>
            <span className={`font-medium capitalize ${
              analysis.status === 'completed' ? 'text-green-600' : 'text-yellow-600'
            }`}>
              {analysis.status}
            </span>
          </div>
          {repositoryInfo.language && (
            <div className="flex justify-between">
              <span className="text-gray-600">Primary Language:</span>
              <span className="font-medium">{repositoryInfo.language}</span>
            </div>
          )}
          {analysisMetadata.files_analyzed && (
            <div className="flex justify-between">
              <span className="text-gray-600">Files Analyzed:</span>
              <span className="font-medium">{analysisMetadata.files_analyzed}</span>
            </div>
          )}
        </div>
      </div>

      {/* Business Logic & Domain */}
      {businessLogic.primary_domain && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <CubeIcon className="h-5 w-5 text-purple-600" />
            <h3 className="font-semibold text-gray-900">Business Domain</h3>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Domain:</span>
              <span className="font-medium capitalize">{businessLogic.primary_domain}</span>
            </div>
            {businessLogic.business_purpose && (
              <div>
                <span className="text-gray-600">Purpose:</span>
                <p className="text-gray-800 mt-1">{businessLogic.business_purpose}</p>
              </div>
            )}
            {businessLogic.value_proposition && (
              <div>
                <span className="text-gray-600">Value Proposition:</span>
                <p className="text-gray-800 mt-1">{businessLogic.value_proposition}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* API Capabilities */}
      {apiCapabilities.existing_apis && apiCapabilities.existing_apis.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <ChartBarIcon className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">API Capabilities</h3>
            <span className="text-xs text-gray-500">({apiCapabilities.existing_apis.length})</span>
          </div>
          <div className="space-y-2">
            {apiCapabilities.existing_apis.slice(0, 5).map((api: any, index: number) => (
              <div key={index} className="bg-blue-50 border border-blue-200 rounded p-2">
                <div className="font-medium text-blue-800 text-sm">{api.name || api.endpoint || `API ${index + 1}`}</div>
                {api.description && (
                  <div className="text-xs text-blue-700 mt-1">{api.description}</div>
                )}
              </div>
            ))}
            {apiCapabilities.existing_apis.length > 5 && (
              <p className="text-xs text-gray-500 mt-2">
                ... and {apiCapabilities.existing_apis.length - 5} more APIs
              </p>
            )}
          </div>
        </div>
      )}

      {/* Detected Integrations */}
      {detectedIntegrations && detectedIntegrations.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <ServerIcon className="h-5 w-5 text-indigo-600" />
            <h3 className="font-semibold text-gray-900">Detected Integrations</h3>
            <span className="text-xs text-gray-500">({detectedIntegrations.length})</span>
          </div>
          <div className="space-y-2">
            {detectedIntegrations.map((integration: any, index: number) => {
              // Debug: Log the integration object to see its structure
              console.log('Integration object:', integration);

              // Extract the integration name from various possible fields
              const integrationName = integration.name ||
                                    integration.service ||
                                    integration.type ||
                                    integration.integration_type ||
                                    integration.service_name ||
                                    integration.tool_name ||
                                    (typeof integration === 'string' ? integration : null) ||
                                    `Integration ${index + 1}`;

              const description = integration.description ||
                                integration.purpose ||
                                integration.use_case ||
                                '';

              return (
                <div key={index} className="bg-indigo-50 border border-indigo-200 rounded p-2">
                  <div className="font-medium text-indigo-800 text-sm">{integrationName}</div>
                  {description && (
                    <div className="text-xs text-indigo-700 mt-1">{description}</div>
                  )}
                  {integration.category && (
                    <div className="text-xs text-indigo-600 mt-1">
                      Category: {integration.category}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Core Operations */}
      {businessLogic.core_operations && businessLogic.core_operations.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <CpuChipIcon className="h-5 w-5 text-orange-600" />
            <h3 className="font-semibold text-gray-900">Core Operations</h3>
            <span className="text-xs text-gray-500">({businessLogic.core_operations.length})</span>
          </div>
          <div className="max-h-32 overflow-y-auto">
            <div className="space-y-1">
              {businessLogic.core_operations.slice(0, 10).map((operation: any, index: number) => (
                <div key={index} className="text-sm text-gray-700 py-1 border-b border-gray-100 last:border-b-0">
                  {typeof operation === 'string' ? operation : operation.name || operation.description || `Operation ${index + 1}`}
                </div>
              ))}
              {businessLogic.core_operations.length > 10 && (
                <p className="text-xs text-gray-500 mt-2">
                  ... and {businessLogic.core_operations.length - 10} more operations
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Business Entities */}
      {businessLogic.business_entities && businessLogic.business_entities.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <DocumentTextIcon className="h-5 w-5 text-green-600" />
            <h3 className="font-semibold text-gray-900">Business Entities</h3>
            <span className="text-xs text-gray-500">({businessLogic.business_entities.length})</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {businessLogic.business_entities.slice(0, 10).map((entity: any, index: number) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
              >
                {typeof entity === 'string' ? entity : entity.name || entity.type || `Entity ${index + 1}`}
              </span>
            ))}
            {businessLogic.business_entities.length > 10 && (
              <span className="text-xs text-gray-500">
                +{businessLogic.business_entities.length - 10} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Generated MCP Tools */}
      {specificTools && specificTools.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <CubeIcon className="h-5 w-5 text-green-600" />
            <h3 className="font-semibold text-gray-900">Generated MCP Tools</h3>
            <span className="text-xs text-gray-500">({specificTools.length})</span>
          </div>
          <div className="space-y-2">
            {specificTools.slice(0, 5).map((tool: any, index: number) => (
              <div key={index} className="bg-green-50 border border-green-200 rounded p-2">
                <div className="font-medium text-green-800 text-sm">{tool.name || `Tool ${index + 1}`}</div>
                {tool.description && (
                  <div className="text-xs text-green-700 mt-1">{tool.description}</div>
                )}
                {tool.implementation_effort && (
                  <div className="text-xs text-green-600 mt-1">
                    Effort: <span className="capitalize">{tool.implementation_effort}</span>
                  </div>
                )}
              </div>
            ))}
            {specificTools.length > 5 && (
              <p className="text-xs text-gray-500 mt-2">
                ... and {specificTools.length - 5} more tools
              </p>
            )}
          </div>
        </div>
      )}

      {/* Analysis Metrics */}
      {(results.confidence_score !== undefined || results.enhanced_metrics) && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <ChartBarIcon className="h-5 w-5 text-gray-600" />
            <h3 className="font-semibold text-gray-900">Analysis Metrics</h3>
          </div>
          <div className="space-y-2 text-sm">
            {results.confidence_score !== undefined && (
              <div className="flex justify-between">
                <span className="text-gray-600">Confidence Score:</span>
                <span className="font-medium">{(results.confidence_score * 100).toFixed(1)}%</span>
              </div>
            )}
            {results.enhanced_metrics && (
              <>
                <div className="flex justify-between">
                  <span className="text-gray-600">Analysis Quality:</span>
                  <span className="font-medium">Enhanced</span>
                </div>
                {results.enhanced_metrics.confidence_score && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Enhanced Confidence:</span>
                    <span className="font-medium">{(results.enhanced_metrics.confidence_score * 100).toFixed(1)}%</span>
                  </div>
                )}
                {results.enhanced_metrics.complexity_assessment && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Complexity:</span>
                    <span className="font-medium capitalize">{results.enhanced_metrics.complexity_assessment}</span>
                  </div>
                )}
              </>
            )}
            {analysisMetadata.repository_size && (
              <div className="flex justify-between">
                <span className="text-gray-600">Repository Size:</span>
                <span className="font-medium">{(analysisMetadata.repository_size / 1024).toFixed(1)} KB</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
