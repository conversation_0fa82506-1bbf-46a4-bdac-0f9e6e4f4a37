'use client';

import React, { useState } from 'react';
import { 
  CogIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  DocumentTextIcon,
  CodeBracketIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import apiClient from '@/lib/api';

interface RequirementsDrivenMCPGeneratorProps {
  analysisId: number;
  onGenerationComplete?: (result: any) => void;
}

interface RequirementAnalysis {
  parsed_requirements: Array<{
    description: string;
    category: string;
    priority: string;
    expected_inputs: string[];
    expected_outputs: string[];
    business_context: string;
  }>;
  requirement_mappings: Array<{
    requirement: any;
    matched_functions: any[];
    implementation_approach: string;
    confidence_score: number;
    missing_capabilities: string[];
  }>;
  feasibility: {
    feasible: boolean;
    feasibility_ratio: number;
    missing_capabilities: string[];
    suggestions: string[];
  };
}

export default function RequirementsDrivenMCPGenerator({ 
  analysisId, 
  onGenerationComplete 
}: RequirementsDrivenMCPGeneratorProps) {
  const [userRequirements, setUserRequirements] = useState('');
  const [targetLanguage, setTargetLanguage] = useState('python');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationResult, setGenerationResult] = useState<any>(null);
  const [requirementAnalysis, setRequirementAnalysis] = useState<RequirementAnalysis | null>(null);
  const [showAnalysis, setShowAnalysis] = useState(false);

  const handleGenerate = async () => {
    if (!userRequirements.trim()) {
      alert('Please describe your requirements for the MCP server');
      return;
    }

    setIsGenerating(true);
    setGenerationResult(null);
    setRequirementAnalysis(null);

    try {
      const response = await apiClient.post(`/mcp-generation/${analysisId}/generate-requirements-driven`, {
        user_requirements: userRequirements,
        target_language: targetLanguage,
        validate_requirements: true
      });

      if (response.data.success) {
        setGenerationResult(response.data);
        setRequirementAnalysis(response.data.requirements_analysis);
        if (onGenerationComplete) {
          onGenerationComplete(response.data);
        }
      } else {
        // Handle feasibility issues
        setRequirementAnalysis(response.data.requirements_analysis);
        setGenerationResult(response.data);
      }
    } catch (error: any) {
      console.error('Generation failed:', error);
      alert(`Generation failed: ${error.response?.data?.detail || error.message}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.7) return 'text-green-600 bg-green-100';
    if (score >= 0.4) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getConfidenceIcon = (score: number) => {
    if (score >= 0.7) return <CheckCircleIcon className="h-4 w-4" />;
    return <ExclamationTriangleIcon className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      {/* Requirements Input */}
      <Card className="border-0 bg-gradient-to-br from-white via-white to-blue-50/30 shadow-lg shadow-blue-100/50">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900 flex items-center">
            <DocumentTextIcon className="mr-3 h-6 w-6 text-blue-600" />
            Describe Your MCP Requirements
          </CardTitle>
          <p className="text-gray-600">
            Describe what you want your MCP server to do. Be specific about the functionality, inputs, and outputs you need.
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Requirements Description
              </label>
              <Textarea
                value={userRequirements}
                onChange={(e) => setUserRequirements(e.target.value)}
                placeholder="Example: I need an MCP server that can manage API gateway configurations, including adding new routes, updating rate limits, and monitoring endpoint health. It should accept route configurations as input and return status updates."
                rows={6}
                className="w-full"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Language
                </label>
                <Select value={targetLanguage} onValueChange={setTargetLanguage}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="python">Python</SelectItem>
                    <SelectItem value="typescript">TypeScript</SelectItem>
                    <SelectItem value="javascript">JavaScript</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button 
                  onClick={handleGenerate}
                  disabled={isGenerating || !userRequirements.trim()}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  {isGenerating ? (
                    <>
                      <CogIcon className="mr-2 h-4 w-4 animate-spin" />
                      Analyzing Requirements...
                    </>
                  ) : (
                    <>
                      <CodeBracketIcon className="mr-2 h-4 w-4" />
                      Generate MCP Server
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Requirements Analysis */}
      {requirementAnalysis && (
        <Card className="border-0 bg-gradient-to-br from-white via-white to-purple-50/30 shadow-lg shadow-purple-100/50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold text-gray-900 flex items-center justify-between">
              <div className="flex items-center">
                <CheckCircleIcon className="mr-3 h-6 w-6 text-purple-600" />
                Requirements Analysis
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAnalysis(!showAnalysis)}
              >
                {showAnalysis ? 'Hide Details' : 'Show Details'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Feasibility Summary */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-semibold text-gray-900">Feasibility Assessment</h4>
                <Badge className={`${requirementAnalysis.feasibility.feasible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} border-0`}>
                  {requirementAnalysis.feasibility.feasible ? 'Feasible' : 'Needs Attention'}
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-white rounded-lg border">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round(requirementAnalysis.feasibility.feasibility_ratio * 100)}%
                  </div>
                  <div className="text-sm text-gray-600">Feasibility</div>
                </div>
                <div className="text-center p-3 bg-white rounded-lg border">
                  <div className="text-2xl font-bold text-green-600">
                    {requirementAnalysis.parsed_requirements.length}
                  </div>
                  <div className="text-sm text-gray-600">Requirements</div>
                </div>
                <div className="text-center p-3 bg-white rounded-lg border">
                  <div className="text-2xl font-bold text-purple-600">
                    {requirementAnalysis.requirement_mappings.filter(m => m.confidence_score > 0.4).length}
                  </div>
                  <div className="text-sm text-gray-600">Mappable</div>
                </div>
              </div>

              {!requirementAnalysis.feasibility.feasible && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h5 className="font-medium text-red-800 mb-2">Issues Found:</h5>
                  <ul className="list-disc list-inside text-red-700 text-sm space-y-1">
                    {requirementAnalysis.feasibility.missing_capabilities.map((capability, index) => (
                      <li key={index}>{capability}</li>
                    ))}
                  </ul>
                  
                  {requirementAnalysis.feasibility.suggestions.length > 0 && (
                    <div className="mt-3">
                      <h5 className="font-medium text-red-800 mb-2">Suggestions:</h5>
                      <ul className="list-disc list-inside text-red-700 text-sm space-y-1">
                        {requirementAnalysis.feasibility.suggestions.map((suggestion, index) => (
                          <li key={index}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Detailed Analysis */}
            {showAnalysis && (
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900">Parsed Requirements</h4>
                {requirementAnalysis.parsed_requirements.map((req, index) => (
                  <div key={index} className="bg-white rounded-lg border p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h5 className="font-medium text-gray-900">{req.description}</h5>
                      <div className="flex space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {req.category}
                        </Badge>
                        <Badge variant="outline" className={`text-xs ${
                          req.priority === 'high' ? 'text-red-700 bg-red-50' :
                          req.priority === 'medium' ? 'text-yellow-700 bg-yellow-50' :
                          'text-gray-700 bg-gray-50'
                        }`}>
                          {req.priority} priority
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Expected Inputs:</span>
                        <div className="text-gray-600">{req.expected_inputs.join(', ') || 'None specified'}</div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Expected Outputs:</span>
                        <div className="text-gray-600">{req.expected_outputs.join(', ') || 'None specified'}</div>
                      </div>
                    </div>
                    
                    {req.business_context && (
                      <div className="mt-2 text-sm">
                        <span className="font-medium text-gray-700">Business Context:</span>
                        <div className="text-gray-600">{req.business_context}</div>
                      </div>
                    )}
                  </div>
                ))}

                <h4 className="font-semibold text-gray-900 mt-6">Requirement Mappings</h4>
                {requirementAnalysis.requirement_mappings.map((mapping, index) => (
                  <div key={index} className="bg-white rounded-lg border p-4">
                    <div className="flex items-start justify-between mb-3">
                      <h5 className="font-medium text-gray-900">{mapping.requirement.description}</h5>
                      <Badge className={`${getConfidenceColor(mapping.confidence_score)} border-0`}>
                        {getConfidenceIcon(mapping.confidence_score)}
                        <span className="ml-1">{Math.round(mapping.confidence_score * 100)}% confidence</span>
                      </Badge>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Implementation Approach:</span>
                        <span className="ml-2 text-gray-600">{mapping.implementation_approach}</span>
                      </div>
                      
                      <div>
                        <span className="font-medium text-gray-700">Matched Functions:</span>
                        <span className="ml-2 text-gray-600">
                          {mapping.matched_functions.length > 0 
                            ? mapping.matched_functions.map(f => f.name || 'unnamed').join(', ')
                            : 'None found'
                          }
                        </span>
                      </div>
                      
                      {mapping.missing_capabilities.length > 0 && (
                        <div>
                          <span className="font-medium text-gray-700">Missing Capabilities:</span>
                          <ul className="ml-4 list-disc text-gray-600">
                            {mapping.missing_capabilities.map((cap, capIndex) => (
                              <li key={capIndex}>{cap}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Generation Result */}
      {generationResult && generationResult.success && (
        <Card className="border-0 bg-gradient-to-br from-white via-white to-green-50/30 shadow-lg shadow-green-100/50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold text-gray-900 flex items-center">
              <CheckCircleIcon className="mr-3 h-6 w-6 text-green-600" />
              MCP Server Generated Successfully
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-green-600">
                  {generationResult.tools_generated}
                </div>
                <div className="text-sm text-gray-600">Tools Generated</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(generationResult.requirements_coverage?.coverage_percentage || 0)}%
                </div>
                <div className="text-sm text-gray-600">Requirements Coverage</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-purple-600">
                  {targetLanguage.toUpperCase()}
                </div>
                <div className="text-sm text-gray-600">Target Language</div>
              </div>
            </div>

            <div className="flex space-x-4">
              <Button className="bg-green-600 hover:bg-green-700">
                <ArrowRightIcon className="mr-2 h-4 w-4" />
                Download MCP Server
              </Button>
              <Button variant="outline">
                <DocumentTextIcon className="mr-2 h-4 w-4" />
                View Documentation
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
