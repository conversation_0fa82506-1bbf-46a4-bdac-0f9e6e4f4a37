'use client';

import React from 'react';
import { 
  CpuChipIcon, 
  CodeBracketIcon, 
  ServerIcon, 
  DocumentTextIcon,
  ExclamationTriangleIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline';

interface MCPCapability {
  capability: string;
  underlying_tech: string;
  exposed_via_api: string;
  candidate_tool_name: string;
}

interface MCPServerSpec {
  name: string;
  description: string;
  parameters: Array<{
    name: string;
    type: string;
    required: boolean;
  }>;
}

interface ExistingMCPServer {
  server_name: string;
  overlapping_tools: string;
  when_to_reuse: string;
}

interface MCPAnalysisData {
  executive_summary: string;
  capability_matrix: MCPCapability[];
  new_mcp_server_specs: MCPServerSpec[];
  existing_mcp_servers: ExistingMCPServer[];
  gap_analysis: string;
  implementation_starter: string;
  client_config_snippet: string;
}

interface MCPAnalysisPanelProps {
  analysis: MCPAnalysisData;
}

export default function MCPAnalysisPanel({ analysis }: MCPAnalysisPanelProps) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="space-y-6">
      {/* Executive Summary */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center space-x-2 mb-3">
          <DocumentTextIcon className="h-5 w-5 text-blue-600" />
          <h3 className="font-semibold text-gray-800">Executive Summary</h3>
        </div>
        <p className="text-gray-700 text-sm leading-relaxed">
          {analysis.executive_summary}
        </p>
      </div>

      {/* Capability Matrix */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center space-x-2 mb-3">
          <CpuChipIcon className="h-5 w-5 text-green-600" />
          <h3 className="font-semibold text-gray-800">Capability Matrix</h3>
        </div>
        {analysis.capability_matrix.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2 px-3 font-medium text-gray-700">Capability</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">Tech</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">API/CLI</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">MCP Tool Name</th>
                </tr>
              </thead>
              <tbody>
                {analysis.capability_matrix.map((cap, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-2 px-3 text-gray-800">{cap.capability}</td>
                    <td className="py-2 px-3 text-gray-600">{cap.underlying_tech}</td>
                    <td className="py-2 px-3 text-gray-600">{cap.exposed_via_api}</td>
                    <td className="py-2 px-3 text-blue-600 font-medium">{cap.candidate_tool_name}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500 text-sm">No capabilities identified yet...</p>
        )}
      </div>

      {/* Existing MCP Servers */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center space-x-2 mb-3">
          <ServerIcon className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold text-gray-800">Existing MCP Servers</h3>
        </div>
        {analysis.existing_mcp_servers.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2 px-3 font-medium text-gray-700">Server</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">Overlapping Tools</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">When to Re-use</th>
                </tr>
              </thead>
              <tbody>
                {analysis.existing_mcp_servers.map((server, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-2 px-3 text-purple-600 font-medium">{server.server_name}</td>
                    <td className="py-2 px-3 text-gray-600">{server.overlapping_tools}</td>
                    <td className="py-2 px-3 text-gray-600">{server.when_to_reuse}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500 text-sm">No existing servers identified yet...</p>
        )}
      </div>

      {/* Gap Analysis */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center space-x-2 mb-3">
          <ExclamationTriangleIcon className="h-5 w-5 text-orange-600" />
          <h3 className="font-semibold text-gray-800">Gap Analysis</h3>
        </div>
        <div className="text-gray-700 text-sm leading-relaxed whitespace-pre-wrap">
          {analysis.gap_analysis}
        </div>
      </div>

      {/* Implementation Starter */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <CodeBracketIcon className="h-5 w-5 text-indigo-600" />
            <h3 className="font-semibold text-gray-800">Implementation Starter</h3>
          </div>
          <button
            onClick={() => copyToClipboard(analysis.implementation_starter)}
            className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700"
          >
            <ClipboardDocumentIcon className="h-4 w-4" />
            <span>Copy</span>
          </button>
        </div>
        <div className="bg-gray-50 rounded-md p-3 overflow-x-auto">
          <pre className="text-sm text-gray-800 whitespace-pre-wrap">
            {analysis.implementation_starter}
          </pre>
        </div>
      </div>

      {/* Client Config Snippet */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <DocumentTextIcon className="h-5 w-5 text-green-600" />
            <h3 className="font-semibold text-gray-800">Client Config</h3>
          </div>
          <button
            onClick={() => copyToClipboard(analysis.client_config_snippet)}
            className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700"
          >
            <ClipboardDocumentIcon className="h-4 w-4" />
            <span>Copy</span>
          </button>
        </div>
        <div className="bg-gray-50 rounded-md p-3 overflow-x-auto">
          <pre className="text-sm text-gray-800 whitespace-pre-wrap">
            {analysis.client_config_snippet}
          </pre>
        </div>
      </div>
    </div>
  );
}
