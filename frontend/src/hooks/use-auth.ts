'use client'

import { useState, useEffect } from 'react'
import { authService, User } from '@/lib/auth'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    if (!authService.isAuthenticated()) {
      setLoading(false)
      return
    }

    try {
      const userData = await authService.getCurrentUser()
      setUser(userData)
    } catch (err) {
      console.error('Failed to load user:', err)
      setError('Failed to load user data')
      // Token might be invalid, remove it
      authService.logout()
    } finally {
      setLoading(false)
    }
  }

  const login = async () => {
    try {
      const { auth_url } = await authService.login()
      window.location.href = auth_url
    } catch (err) {
      console.error('Login failed:', err)
      setError('Login failed')
    }
  }

  const handleCallback = async (code: string) => {
    try {
      await authService.handleCallback(code)
      await loadUser()
      return true
    } catch (err) {
      console.error('OAuth callback failed:', err)
      setError('Authentication failed')
      return false
    }
  }

  const logout = async () => {
    try {
      await authService.logout()
      setUser(null)
    } catch (err) {
      console.error('Logout failed:', err)
    }
  }

  return {
    user,
    loading,
    error,
    login,
    logout,
    handleCallback,
    isAuthenticated: !!user,
  }
}