'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  RefreshCw, 
  Github, 
  Star, 
  ExternalLink, 
  Code,
  Filter,
  Loader2
} from 'lucide-react';
import { api } from '@/lib/api';

interface MCPServer {
  id: number;
  name: string;
  url: string;
  description: string;
  category: string;
  github_url?: string;
  npm_url?: string;
  stars?: number;
  language?: string;
  confidence_score: number;
  last_updated?: string;
}

interface MCPCategory {
  id: number;
  name: string;
  description: string;
  icon: string;
  color: string;
  server_count: number;
}

interface MCPStats {
  total_servers: number;
  category_distribution: Array<{ category: string; count: number }>;
  language_distribution: Array<{ language: string; count: number }>;
  top_servers: Array<{
    name: string;
    url: string;
    stars: number;
    category: string;
    language: string;
  }>;
}

const categoryIcons: { [key: string]: string } = {
  database: '🗄️',
  ai_chat: '🤖',
  development: '💻',
  productivity: '⚡',
  cloud: '☁️',
  monitoring: '📊',
  search: '🔍',
  payment: '💳',
  communication: '📧',
  design: '🎨',
  ticketing: '🎫',
  general: '🔧'
};

const languageIcons: { [key: string]: string } = {
  javascript: '🟨',
  typescript: '🔷',
  python: '🐍',
  go: '🐹',
  rust: '🦀',
  java: '☕'
};

export default function MCPMarketPage() {
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [categories, setCategories] = useState<MCPCategory[]>([]);
  const [stats, setStats] = useState<MCPStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [discovering, setDiscovering] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [serversResponse, categoriesResponse, statsResponse] = await Promise.all([
        api.get('/mcp-discovery/servers'),
        api.get('/mcp-discovery/categories'),
        api.get('/mcp-discovery/stats')
      ]);

      setServers(serversResponse.data.servers);
      setCategories(categoriesResponse.data.categories);
      setStats(statsResponse.data);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load MCP data');
    } finally {
      setLoading(false);
    }
  };

  const triggerDiscovery = async () => {
    try {
      setDiscovering(true);
      setError(null);

      await api.post('/mcp-discovery/discover');
      
      // Wait a bit then reload data
      setTimeout(() => {
        loadData();
        setDiscovering(false);
      }, 3000);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to trigger discovery');
      setDiscovering(false);
    }
  };

  const searchServers = async () => {
    if (!searchQuery.trim()) {
      loadData();
      return;
    }

    try {
      setLoading(true);
      const response = await api.get('/mcp-discovery/search', {
        params: {
          q: searchQuery,
          category: selectedCategory || undefined,
          language: selectedLanguage || undefined
        }
      });
      setServers(response.data.servers);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Search failed');
    } finally {
      setLoading(false);
    }
  };

  const filterServers = async () => {
    try {
      setLoading(true);
      const response = await api.get('/mcp-discovery/servers', {
        params: {
          category: selectedCategory || undefined,
          language: selectedLanguage || undefined,
          min_confidence: 0.5
        }
      });
      setServers(response.data.servers);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Filter failed');
    } finally {
      setLoading(false);
    }
  };

  const getCategoryColor = (categoryName: string) => {
    const category = categories.find(c => c.name === categoryName);
    return category?.color || '#6B7280';
  };

  if (loading && servers.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-purple-600 mb-2">
          🗺️ MCP Market Map
        </h1>
        <p className="text-gray-600 mb-4">
          Discover and explore active Model Context Protocol servers from the ecosystem
        </p>
        
        {stats && (
          <div className="flex gap-4 flex-wrap items-center">
            <Badge variant="outline" className="text-sm">
              {stats.total_servers} Total Servers
            </Badge>
            <Badge variant="outline" className="text-sm">
              {categories.length} Categories
            </Badge>
            <Button
              onClick={triggerDiscovery}
              disabled={discovering}
              className="ml-auto"
            >
              {discovering ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Discovering...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Discover New Servers
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search MCP servers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && searchServers()}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.name}>
                    {category.icon} {category.name} ({category.server_count})
                  </option>
                ))}
              </select>
            </div>
            <div>
              <Button onClick={filterServers} disabled={loading} className="w-full">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* MCP Servers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {servers.map((server) => (
          <Card 
            key={server.id}
            className="h-full hover:shadow-lg transition-all duration-200 hover:-translate-y-1"
            style={{ 
              borderLeft: `4px solid ${getCategoryColor(server.category)}` 
            }}
          >
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg font-bold">
                  {server.name}
                </CardTitle>
                <div className="flex gap-1">
                  {server.github_url && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(server.github_url, '_blank')}
                    >
                      <Github className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open(server.url, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm mb-4 min-h-[40px]">
                {server.description}
              </p>

              <div className="flex gap-2 mb-3 flex-wrap">
                <Badge 
                  style={{ 
                    backgroundColor: getCategoryColor(server.category),
                    color: 'white'
                  }}
                >
                  {categoryIcons[server.category]} {server.category}
                </Badge>
                {server.language && (
                  <Badge variant="outline">
                    {languageIcons[server.language?.toLowerCase()] || '💻'} {server.language}
                  </Badge>
                )}
                <Badge 
                  variant={server.confidence_score > 0.8 ? "default" : "secondary"}
                >
                  {Math.round(server.confidence_score * 100)}% confidence
                </Badge>
              </div>

              {server.stars && (
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Star className="h-4 w-4 text-yellow-500" />
                  {server.stars} stars
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {servers.length === 0 && !loading && (
        <div className="text-center py-12">
          <h3 className="text-lg font-semibold text-gray-600 mb-2">
            No MCP servers found
          </h3>
          <p className="text-gray-500">
            Try adjusting your search criteria or discover new servers
          </p>
        </div>
      )}
    </div>
  );
}
