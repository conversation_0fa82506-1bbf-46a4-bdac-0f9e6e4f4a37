import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'SuperMCP - AI-Powered MCP Tool Generator',
  description: 'Analyze GitHub repositories and generate custom MCP tools with AI. Transform any repository into powerful AI assistant capabilities.',
  keywords: ['MCP', 'Model Context Protocol', 'AI Tools', 'GitHub Analysis', 'Repository Analysis', 'AI Assistant'],
  authors: [{ name: 'SuperMCP Team' }],
  creator: 'SuperMCP',
  publisher: 'SuperMCP',
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
      { url: '/supermcp.png', sizes: '192x192', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
    apple: '/supermcp.png',
  },
  manifest: '/manifest.json',
  openGraph: {
    title: 'SuperMCP - AI-Powered MCP Tool Generator',
    description: 'Transform GitHub repositories into powerful AI assistant capabilities with custom MCP tools.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SuperMCP - AI-Powered MCP Tool Generator',
    description: 'Transform GitHub repositories into powerful AI assistant capabilities with custom MCP tools.',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  )
}