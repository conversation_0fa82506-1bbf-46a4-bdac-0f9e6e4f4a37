'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { healthAPI } from '@/lib/api';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, RefreshCw, XCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ServiceStatus {
  healthy: boolean;
  message: string;
  critical?: boolean;
  details?: any;
}

interface HealthStatus {
  overall_status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: {
    [key: string]: ServiceStatus;
  };
  critical_services_down: string[];
  warnings: string[];
}

export default function HealthPage() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const fetchHealthStatus = async () => {
    setLoading(true);
    try {
      const response = await healthAPI.getSystemHealth();
      setHealthStatus(response);
      setLastChecked(new Date());
    } catch (error) {
      console.error('Failed to fetch health status:', error);
      // Set a basic error state
      setHealthStatus({
        overall_status: 'unhealthy',
        timestamp: new Date().toISOString(),
        services: {
          backend: {
            healthy: false,
            message: 'Backend API unreachable',
            critical: true
          }
        },
        critical_services_down: ['backend'],
        warnings: []
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthStatus();
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchHealthStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (healthy: boolean, critical: boolean = true) => {
    if (healthy) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else if (critical) {
      return <XCircle className="h-5 w-5 text-red-500" />;
    } else {
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge variant="default" className="bg-green-500">Healthy</Badge>;
      case 'degraded':
        return <Badge variant="secondary" className="bg-yellow-500">Degraded</Badge>;
      case 'unhealthy':
        return <Badge variant="destructive">Unhealthy</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getServiceDisplayName = (serviceName: string) => {
    const names: { [key: string]: string } = {
      database: 'PostgreSQL Database',
      redis: 'Redis Cache',
      weaviate: 'Weaviate Vector DB',
      celery: 'Celery Workers',
      ai_services: 'AI Services'
    };
    return names[serviceName] || serviceName;
  };

  if (loading && !healthStatus) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span>Checking system health...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Health</h1>
          <p className="text-muted-foreground">
            Monitor the status of all SuperMCP services
          </p>
        </div>
        <Button onClick={fetchHealthStatus} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overall Status */}
      <Card className={`${
        healthStatus?.overall_status === 'healthy' ? 'border-green-200 bg-green-50' :
        healthStatus?.overall_status === 'degraded' ? 'border-yellow-200 bg-yellow-50' :
        'border-red-200 bg-red-50'
      }`}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {healthStatus?.overall_status === 'healthy' && <CheckCircle className="h-8 w-8 text-green-500" />}
              {healthStatus?.overall_status === 'degraded' && <AlertTriangle className="h-8 w-8 text-yellow-500" />}
              {healthStatus?.overall_status === 'unhealthy' && <XCircle className="h-8 w-8 text-red-500" />}
              <div>
                <div className="text-xl font-bold">Overall System Status</div>
                <div className="text-sm text-muted-foreground">
                  Last checked: {lastChecked?.toLocaleString() || 'Never'}
                </div>
              </div>
            </div>
            {healthStatus && (
              <div className="text-right">
                {getStatusBadge(healthStatus.overall_status)}
                <div className="text-xs text-muted-foreground mt-1">
                  {healthStatus.services ? Object.keys(healthStatus.services).length : 0} services monitored
                </div>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {healthStatus?.critical_services_down && healthStatus.critical_services_down.length > 0 && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">Critical Services Down:</h4>
              <ul className="list-disc list-inside text-red-700">
                {healthStatus.critical_services_down.map((service) => (
                  <li key={service}>{getServiceDisplayName(service)}</li>
                ))}
              </ul>
            </div>
          )}
          
          {healthStatus?.warnings && healthStatus.warnings.length > 0 && (
            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="font-semibold text-yellow-800 mb-2">Warnings:</h4>
              <ul className="list-disc list-inside text-yellow-700">
                {healthStatus.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Legend */}
      <Card className="bg-gray-50">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Service Status Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Status Indicators</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <Badge className="bg-green-500 text-xs">Healthy</Badge>
                  <span>Service is working normally</span>
                </div>
                <div className="flex items-center space-x-2">
                  <XCircle className="h-4 w-4 text-red-500" />
                  <Badge variant="destructive" className="text-xs">Down</Badge>
                  <span>Service is not responding</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Service Priority</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800 border-blue-200">
                    Essential
                  </Badge>
                  <span>Required for core functionality</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="text-xs">
                    Optional
                  </Badge>
                  <span>Enhances features but not required</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Services */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {healthStatus?.services && Object.entries(healthStatus.services).map(([serviceName, status]) => (
          <Card key={serviceName} className={`${
            !status.healthy && status.critical ? 'border-red-200 bg-red-50' :
            !status.healthy ? 'border-yellow-200 bg-yellow-50' :
            'border-green-200 bg-green-50'
          }`}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-lg">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(status.healthy, status.critical)}
                  <span>{getServiceDisplayName(serviceName)}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {status.critical ? (
                    <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800 border-blue-200">
                      Essential
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600 border-gray-200">
                      Optional
                    </Badge>
                  )}
                  <Badge
                    variant={status.healthy ? "default" : "destructive"}
                    className={`text-xs ${
                      status.healthy ? 'bg-green-500 hover:bg-green-600' : ''
                    }`}
                  >
                    {status.healthy ? "Healthy" : "Down"}
                  </Badge>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className={`text-sm mb-3 ${
                !status.healthy && status.critical ? 'text-red-700' :
                !status.healthy ? 'text-yellow-700' :
                'text-green-700'
              }`}>
                {status.message}
              </p>

              {status.details && (
                <div className="space-y-1">
                  <h4 className="text-xs font-semibold text-gray-600 uppercase tracking-wide">Details</h4>
                  <div className="text-xs text-muted-foreground space-y-1">
                    {Object.entries(status.details).map(([key, value]) => (
                      <div key={key} className="flex justify-between items-start">
                        <span className="capitalize font-medium">{key.replace(/_/g, ' ')}:</span>
                        <span className="text-right max-w-[60%] break-words">
                          {Array.isArray(value) ? value.join(', ') : String(value)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle>System Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Application</h4>
              <div className="space-y-1 text-muted-foreground">
                <div>SuperMCP - AI-Powered MCP Generation Platform</div>
                <div>Version: 1.0.0</div>
                <div>Environment: Development</div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Features</h4>
              <div className="space-y-1 text-muted-foreground">
                <div>✅ Unlimited Repository Processing</div>
                <div>✅ Parallel Analysis (8 Workers)</div>
                <div>✅ Vector Database Integration</div>
                <div>✅ AI-Powered Code Analysis</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
