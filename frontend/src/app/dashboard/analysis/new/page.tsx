'use client';

import SimpleRepositoryBrowser from '@/components/analysis/SimpleRepositoryBrowser';
import DashboardLayout from '@/components/layout/dashboard-layout';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import apiClient from '@/lib/api';
import {
    ArrowLeftIcon,
    ChartBarIcon,
    ExclamationTriangleIcon,
    LightBulbIcon,
    MagnifyingGlassIcon,
    SparklesIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function NewAnalysisPage() {
  const [repoUrl, setRepoUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFromBrowser, setSelectedFromBrowser] = useState(false);
  const router = useRouter();

  const parseGitHubUrl = (url: string) => {
    try {
      // Handle various GitHub URL formats
      const patterns = [
        /github\.com\/([^\/]+)\/([^\/]+?)(?:\.git)?(?:\/.*)?$/,
        /^([^\/]+)\/([^\/]+)$/
      ];

      for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
          return {
            owner: match[1],
            repo: match[2].replace('.git', '')
          };
        }
      }

      throw new Error('Invalid URL format');
    } catch (err) {
      throw new Error('Unable to parse repository URL');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      if (!repoUrl.trim()) {
        throw new Error('Please enter a repository URL');
      }

      const { owner, repo } = parseGitHubUrl(repoUrl.trim());

      // Create the analysis
      const analysis = await apiClient.createAnalysis({
        repo_url: repoUrl.trim(),
        repo_name: repo,
        repo_owner: owner
      });

      // Redirect to the analysis detail page
      router.push(`/dashboard/analysis/${analysis.id}`);
    } catch (err: any) {
      console.error('Failed to create analysis:', err);
      console.error('Error details:', {
        status: err.response?.status,
        data: err.response?.data,
        message: err.message,
        isAuthenticated: apiClient.isAuthenticated()
      });

      if (err.response?.status === 401) {
        setError('Please log in to create an analysis');
        router.push('/auth/login');
      } else if (err.code === 'ERR_NETWORK') {
        setError('Network error - please check if you are logged in');
      } else {
        setError(err.response?.data?.detail || err.message || 'Failed to start analysis');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRepoUrl(e.target.value);
    setError(null); // Clear error when user types
    setSelectedFromBrowser(false); // Reset browser selection flag
  };

  const handleRepositorySelect = (url: string) => {
    setRepoUrl(url);
    setError(null);
    setSelectedFromBrowser(true);
    // Scroll to form for better UX
    document.getElementById('repo-url')?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  };

  const exampleUrls = [
    'https://github.com/microsoft/vscode',
    'https://github.com/facebook/react',
    'https://github.com/vercel/next.js',
    'openai/openai-python'
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <Link href="/dashboard/analysis" className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-4">
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Analysis
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">New Repository Analysis</h1>
          <p className="text-gray-600">
            Analyze a GitHub repository to discover MCP server potential and generate implementation recommendations.
          </p>
        </div>

        {/* Repository Browser */}
        <SimpleRepositoryBrowser
          onRepositorySelect={handleRepositorySelect}
          className="mb-6"
        />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Main Form */}
          <Card className="border border-gray-200">
            <CardHeader>
              <CardTitle className="text-lg">Repository Analysis</CardTitle>
              <CardDescription>
                Enter the GitHub repository URL to begin analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="repo-url" className="text-sm font-medium">GitHub Repository URL</Label>
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="repo-url"
                      type="url"
                      placeholder="https://github.com/owner/repository or owner/repository"
                      value={repoUrl}
                      onChange={handleUrlChange}
                      className="pl-10"
                      disabled={loading}
                    />
                  </div>
                  <p className="text-sm text-gray-500">
                    Enter a full GitHub URL or just the owner/repository format.
                    {selectedFromBrowser && (
                      <span className="text-green-600 font-medium ml-2">
                        ✓ Selected from browser
                      </span>
                    )}
                  </p>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <ExclamationTriangleIcon className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                  <Link href="/dashboard/analysis">
                    <Button type="button" variant="outline" disabled={loading} className="w-full sm:w-auto">
                      Cancel
                    </Button>
                  </Link>
                  <Button
                    type="submit"
                    disabled={loading || !repoUrl.trim()}
                    className="w-full sm:w-auto"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Starting Analysis...
                      </>
                    ) : (
                      <>
                        <ChartBarIcon className="mr-2 h-4 w-4" />
                        Start Analysis
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Information Section */}
          <div className="space-y-6">
            <Card className="border border-gray-200">
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <SparklesIcon className="mr-2 h-5 w-5" />
                  How Analysis Works
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                    1
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Repository Scan</h4>
                    <p className="text-sm text-gray-600">
                      Analyzes code structure, dependencies, and patterns.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                    2
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Feasibility Scoring</h4>
                    <p className="text-sm text-gray-600">
                      Calculates MCP server potential based on multiple factors.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                    3
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Tool Generation</h4>
                    <p className="text-sm text-gray-600">
                      Generates tailored MCP tools with implementation guides.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200">
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <LightBulbIcon className="mr-2 h-5 w-5" />
                  Example Repositories
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {exampleUrls.map((url, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => setRepoUrl(url)}
                      className="w-full text-left p-3 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors"
                      disabled={loading}
                    >
                      <span className="text-sm font-medium text-gray-700">
                        {url}
                      </span>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>


      </div>
    </DashboardLayout>
  );
}