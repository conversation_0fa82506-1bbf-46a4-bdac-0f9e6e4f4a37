'use client';

import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function MCPPlaygroundPage() {
  const params = useParams();
  const analysisId = params.id as string;
  const [generationPrompt, setGenerationPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [mcpServer, setMcpServer] = useState(null);
  const [error, setError] = useState('');
  const [selectedFile, setSelectedFile] = useState('README.md');
  const [isDownloading, setIsDownloading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResults, setTestResults] = useState([]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const prompt = urlParams.get('prompt');
    if (prompt) {
      setGenerationPrompt(decodeURIComponent(prompt));
    }
  }, []);

  const generateMCPServer = async () => {
    if (!generationPrompt.trim()) return;

    setIsGenerating(true);
    setError('');

    try {
      // Import apiClient dynamically to avoid import issues
      const { default: apiClient } = await import('@/lib/api');

      const response = await apiClient.post(`/analysis/${analysisId}/mcp/generate-server`, {
        prompt: generationPrompt
      });

      if (response.data.success) {
        setMcpServer(response.data.server);
      } else {
        setError(response.data.error || 'Generation failed');
      }
    } catch (err) {
      setError('Failed to generate MCP server. Please try again.');
      console.error('Generation error:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadMCPServer = async () => {
    if (!mcpServer || !mcpServer.files) return;

    setIsDownloading(true);
    try {
      // Call backend to create zip file
      const response = await fetch(`/api/v1/analysis/${analysisId}/mcp/download-server`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          server: mcpServer
        })
      });

      if (!response.ok) {
        throw new Error('Download failed');
      }

      // Get the blob from response
      const blob = await response.blob();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${mcpServer.name || 'mcp-server'}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('Failed to download MCP server. Please try again.');
      console.error('Download error:', err);
    } finally {
      setIsDownloading(false);
    }
  };

  const testMCPServer = async () => {
    if (!mcpServer) return;

    setIsTesting(true);
    setTestResults([]);

    try {
      const { default: apiClient } = await import('@/lib/api');

      const response = await apiClient.post(`/analysis/${analysisId}/mcp/test-server`, {
        server_code: mcpServer.code,
        package_json: mcpServer.packageJson,
        user_prompt: generationPrompt
      });

      if (response.data.success) {
        setTestResults(response.data.test_results);
      } else {
        setTestResults([{
          success: false,
          message: 'Testing failed',
          details: response.data.error
        }]);
      }
    } catch (err) {
      setTestResults([{
        success: false,
        message: 'Testing error',
        details: err instanceof Error ? err.message : 'Unknown error'
      }]);
      console.error('Testing error:', err);
    } finally {
      setIsTesting(false);
    }
  };

  const copyToClipboard = async (content) => {
    try {
      await navigator.clipboard.writeText(content);
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const getFileIcon = (fileName) => {
    if (fileName.includes('/')) return '📁'; // Folder
    if (fileName.endsWith('.js')) return '📄';
    if (fileName.endsWith('.json')) return '⚙️';
    if (fileName.endsWith('.md')) return '📝';
    if (fileName.endsWith('.sh')) return '🔧';
    if (fileName === 'Dockerfile') return '🐳';
    if (fileName.endsWith('.yml') || fileName.endsWith('.yaml')) return '📋';
    return '📄';
  };

  const getFileLanguage = (fileName) => {
    if (fileName.endsWith('.js')) return 'javascript';
    if (fileName.endsWith('.json')) return 'json';
    if (fileName.endsWith('.md')) return 'markdown';
    if (fileName.endsWith('.sh')) return 'bash';
    if (fileName === 'Dockerfile') return 'dockerfile';
    if (fileName.endsWith('.yml') || fileName.endsWith('.yaml')) return 'yaml';
    return 'text';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white border-b border-gray-200">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-8">
              <Link href="/dashboard" className="flex items-center space-x-3">
                <img
                  src="/supermcp.png"
                  alt="SuperMCP Logo"
                  className="h-8 w-8 object-contain"
                />
                <span className="text-lg font-semibold text-gray-900">
                  Super<span className="text-primary">MCP</span>
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">Welcome back!</span>
              <Link
                href="/dashboard"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none transition ease-in-out duration-150"
              >
                Dashboard
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="bg-white border-b border-gray-200">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                href={`/dashboard/analysis/${analysisId}/mcp-assistant`}
                className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-1" />
                Back to MCP Assistant
              </Link>
              <div className="h-4 w-px bg-gray-300"></div>
              <h1 className="text-lg font-semibold text-gray-900">MCP Playground</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Generate MCP Server</h2>
            {generationPrompt && (
              <div className="mb-4">
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  Context loaded from chat
                </span>
              </div>
            )}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Describe your MCP server
                </label>
                <textarea
                  value={generationPrompt}
                  onChange={(e) => setGenerationPrompt(e.target.value)}
                  placeholder="Describe the MCP server you want to create..."
                  className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>
              <button
                onClick={generateMCPServer}
                disabled={!generationPrompt.trim() || isGenerating}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Generating...
                  </>
                ) : (
                  'Generate MCP Server'
                )}
              </button>

              {error && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              )}
            </div>

            {mcpServer && (
              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Generated MCP Server</h3>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={testMCPServer}
                      disabled={isTesting}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50"
                    >
                      {isTesting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Testing...
                        </>
                      ) : (
                        <>
                          🧪 Test Server
                        </>
                      )}
                    </button>
                    <button
                      onClick={downloadMCPServer}
                      disabled={isDownloading}
                      className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 disabled:opacity-50"
                    >
                      {isDownloading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Downloading...
                        </>
                      ) : (
                        <>
                          📦 Download ZIP
                        </>
                      )}
                    </button>
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-green-800">
                        {mcpServer.name || 'MCP Server Generated Successfully!'}
                      </h4>
                      <p className="text-sm text-green-700 mt-1">
                        {mcpServer.description || 'Your MCP server has been generated and is ready to use.'}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Test Results Section */}
                {testResults.length > 0 && (
                  <div className="mb-6 bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <div className="p-4 border-b border-gray-200 bg-gray-50">
                      <h4 className="text-lg font-semibold text-gray-900">🧪 Validation Results</h4>
                      <p className="text-sm text-gray-600 mt-1">Comprehensive testing against requirements and best practices</p>
                    </div>
                    <div className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {testResults.map((result, index) => {
                          const getResultIcon = () => {
                            if (result.success) return '✅';
                            if (result.message.includes('GOOD') || result.message.includes('EXCELLENT')) return '🟡';
                            return '❌';
                          };

                          const getResultColor = () => {
                            if (result.success) return 'border-green-200 bg-green-50';
                            if (result.message.includes('GOOD') || result.message.includes('EXCELLENT')) return 'border-yellow-200 bg-yellow-50';
                            return 'border-red-200 bg-red-50';
                          };

                          const getTextColor = () => {
                            if (result.success) return 'text-green-800';
                            if (result.message.includes('GOOD') || result.message.includes('EXCELLENT')) return 'text-yellow-800';
                            return 'text-red-800';
                          };

                          return (
                            <div
                              key={index}
                              className={`border rounded-lg p-4 ${getResultColor()}`}
                            >
                              <div className="flex items-start">
                                <span className="text-lg mr-3 mt-0.5">{getResultIcon()}</span>
                                <div className="flex-1">
                                  <h5 className={`font-medium ${getTextColor()}`}>
                                    {result.message}
                                  </h5>
                                  {result.details && (
                                    <div className="mt-2">
                                      <details className="cursor-pointer">
                                        <summary className={`text-xs ${getTextColor()} hover:underline`}>
                                          View Details
                                        </summary>
                                        <div className={`mt-2 text-xs ${getTextColor()} whitespace-pre-wrap bg-white bg-opacity-50 p-2 rounded border`}>
                                          {result.details}
                                        </div>
                                      </details>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>

                      {/* Overall Score */}
                      <div className="mt-6 pt-4 border-t border-gray-200">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Overall Validation Score:</span>
                          <div className="flex items-center">
                            {(() => {
                              const passedTests = testResults.filter(r => r.success).length;
                              const totalTests = testResults.length;
                              const score = Math.round((passedTests / totalTests) * 100);

                              let scoreColor = 'text-red-600';
                              let scoreIcon = '❌';
                              if (score >= 80) {
                                scoreColor = 'text-green-600';
                                scoreIcon = '✅';
                              } else if (score >= 60) {
                                scoreColor = 'text-yellow-600';
                                scoreIcon = '🟡';
                              }

                              return (
                                <>
                                  <span className="mr-2">{scoreIcon}</span>
                                  <span className={`font-semibold ${scoreColor}`}>
                                    {score}% ({passedTests}/{totalTests} tests passed)
                                  </span>
                                </>
                              );
                            })()}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* GitHub-like file browser */}
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <div className="grid grid-cols-1 lg:grid-cols-3">
                    {/* File tree */}
                    <div className="lg:col-span-1 border-r border-gray-200 bg-gray-50">
                      <div className="p-4 border-b border-gray-200 bg-gray-100">
                        <h4 className="text-sm font-semibold text-gray-900">📁 {mcpServer.name}</h4>
                      </div>
                      <div className="p-2">
                        {mcpServer.files && Object.keys(mcpServer.files).map((fileName) => (
                          <button
                            key={fileName}
                            onClick={() => setSelectedFile(fileName)}
                            className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-200 flex items-center ${
                              selectedFile === fileName ? 'bg-blue-100 text-blue-800' : 'text-gray-700'
                            }`}
                          >
                            <span className="mr-2">{getFileIcon(fileName)}</span>
                            {fileName}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* File content */}
                    <div className="lg:col-span-2">
                      {selectedFile && mcpServer.files && mcpServer.files[selectedFile] && (
                        <>
                          <div className="p-4 border-b border-gray-200 bg-gray-50 flex items-center justify-between">
                            <div className="flex items-center">
                              <span className="mr-2">{getFileIcon(selectedFile)}</span>
                              <span className="font-medium text-gray-900">{selectedFile}</span>
                            </div>
                            <button
                              onClick={() => copyToClipboard(mcpServer.files[selectedFile])}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                            >
                              📋 Copy
                            </button>
                          </div>
                          <div className="p-4 bg-white overflow-x-auto max-h-96">
                            <pre className={`text-sm font-mono whitespace-pre-wrap language-${getFileLanguage(selectedFile)}`}>
                              {mcpServer.files[selectedFile]}
                            </pre>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
