'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/use-auth'
import { Loader2, XCircle } from 'lucide-react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense, useEffect, useRef, useState } from 'react'

function CallbackContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [error, setError] = useState<string>('')
  const { handleCallback } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()

  const processedRef = useRef(false)

  useEffect(() => {
    if (processedRef.current) {
      return
    }

    const code = searchParams.get('code')
    const error = searchParams.get('error')

    if (error) {
      setStatus('error')
      setError(`GitHub OAuth error: ${error}`)
      processedRef.current = true
      return
    }

    if (!code) {
      setStatus('error')
      setError('No authorization code received from GitHub')
      processedRef.current = true
      return
    }

    handleOAuthCallback(code)
    processedRef.current = true
  }, [searchParams])

  const handleOAuthCallback = async (code: string) => {
    try {
      const success = await handleCallback(code)
      if (success) {
        // Redirect immediately to dashboard
        router.push('/dashboard')
      } else {
        setStatus('error')
        setError('Authentication failed')
      }
    } catch (err) {
      console.error('OAuth callback error:', err)
      setStatus('error')
      setError('Authentication failed. Please try again.')
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="w-full max-w-md">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              {status === 'loading' && (
                <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
              )}
              {status === 'error' && (
                <XCircle className="w-8 h-8 text-red-500" />
              )}
            </div>

            <CardTitle>
              {status === 'loading' && 'Signing you in...'}
              {status === 'error' && 'Authentication Failed'}
            </CardTitle>

            <CardDescription>
              {status === 'loading' && 'Please wait while we complete your authentication.'}
              {status === 'error' && error}
            </CardDescription>
          </CardHeader>

          {status === 'error' && (
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <Button asChild className="flex-1">
                  <Link href="/auth/login">Try Again</Link>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/">Go Home</Link>
                </Button>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  )
}

export default function CallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="w-full max-w-md">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto mb-4">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
              </div>
              <CardTitle>Loading...</CardTitle>
              <CardDescription>Please wait while we process your authentication.</CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    }>
      <CallbackContent />
    </Suspense>
  )
}