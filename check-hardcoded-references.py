#!/usr/bin/env python3
"""
Check for Hardcoded References Script

This script scans the codebase for any hardcoded repository names, analysis IDs,
or other non-dynamic references that should be based on current analysis.
"""

import os
import re
import sys
from pathlib import Path

def scan_file_for_hardcoded_refs(file_path):
    """Scan a file for potential hardcoded references"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        # Patterns to check for hardcoded references
        patterns = [
            # Hardcoded analysis IDs
            (r'\.id\s*==\s*1(?!\d)', 'Hardcoded analysis ID 1'),
            (r'analysis_id\s*=\s*1(?!\d)', 'Hardcoded analysis_id = 1'),
            (r'RepoAnalysis.*filter.*1(?!\d)', 'Hardcoded RepoAnalysis filter with ID 1'),
            
            # Hardcoded repository names (but allow technology detection)
            (r'microsoft/playwright(?!.*technology|.*detect|.*marketplace)', 'Hardcoded microsoft/playwright repository'),
            (r'kortix-ai/suna(?!.*technology|.*detect)', 'Hardcoded kortix-ai/suna repository'),
            
            # Hardcoded URLs
            (r'github\.com/microsoft/playwright(?!.*example|.*marketplace)', 'Hardcoded GitHub URL'),
            (r'github\.com/kortix-ai/suna', 'Hardcoded GitHub URL'),
            
            # Hardcoded database queries that should be dynamic
            (r'WHERE.*id.*=.*1(?!\d)', 'Hardcoded WHERE id = 1 query'),
        ]
        
        for line_num, line in enumerate(lines, 1):
            for pattern, description in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # Skip comments and documentation
                    if line.strip().startswith('#') or line.strip().startswith('//') or line.strip().startswith('*'):
                        continue
                    # Skip example usage in help text
                    if 'example' in line.lower() or 'usage' in line.lower():
                        continue
                    
                    issues.append({
                        'file': file_path,
                        'line': line_num,
                        'content': line.strip(),
                        'issue': description,
                        'pattern': pattern
                    })
                    
    except Exception as e:
        print(f"Error scanning {file_path}: {e}")
        
    return issues

def scan_directory(directory, extensions):
    """Scan directory for files with given extensions"""
    all_issues = []
    
    for root, dirs, files in os.walk(directory):
        # Skip node_modules, __pycache__, .git, etc.
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'dist', 'build']]
        
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                issues = scan_file_for_hardcoded_refs(file_path)
                all_issues.extend(issues)
                
    return all_issues

def main():
    """Main function to scan for hardcoded references"""
    print("🔍 Scanning for hardcoded references in SuperMCP codebase...")
    
    # Define directories and file extensions to scan
    scan_configs = [
        {
            'directory': 'backend',
            'extensions': ['.py'],
            'name': 'Backend Python files'
        },
        {
            'directory': 'frontend/src',
            'extensions': ['.tsx', '.ts', '.js', '.jsx'],
            'name': 'Frontend TypeScript/JavaScript files'
        }
    ]
    
    all_issues = []
    
    for config in scan_configs:
        if os.path.exists(config['directory']):
            print(f"\n📁 Scanning {config['name']} in {config['directory']}...")
            issues = scan_directory(config['directory'], config['extensions'])
            all_issues.extend(issues)
            print(f"   Found {len(issues)} potential issues")
        else:
            print(f"⚠️  Directory {config['directory']} not found")
    
    # Report results
    print(f"\n{'='*60}")
    print(f"HARDCODED REFERENCE SCAN RESULTS")
    print(f"{'='*60}")
    
    if not all_issues:
        print("✅ No hardcoded references found!")
        print("\n🎉 The system is completely dynamic and repository-agnostic!")
        return
    
    # Group issues by type
    issues_by_type = {}
    for issue in all_issues:
        issue_type = issue['issue']
        if issue_type not in issues_by_type:
            issues_by_type[issue_type] = []
        issues_by_type[issue_type].append(issue)
    
    print(f"❌ Found {len(all_issues)} hardcoded references that need fixing:\n")
    
    for issue_type, issues in issues_by_type.items():
        print(f"🔸 {issue_type} ({len(issues)} occurrences):")
        for issue in issues:
            rel_path = os.path.relpath(issue['file'])
            print(f"   📄 {rel_path}:{issue['line']}")
            print(f"      {issue['content']}")
        print()
    
    print("🔧 RECOMMENDATIONS:")
    print("1. Replace hardcoded analysis IDs with dynamic queries (e.g., get latest completed analysis)")
    print("2. Remove hardcoded repository names and use current analysis data")
    print("3. Make all database queries dynamic based on analysis context")
    print("4. Ensure all validation scripts work with any repository")
    
    print(f"\n⚠️  Please fix these {len(all_issues)} issues to make the system fully dynamic!")

if __name__ == "__main__":
    main()
