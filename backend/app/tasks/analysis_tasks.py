from celery import current_task
from sqlalchemy.orm import Session
from datetime import datetime
import logging
import asyncio

from .celery_app import celery_app
from ..database import SessionLocal
from ..models import RepoAnalysis
from ..services.github_service import GitHubService
from ..services.analysis_service import AnalysisService, RepositoryAnalysisService

logger = logging.getLogger(__name__)


def get_db():
    db = SessionLocal()
    try:
        return db
    finally:
        pass  # Don't close here, will be closed in task


@celery_app.task(bind=True)
def start_repository_analysis(self, analysis_id: int, github_token: str):
    """
    Comprehensive Celery task to analyze a repository in the background
    Phase 3: Full multi-language dependency parsing, MCP integration, and scoring
    """
    db = get_db()
    
    try:
        # Get analysis record
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            logger.error(f"Analysis {analysis_id} not found")
            return {"error": "Analysis not found"}
        
        # Update status to analyzing
        analysis.status = "analyzing"
        db.commit()
        
        # Initialize task progress
        self.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 100, 'status': 'Starting comprehensive repository analysis...'}
        )
        
        # Initialize services
        repo_service = RepositoryAnalysisService()
        tavily_service = TavilyService()
        
        # Step 1: Repository Analysis (30%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 10, 'total': 100, 'status': 'Analyzing repository structure and dependencies...'}
        )
        
        # Run comprehensive repository analysis
        repo_url = analysis.repo_url
        analysis_results = asyncio.run(
            repo_service.analyze_repository(repo_url, github_token)
        )
        
        # Step 2: Save Dependencies (50%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 30, 'total': 100, 'status': 'Processing dependencies...'}
        )
        
        dependencies_data = analysis_results.get('dependencies', [])
        saved_dependencies = []
        
        # Save each dependency to database
        for dep_data in dependencies_data:
            dependency = Dependency(
                analysis_id=analysis_id,
                name=dep_data['name'],
                version=dep_data['version'],
                language=dep_data['language'],
                dependency_type=dep_data['dependency_type'],
                file_path=dep_data['file_path'],
                mcp_potential=dep_data.get('mcp_potential', 0.0)
            )
            db.add(dependency)
            saved_dependencies.append(dependency)
        
        db.commit()
        
        # Step 3: Enhanced Analysis (70%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 50, 'total': 100, 'status': 'Performing enhanced dependency analysis...'}
        )

        # Enhanced analysis without Context7 dependency
        try:
            # Basic MCP potential scoring based on dependency patterns
            for dep in saved_dependencies:
                # Simple scoring based on dependency type and usage patterns
                if any(keyword in dep.name.lower() for keyword in ['api', 'client', 'sdk', 'service']):
                    dep.mcp_potential = 0.8
                elif any(keyword in dep.name.lower() for keyword in ['database', 'db', 'orm']):
                    dep.mcp_potential = 0.7
                elif any(keyword in dep.name.lower() for keyword in ['http', 'request', 'fetch']):
                    dep.mcp_potential = 0.6
                else:
                    dep.mcp_potential = 0.4

            db.commit()

        except Exception as e:
            logger.warning(f"Enhanced analysis failed: {e}")
            # Continue with basic analysis
        
        # Step 4: Tavily MCP Server Discovery (85%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 70, 'total': 100, 'status': 'Discovering existing MCP servers...'}
        )
        
        # Search for MCP servers related to repository
        repo_name = analysis.repo_name
        primary_language = analysis_results.get('repository_info', {}).get('language', '')
        
        try:
            async def discover_servers():
                # Search for general MCP servers for this type of project
                search_query = f"{repo_name} {primary_language}"
                discovered_servers = await tavily_service.search_mcp_servers(search_query, max_results=10)
                
                # Search by category based on code structure
                code_structure = analysis_results.get('code_structure', {})
                categories_to_search = []
                
                if code_structure.get('has_database'):
                    categories_to_search.append('database')
                if code_structure.get('has_api') or analysis_results.get('api_endpoints'):
                    categories_to_search.append('api')
                if code_structure.get('has_cli'):
                    categories_to_search.append('cli')
                
                # Add category-specific servers
                for category in categories_to_search:
                    category_servers = await tavily_service.discover_mcp_servers_by_category(category)
                    discovered_servers.extend(category_servers)
                
                # Remove duplicates and limit results
                unique_servers = {}
                for server in discovered_servers:
                    url = server.get('url', '')
                    if url not in unique_servers:
                        unique_servers[url] = server
                
                return list(unique_servers.values())[:20]
            
            discovered_servers = asyncio.run(discover_servers())
            analysis_results['discovered_mcp_servers'] = discovered_servers
            
        except Exception as e:
            logger.warning(f"Tavily MCP discovery failed: {e}")
            analysis_results['discovered_mcp_servers'] = []
        finally:
            asyncio.run(tavily_service.close())
        
        # Step 5: Generate Recommendations (95%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 85, 'total': 100, 'status': 'Generating MCP recommendations...'}
        )
        
        # Generate implementation recommendations
        recommendations = generate_mcp_recommendations(
            analysis_results, 
            dependencies_data,
            analysis_results.get('discovered_mcp_servers', [])
        )
        analysis_results['recommendations'] = recommendations
        
        # Step 6: Finalize (100%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 95, 'total': 100, 'status': 'Finalizing analysis...'}
        )
        
        # Update analysis record with final results
        # analysis.mcp_feasibility_score removed - using conversational analysis instead
        analysis.analysis_results = analysis_results
        analysis.status = "completed"
        analysis.completed_at = datetime.utcnow()
        db.commit()
        
        # Complete
        self.update_state(
            state='SUCCESS',
            meta={'current': 100, 'total': 100, 'status': 'Comprehensive analysis completed successfully!'}
        )
        
        logger.info(f"Analysis {analysis_id} completed successfully")
        return {
            "analysis_id": analysis_id,
            "status": "completed",
            # "mcp_score" removed - using conversational analysis instead
            "dependencies_count": len(dependencies_data),
            "api_endpoints_count": len(analysis_results.get('api_endpoints', [])),
            "discovered_servers_count": len(analysis_results.get('discovered_mcp_servers', [])),
            "results": analysis_results
        }
        
    except Exception as exc:
        logger.error(f"Analysis {analysis_id} failed: {str(exc)}")
        
        # Update analysis status to failed
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if analysis:
            analysis.status = "failed"
            analysis.error_message = str(exc)
            db.commit()
        
        # Update task state
        self.update_state(
            state='FAILURE',
            meta={'current': 0, 'total': 100, 'status': f'Analysis failed: {str(exc)}'}
        )
        
        raise exc
    
    finally:
        db.close()


def generate_mcp_recommendations(analysis_results, dependencies, discovered_servers):
    """Generate MCP server implementation recommendations"""
    recommendations = []
    
    # mcp_score removed - using conversational analysis instead
    mcp_score = 0  # Deprecated
    code_structure = analysis_results.get('code_structure', {})
    api_endpoints = analysis_results.get('api_endpoints', [])
    
    # High-level recommendation based on MCP score
    if mcp_score >= 75:
        recommendations.append({
            "type": "implementation",
            "priority": "high",
            "title": "Excellent MCP Server Candidate",
            "description": "This repository shows excellent potential for MCP server implementation with rich functionality and good structure.",
            "effort": "medium",
            "timeline": "2-4 weeks"
        })
    elif mcp_score >= 50:
        recommendations.append({
            "type": "implementation",
            "priority": "medium", 
            "title": "Good MCP Server Potential",
            "description": "This repository has good potential for MCP server implementation with some enhancements.",
            "effort": "medium-high",
            "timeline": "4-6 weeks"
        })
    else:
        recommendations.append({
            "type": "enhancement",
            "priority": "low",
            "title": "Consider Enhancements First",
            "description": "Consider adding more structured APIs or CLI interfaces before implementing as MCP server.",
            "effort": "high",
            "timeline": "6-8 weeks"
        })
    
    # Specific recommendations based on analysis
    if api_endpoints:
        recommendations.append({
            "type": "api",
            "priority": "high",
            "title": "Leverage Existing API Endpoints",
            "description": f"Found {len(api_endpoints)} API endpoints that can be exposed through MCP protocol.",
            "effort": "low",
            "timeline": "1-2 weeks",
            "details": [endpoint['path'] for endpoint in api_endpoints[:5]]
        })
    
    if code_structure.get('has_cli'):
        recommendations.append({
            "type": "cli",
            "priority": "high",
            "title": "Expose CLI Commands via MCP",
            "description": "Existing CLI interface can be wrapped with MCP protocol for remote execution.",
            "effort": "low-medium",
            "timeline": "1-3 weeks"
        })
    
    if code_structure.get('has_database'):
        recommendations.append({
            "type": "database",
            "priority": "medium",
            "title": "Database Integration Opportunities",
            "description": "Database functionality can be exposed through MCP for data access and manipulation.",
            "effort": "medium",
            "timeline": "2-4 weeks"
        })
    
    # Recommendations based on discovered servers
    if discovered_servers:
        similar_servers = [s for s in discovered_servers if s.get('relevance_score', 0) > 0.7]
        if similar_servers:
            recommendations.append({
                "type": "reference",
                "priority": "medium",
                "title": "Learn from Similar MCP Servers",
                "description": f"Found {len(similar_servers)} similar MCP servers to use as reference.",
                "effort": "low",
                "timeline": "research phase",
                "details": [s.get('name', 'Unknown') for s in similar_servers[:3]]
            })
    
    # Language-specific recommendations
    primary_language = analysis_results.get('repository_info', {}).get('language', '').lower()
    if primary_language == 'python':
        recommendations.append({
            "type": "framework",
            "priority": "medium",
            "title": "Use Python MCP Framework",
            "description": "Consider using existing Python MCP frameworks like 'mcp-python' for faster development.",
            "effort": "medium",
            "timeline": "integrated into development"
        })
    
    return recommendations