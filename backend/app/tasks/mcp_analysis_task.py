"""
MCP Analysis Celery Task
"""
import asyncio
import logging
import json
import redis
import os
from celery import current_task
from sqlalchemy.orm import Session
from datetime import datetime

from .celery_app import celery_app
from ..database import SessionLocal
from ..models import RepoAnalysis
from ..services.mcp_analysis_service import MCPAnalysisService

logger = logging.getLogger(__name__)

# Redis client for shared storage
redis_client = redis.Redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379/0'))


@celery_app.task(bind=True, max_retries=2)
def analyze_mcp_opportunities_task(self, analysis_id: int, repo_url: str):
    """
    Analyze repository for MCP opportunities using Anthropic and Tavily
    """
    try:
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 10,
                'total': 100,
                'status': 'Initializing MCP analysis...',
                'current_task': 'mcp_init'
            }
        )

        # Initialize MCP analysis service
        mcp_service = MCPAnalysisService()
        
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 30,
                'total': 100,
                'status': 'Analyzing repository for MCP opportunities...',
                'current_task': 'mcp_analysis'
            }
        )

        # Get comprehensive context from the analysis
        try:
            from ..database import SessionLocal
            from ..models import RepoAnalysis

            db = SessionLocal()
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

            if analysis and analysis.analysis_results:
                # Create comprehensive context using the same logic as sequential workflow
                context_parts = [
                    f"Repository: {analysis.repo_url}",
                    f"Name: {analysis.repo_name}",
                    f"Owner: {analysis.repo_owner}",
                    f"Status: {analysis.status}",
                ]

                # Generic repository context - no hardcoded repository-specific logic
                context_parts.extend([
                    "",
                    "=== REPOSITORY ANALYSIS CONTEXT ===",
                    "",
                    f"Repository: {analysis.repo_owner}/{analysis.repo_name}",
                    f"Description: {repo_info.get('description', 'No description available')}",
                    f"Primary Language: {repo_info.get('language', 'Unknown')}",
                    f"Repository URL: {repo_info.get('html_url', 'N/A')}",
                    "",
                    "This analysis examines the repository's actual codebase to suggest relevant MCP tools and integrations."
                ])

                comprehensive_context = "\n".join(context_parts)

                # Run MCP analysis with comprehensive context
                mcp_results = asyncio.run(
                    mcp_service.analyze_mcp_opportunities_with_context(repo_url, comprehensive_context)
                )
            else:
                # Fallback to basic analysis
                mcp_results = asyncio.run(
                    mcp_service.analyze_mcp_opportunities(repo_url, analysis_id)
                )

            db.close()

        except Exception as context_error:
            logger.error(f"Error creating comprehensive context: {context_error}")
            # Fallback to basic analysis
            mcp_results = asyncio.run(
                mcp_service.analyze_mcp_opportunities(repo_url, analysis_id)
            )

        self.update_state(
            state='PROGRESS',
            meta={
                'current': 80,
                'total': 100,
                'status': 'Storing MCP analysis results...',
                'current_task': 'mcp_storage'
            }
        )

        # Store results in Redis
        cache_key = f"mcp_analysis:{analysis_id}"
        try:
            # Convert MCPAnalysisResult to dict for JSON serialization
            result_dict = {
                'executive_summary': mcp_results.executive_summary,
                'capability_matrix': [
                    {
                        'capability': cap.capability,
                        'underlying_tech': cap.underlying_tech,
                        'exposed_via_api': cap.exposed_via_api,
                        'candidate_tool_name': cap.candidate_tool_name
                    }
                    for cap in mcp_results.capability_matrix
                ],
                'new_mcp_server_specs': [
                    {
                        'name': spec.name,
                        'description': spec.description,
                        'parameters': spec.parameters
                    }
                    for spec in mcp_results.new_mcp_server_specs
                ],
                'existing_mcp_servers': [
                    {
                        'server_name': server.server_name,
                        'overlapping_tools': server.overlapping_tools,
                        'when_to_reuse': server.when_to_reuse
                    }
                    for server in mcp_results.existing_mcp_servers
                ],
                'gap_analysis': mcp_results.gap_analysis,
                'implementation_starter': mcp_results.implementation_starter,
                'client_config_snippet': mcp_results.client_config_snippet
            }
            redis_client.setex(cache_key, 3600, json.dumps(result_dict))  # Cache for 1 hour
            logger.info(f"MCP analysis results stored in Redis for analysis {analysis_id}")
        except Exception as cache_error:
            logger.error(f"Failed to store MCP analysis in Redis: {cache_error}")
        
        self.update_state(
            state='SUCCESS',
            meta={
                'current': 100,
                'total': 100,
                'status': 'MCP analysis completed successfully',
                'current_task': 'mcp_complete',
                'results': {
                    'executive_summary': mcp_results.executive_summary,
                    'capability_count': len(mcp_results.capability_matrix),
                    'existing_servers_count': len(mcp_results.existing_mcp_servers),
                    'has_implementation_starter': bool(mcp_results.implementation_starter),
                    'has_client_config': bool(mcp_results.client_config_snippet)
                }
            }
        )

        logger.info(f"MCP analysis completed successfully for analysis {analysis_id}")
        
        return {
            'analysis_id': analysis_id,
            'status': 'completed',
            'executive_summary': mcp_results.executive_summary,
            'capability_count': len(mcp_results.capability_matrix),
            'existing_servers_count': len(mcp_results.existing_mcp_servers)
        }

    except Exception as e:
        logger.error(f"MCP analysis failed for analysis {analysis_id}: {str(e)}")
        
        self.update_state(
            state='FAILURE',
            meta={
                'current': 100,
                'total': 100,
                'status': f'MCP analysis failed: {str(e)}',
                'current_task': 'mcp_error',
                'error': str(e)
            }
        )
        
        # Store error result in Redis
        cache_key = f"mcp_analysis:{analysis_id}"
        error_result = {
            'executive_summary': f"MCP analysis failed: {str(e)}",
            'capability_matrix': [],
            'new_mcp_server_specs': [],
            'existing_mcp_servers': [],
            'gap_analysis': "Analysis could not be completed due to an error.",
            'implementation_starter': "# Analysis failed",
            'client_config_snippet': '{"error": "Analysis failed"}'
        }
        try:
            redis_client.setex(cache_key, 3600, json.dumps(error_result))
        except Exception as cache_error:
            logger.error(f"Failed to store error result in Redis: {cache_error}")
        
        raise


def get_mcp_analysis_result(analysis_id: int):
    """Get MCP analysis result from Redis cache"""
    try:
        cache_key = f"mcp_analysis:{analysis_id}"
        cached_data = redis_client.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
        return None
    except Exception as e:
        logger.error(f"Failed to get MCP analysis from Redis: {e}")
        return None


def clear_mcp_analysis_result(analysis_id: int):
    """Clear MCP analysis result from Redis cache"""
    try:
        cache_key = f"mcp_analysis:{analysis_id}"
        redis_client.delete(cache_key)
    except Exception as e:
        logger.error(f"Failed to clear MCP analysis from Redis: {e}")
