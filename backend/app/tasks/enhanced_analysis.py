"""
Enhanced repository analysis task with AI-powered business logic understanding
"""
from celery import current_task
from sqlalchemy.orm import Session
from datetime import datetime
import logging
import json
import asyncio
from typing import Dict, Any, Optional

from .celery_app import celery_app
from ..database import SessionLocal
from ..models import RepoAnalysis
from ..services.intelligent_analysis_service import IntelligentAnalysisService
from ..services.indexing_service import IndexingService
from ..services.updated_analysis_workflow import UpdatedAnalysisWorkflow
from ..services.enhanced_mcp_suggestions import EnhancedMCPSuggestionService
from ..services.functional_mcp_generator import FunctionalMCPGenerator
from ..services.mcp_analysis_service import MCPAnalysisService
from ..config import settings

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, max_retries=2)
def analyze_repository_with_enhanced_mcps(self, analysis_id: int, github_token: str):
    """
    Enhanced repository analysis with functional MCP generation
    """
    db = SessionLocal()
    try:
        # Get analysis record
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            logger.error(f"Analysis {analysis_id} not found")
            return {"error": "Analysis not found"}

        # Update status
        analysis.status = "analyzing"
        db.commit()

        logger.info(f"Starting enhanced analysis for {analysis.repo_owner}/{analysis.repo_name}")

        # Step 1: Index repository code FIRST for better AI context (20-40%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 20,
                'total': 100,
                'status': f'Analyzing {analysis.repo_owner}/{analysis.repo_name} - Indexing repository code...',
                'current_task': 'code_indexing',
                'details': f'Creating searchable index for {analysis.repo_owner}/{analysis.repo_name}'
            }
        )

        # Perform initial repository content gathering for indexing
        intelligent_service = IntelligentAnalysisService()
        initial_repo_content = asyncio.run(intelligent_service._gather_repository_content(
            analysis.repo_owner, analysis.repo_name, github_token
        ))

        # Index the code first to provide context for AI analysis
        try:
            indexing_service = IndexingService()
            indexing_result = asyncio.run(indexing_service.index_repository(analysis.id, initial_repo_content))
            logger.info(f"Code indexing completed: {indexing_result}")
        except Exception as indexing_error:
            logger.warning(f"Code indexing failed (continuing with analysis): {str(indexing_error)}")

        # Step 2: Code indexing and preparation (30-40%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 30,
                'total': 100,
                'status': 'Indexing code and preparing for analysis...',
                'current_task': 'code_indexing',
                'details': 'Indexing repository code for comprehensive analysis'
            }
        )

        # Step 3: Enhanced analysis with functional MCP generation (40-90%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 40,
                'total': 100,
                'status': f'Analyzing {analysis.repo_owner}/{analysis.repo_name} - Extracting business logic and APIs...',
                'current_task': 'enhanced_analysis',
                'details': 'Analyzing repository and generating functional MCP tools'
            }
        )

        # Initialize the enhanced workflow
        workflow = UpdatedAnalysisWorkflow()

        # Run the enhanced analysis
        enhanced_results = asyncio.run(
            workflow.analyze_repository_with_functional_mcps(
                analysis.repo_owner,
                analysis.repo_name,
                github_token,
                {
                    'name': analysis.repo_name,
                    'owner': analysis.repo_owner,
                    'url': analysis.repo_url
                }
            )
        )

        # Step 3: MCP Analysis using indexed code and repo analysis (80-90%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 80,
                'total': 100,
                'status': f'Analyzing {analysis.repo_owner}/{analysis.repo_name} - Generating MCP tool suggestions...',
                'current_task': 'mcp_analysis',
                'details': 'Analyzing repository for MCP opportunities using indexed code and analysis results'
            }
        )

        # Run MCP analysis with rich context
        try:
            mcp_service = MCPAnalysisService()

            # Create comprehensive context inline
            context_parts = [
                f"Repository: {analysis.repo_url}",
                f"Name: {analysis.repo_name}",
                f"Owner: {analysis.repo_owner}",
                f"Status: {analysis.status}",
            ]

            # Debug: Log what's in enhanced_results
            logger.info(f"DEBUG: Enhanced results keys: {list(enhanced_results.keys()) if enhanced_results else 'None'}")
            if enhanced_results:
                logger.info(f"DEBUG: Enhanced results sample: {str(enhanced_results)[:1000]}...")

            # Add comprehensive enhanced analysis results
            if enhanced_results:
                # Repository information
                if 'repository_info' in enhanced_results:
                    repo_info = enhanced_results['repository_info']
                    if repo_info.get('language'):
                        context_parts.append(f"Primary Language: {repo_info['language']}")

                # Comprehensive analysis data
                if 'comprehensive_analysis' in enhanced_results:
                    comp_analysis = enhanced_results['comprehensive_analysis']

                    # Business logic
                    if 'business_logic' in comp_analysis:
                        business_logic = comp_analysis['business_logic']
                        if business_logic.get('primary_domain'):
                            context_parts.append(f"Business Domain: {business_logic['primary_domain']}")
                        if business_logic.get('business_purpose'):
                            context_parts.append(f"Business Purpose: {business_logic['business_purpose']}")
                        if business_logic.get('value_proposition'):
                            context_parts.append(f"Value Proposition: {business_logic['value_proposition']}")
                        if business_logic.get('core_operations'):
                            ops = business_logic['core_operations'][:5]
                            context_parts.append(f"Core Operations: {', '.join([str(op) for op in ops])}")
                        if business_logic.get('business_entities'):
                            entities = business_logic['business_entities'][:5]
                            context_parts.append(f"Business Entities: {', '.join([str(e) for e in entities])}")

                    # API capabilities
                    if 'api_capabilities' in comp_analysis:
                        api_caps = comp_analysis['api_capabilities']
                        if api_caps.get('existing_apis'):
                            apis = api_caps['existing_apis'][:5]
                            api_names = [api.get('name', api.get('endpoint', 'API')) for api in apis]
                            context_parts.append(f"Existing APIs ({len(apis)}): {', '.join(api_names)}")
                        if api_caps.get('api_patterns'):
                            patterns = api_caps['api_patterns'][:3]
                            context_parts.append(f"API Patterns: {', '.join(patterns)}")

                    # Integration opportunities
                    if 'integration_opportunities' in comp_analysis:
                        integrations = comp_analysis['integration_opportunities']
                        if integrations.get('third_party_services'):
                            services = integrations['third_party_services'][:5]
                            context_parts.append(f"Third-party Services: {', '.join([str(s) for s in services])}")

                # Detected integrations
                if 'detected_integrations' in enhanced_results:
                    integrations = enhanced_results['detected_integrations'][:5]
                    integration_names = [
                        integration.get('name', integration.get('service', integration.get('type', 'Integration')))
                        for integration in integrations
                    ]
                    context_parts.append(f"Detected Integrations ({len(integrations)}): {', '.join(integration_names)}")

                # Specific MCP tools already identified
                if 'specific_mcp_tools' in enhanced_results:
                    tools = enhanced_results.get('specific_mcp_tools', [])
                    if tools:
                        tool_names = [tool.get('name', 'Unknown') for tool in tools[:5]]
                        context_parts.append(f"Generated MCP Tools ({len(tools)}): {', '.join(tool_names)}")

                # Analysis metadata
                if 'analysis_metadata' in enhanced_results:
                    metadata = enhanced_results['analysis_metadata']
                    if metadata.get('files_analyzed'):
                        context_parts.append(f"Files Analyzed: {metadata['files_analyzed']}")
                    if metadata.get('repository_size'):
                        size_kb = metadata['repository_size'] / 1024
                        context_parts.append(f"Repository Size: {size_kb:.1f} KB")

            # Generic repository analysis - no hardcoded repository-specific logic
            context_parts.extend([
                "",
                "=== REPOSITORY ANALYSIS CONTEXT ===",
                "",
                f"Repository: {analysis.repo_owner}/{analysis.repo_name}",
                f"Description: {repo_info.get('description', 'No description available')}",
                f"Primary Language: {repo_info.get('language', 'Unknown')}",
                f"Stars: {repo_info.get('stargazers_count', 0)}",
                f"Forks: {repo_info.get('forks_count', 0)}",
                f"Topics: {', '.join(repo_info.get('topics', []))}",
                "",
                "This analysis will examine the repository's structure, dependencies, APIs, and integration patterns",
                "to suggest relevant MCP (Model Context Protocol) tools and integrations based on the actual codebase."
            ])

            comprehensive_context = "\n".join(context_parts)

            # Run MCP analysis with rich context
            mcp_results = asyncio.run(
                mcp_service.analyze_mcp_opportunities_with_context(
                    analysis.repo_url,
                    comprehensive_context
                )
            )

            # Store MCP analysis results
            cache_key = f"mcp_analysis:{analysis.id}"
            result_dict = {
                'executive_summary': mcp_results.executive_summary,
                'capability_matrix': [
                    {
                        'capability': cap.capability,
                        'underlying_tech': cap.underlying_tech,
                        'exposed_via_api': cap.exposed_via_api,
                        'candidate_tool_name': cap.candidate_tool_name
                    }
                    for cap in mcp_results.capability_matrix
                ],
                'new_mcp_server_specs': [
                    {
                        'name': spec.name,
                        'description': spec.description,
                        'parameters': spec.parameters
                    }
                    for spec in mcp_results.new_mcp_server_specs
                ],
                'existing_mcp_servers': [
                    {
                        'server_name': server.server_name,
                        'overlapping_tools': server.overlapping_tools,
                        'when_to_reuse': server.when_to_reuse
                    }
                    for server in mcp_results.existing_mcp_servers
                ],
                'gap_analysis': mcp_results.gap_analysis,
                'implementation_starter': mcp_results.implementation_starter,
                'client_config_snippet': mcp_results.client_config_snippet
            }

            # Store in Redis
            import redis
            import json
            import os
            redis_client = redis.Redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379/0'))
            redis_client.setex(cache_key, 3600, json.dumps(result_dict))

            logger.info(f"MCP analysis completed and stored for analysis {analysis.id}")

        except Exception as mcp_error:
            logger.error(f"MCP analysis failed for analysis {analysis.id}: {str(mcp_error)}")
            # Don't fail the entire analysis, but log the error

        # Step 4: Finalize analysis (90-100%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 90,
                'total': 100,
                'status': 'Finalizing enhanced analysis...',
                'current_task': 'finalization',
                'details': 'Saving results and completing analysis'
            }
        )

        # CRITICAL: Ensure we have specific tools
        specific_tools = enhanced_results.get('specific_mcp_tools', [])
        if not specific_tools:
            logger.warning(f"No specific tools generated for {analysis_id}")
            # Don't fail the analysis, but note this
            enhanced_results['specific_mcp_tools'] = []

        # Update analysis with enhanced results
        analysis.analysis_results = enhanced_results
        analysis.mcp_suggestions = enhanced_results.get('specific_mcp_tools', [])
        analysis.specific_tools = enhanced_results.get('specific_mcp_tools', [])
        analysis.functional_server_config = enhanced_results.get('functional_mcp_server', {})
        analysis.implementation_roadmap = enhanced_results.get('implementation_roadmap', {})
        analysis.enhanced_metrics = enhanced_results.get('enhanced_metrics', {})
        analysis.tools_confidence_score = enhanced_results.get('enhanced_metrics', {}).get('confidence_score', 0.0)
        # analysis.mcp_feasibility_score removed - using conversational analysis instead

        # Update status
        analysis.status = "completed"
        analysis.completed_at = datetime.utcnow()
        db.commit()

        # Final progress update
        self.update_state(
            state='SUCCESS',
            meta={
                'current': 100,
                'total': 100,
                'status': 'Enhanced analysis completed successfully!',
                'current_task': 'completed',
                'details': f'Generated {len(enhanced_results.get("specific_mcp_tools", []))} specific tools with functional server code'
            }
        )

        logger.info(f"Enhanced analysis completed for {analysis.repo_owner}/{analysis.repo_name}")
        logger.info(f"Generated {len(enhanced_results.get('specific_mcp_tools', []))} specific tools")

        return {
            "status": "completed",
            "analysis_id": analysis_id,
            "tools_count": len(specific_tools),
            "confidence_score": enhanced_results.get('enhanced_metrics', {}).get('confidence_score', 0.0),
            "server_generated": bool(enhanced_results.get('functional_mcp_server')),
            "has_roadmap": bool(enhanced_results.get('implementation_roadmap'))
        }

    except Exception as e:
        logger.error(f"Enhanced analysis failed for analysis {analysis_id}: {str(e)}")
        logger.exception("Full error details:")

        # Update analysis with error
        analysis.status = "failed"
        analysis.error_message = str(e)
        db.commit()

        # Retry if possible
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying enhanced analysis for analysis {analysis_id} (attempt {self.request.retries + 1})")
            raise self.retry(countdown=60 * (self.request.retries + 1))

        return {"error": str(e), "analysis_id": analysis_id}

    finally:
        db.close()


@celery_app.task(bind=True, max_retries=2)
def analyze_repository_enhanced(self, analysis_id: int, github_token: str):
    """
    Enhanced repository analysis task with AI-powered business logic understanding
    """
    db = SessionLocal()
    try:
        # Get analysis record
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            raise ValueError(f"Analysis {analysis_id} not found")
        
        # Check if any AI API key is available
        if not settings.anthropic_api_key and not settings.openai_api_key:
            logger.warning("No AI API key configured, falling back to basic analysis")
            # Import and run basic analysis as fallback
            from .real_analysis import analyze_repository_real
            return analyze_repository_real(analysis_id, github_token)
        
        # Update status to analyzing
        analysis.status = "analyzing" 
        db.commit()
        
        # Step 1: Initialize (5%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 5, 'total': 100, 'status': 'Initializing AI-powered analysis...'}
        )
        
        intelligent_service = IntelligentAnalysisService()
        
        # Step 2: Gather repository metadata (15%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 15, 'total': 100, 'status': 'Fetching repository metadata...'}
        )
        
        # Step 3: Analyze repository structure (25%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 25, 'total': 100, 'status': 'Analyzing repository structure...'}
        )
        
        # Step 4: Index repository code FIRST for better AI context (40-60%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 40,
                'total': 100,
                'status': 'Indexing repository code for enhanced analysis...',
                'current_task': 'code_indexing',
                'details': f'Creating searchable index for {analysis.repo_owner}/{analysis.repo_name}'
            }
        )

        # Perform initial repository content gathering for indexing
        intelligent_service = IntelligentAnalysisService()
        initial_repo_content = asyncio.run(intelligent_service._gather_repository_content(
            analysis.repo_owner, analysis.repo_name, github_token
        ))

        # Index the code first to provide context for AI analysis
        try:
            indexing_service = IndexingService()
            indexing_result = asyncio.run(indexing_service.index_repository(analysis.id, initial_repo_content))
            logger.info(f"Code indexing completed: {indexing_result}")
        except Exception as indexing_error:
            logger.warning(f"Code indexing failed (continuing with analysis): {str(indexing_error)}")

        # Step 5: AI business logic analysis with indexed context (60-90%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 60,
                'total': 100,
                'status': f'Analyzing {analysis.repo_owner}/{analysis.repo_name} - AI-powered code analysis...',
                'current_task': 'ai_analysis',
                'details': 'Analyzing business logic, workflows, and generating MCP suggestions using indexed code'
            }
        )
        
        try:
            logger.info("=== CALLING COMPREHENSIVE ANALYSIS (MAIN) ===")
            logger.info(f"Repo: {analysis.repo_owner}/{analysis.repo_name}")

            comprehensive_analysis = asyncio.run(intelligent_service.analyze_repository_comprehensively(
                analysis.repo_owner,
                analysis.repo_name,
                github_token
            ))

            logger.info("=== COMPREHENSIVE ANALYSIS COMPLETED (MAIN) ===")
        except Exception as ai_error:
            logger.error("=== COMPREHENSIVE ANALYSIS FAILED (MAIN) ===")
            logger.error(f"Error: {str(ai_error)}")
            logger.error(f"Error type: {type(ai_error)}")

            # Check if it's the slice error specifically
            if "unhashable type: 'slice'" in str(ai_error):
                logger.error("=== DETECTED SLICE ERROR IN MAIN ANALYSIS ===")
                import traceback
                logger.error(f"Full traceback: {traceback.format_exc()}")

            # Continue with the existing error handling
            logger.error(f"AI analysis failed: {str(ai_error)}, falling back to basic analysis")
            # Fallback to basic analysis - implement directly to avoid context issues
            self.update_state(
                state='PROGRESS',
                meta={'current': 50, 'total': 100, 'status': 'Falling back to basic analysis...'}
            )
            
            # Try to gather at least repository tree data for the fallback
            try:
                basic_repo_content = asyncio.run(intelligent_service._gather_repository_content(
                    analysis.repo_owner,
                    analysis.repo_name,
                    github_token
                ))
                repository_tree = basic_repo_content.get("repository_tree", [])
                repository_info = basic_repo_content.get("repository_info", {})
            except Exception as content_error:
                logger.warning(f"Failed to gather basic repository content: {str(content_error)}")
                repository_tree = []
                repository_info = {}
            
            # Use basic scoring instead of AI analysis
            basic_results = {
                "analysis_type": "basic_fallback",
                "fallback_reason": str(ai_error),
                "basic_analysis": True,
                "repository_tree": repository_tree,
                "repository_info": repository_info
            }
            
            # Calculate a basic MCP score (simplified)
            basic_mcp_score = 30.0  # Default fallback score
            
            # Update analysis record with basic results
            # analysis.mcp_feasibility_score removed - using conversational analysis instead
            analysis.analysis_results = basic_results
            analysis.status = "completed"
            analysis.completed_at = datetime.utcnow()
            db.commit()
            
            # Complete (100%)
            self.update_state(
                state='SUCCESS',
                meta={
                    'current': 100, 
                    'total': 100, 
                    'status': 'Basic analysis completed (AI analysis failed)'
                }
            )
            
            logger.info(f"Basic fallback analysis {analysis_id} completed with MCP score: {basic_mcp_score}")
            
            return {
                "analysis_id": analysis_id,
                "status": "completed",
                "analysis_type": "basic_fallback",
                "mcp_score": basic_mcp_score,
                "tools_suggested": 0,
                "confidence_score": 0.3,
                "ready_for_generation": False,
                "fallback_reason": str(ai_error)
            }
        
        # Step 6: Generating MCP tool suggestions (75%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 75, 'total': 100, 'status': 'Generating MCP tool suggestions...'}
        )
        
        # Step 7: MCP feasibility scoring removed - using conversational analysis instead
        self.update_state(
            state='PROGRESS',
            meta={'current': 85, 'total': 100, 'status': 'Finalizing enhanced analysis...'}
        )

        enhanced_mcp_score = 0  # Deprecated - using conversational analysis instead
        
        # Step 8: Preparing recommendations (92%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 92, 'total': 100, 'status': 'Preparing implementation recommendations...'}
        )
        
        # Step 9: Finalize results (98%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 98, 'total': 100, 'status': 'Finalizing enhanced analysis results...'}
        )
        
        # Transform AI analysis data for frontend compatibility
        def transform_ai_data_for_frontend(ai_data):
            """Transform AI analysis data to frontend-expected format"""
            comprehensive = ai_data.get("comprehensive_analysis", {})
            mcp_suggestions = ai_data.get("mcp_suggestions", {})
            
            # Create recommendations from MCP suggestions
            recommendations = []
            categories = mcp_suggestions.get("categories", {})
            
            for category, tools in categories.items():
                if tools:
                    for tool in tools:
                        rec = {
                            "title": tool.get("name", "Unknown Tool"),
                            "description": tool.get("description", "No description available"),
                            "type": "implementation",
                            "priority": "high" if tool.get("business_value") else "medium",
                            "confidence": "high",
                            "reasoning": [
                                f"Business value: {tool.get('business_value', 'Not specified')}",
                                f"Implementation effort: {tool.get('implementation_effort', 'unknown')}",
                                f"Category: {category.replace('_', ' ').title()}"
                            ],
                            "tools": [tool],
                            "use_cases": tool.get("use_cases", []),
                            "effort": tool.get("implementation_effort", "medium"),
                            "timeline": "Post-implementation"
                        }
                        recommendations.append(rec)
            
            # Get repository tree from the analysis data
            repository_tree = ai_data.get("repository_tree", [])
            
            # Create code structure summary
            # Handle both direct and nested comprehensive analysis structure
            comp_analysis = comprehensive.get("comprehensive_analysis", comprehensive)
            business_logic = comp_analysis.get("business_logic", {})
            api_capabilities = comp_analysis.get("api_capabilities", {})
            integration_opportunities = comp_analysis.get("integration_opportunities", {})
            
            # Safely check for database indicators
            has_database = False
            try:
                has_database = (
                    "database" in str(integration_opportunities).lower() or 
                    "database" in str(api_capabilities).lower() or
                    "sql" in str(api_capabilities).lower() or
                    "postgres" in str(api_capabilities).lower() or
                    "mysql" in str(api_capabilities).lower()
                )
            except Exception:
                has_database = False
            
            code_structure = {
                "languages": {"Primary": business_logic.get("primary_domain", "Unknown")},
                "has_cli": bool(api_capabilities.get("existing_apis")),
                "has_api": bool(api_capabilities.get("existing_apis")),
                "has_database": has_database,
                "file_count": ai_data.get("analysis_metadata", {}).get("files_analyzed", 0)
            }
            
            return {
                "recommendations": recommendations,
                "repository_tree": repository_tree,
                "code_structure": code_structure
            }
        
        frontend_data = transform_ai_data_for_frontend(comprehensive_analysis)
        
        # Prepare comprehensive analysis results
        enhanced_results = {
            "analysis_type": "ai_enhanced",
            "ai_analysis": comprehensive_analysis,
            "enhanced_mcp_score": enhanced_mcp_score,
            "generation_ready": True,
            "tool_suggestions_available": True,
            "business_logic_analyzed": True,
            # Add frontend-compatible data
            **frontend_data,
            # Add MCP suggestions API compatibility
            "ai_suggestions": comprehensive_analysis.get("mcp_suggestions", {}),
            "business_analysis": comprehensive_analysis.get("comprehensive_analysis", {}),
            "confidence_score": comprehensive_analysis.get("confidence_score", 0.0),
            "implementation_complexity": comprehensive_analysis.get("implementation_complexity", {})
        }
        
        # Update analysis record with enhanced results
        # analysis.mcp_feasibility_score removed - using conversational analysis instead
        analysis.analysis_results = enhanced_results
        db.commit()

        # Step 6: Finalization (95%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 95, 'total': 100, 'status': 'Finalizing analysis results...'}
        )

        # Code indexing was already completed in Step 4 before AI analysis
        logger.info("Analysis completed with pre-indexed code context")

        # Mark analysis as completed
        analysis.status = "completed"
        analysis.completed_at = datetime.utcnow()
        db.commit()

        # Complete (100%)
        self.update_state(
            state='SUCCESS',
            meta={
                'current': 100,
                'total': 100,
                'status': 'AI-powered analysis completed! MCP tool suggestions available.'
            }
        )
        
        logger.info(f"Enhanced analysis {analysis_id} completed with MCP score: {enhanced_mcp_score}")
        
        return {
            "analysis_id": analysis_id,
            "status": "completed",
            "analysis_type": "ai_enhanced",
            "mcp_score": enhanced_mcp_score,
            "tools_suggested": comprehensive_analysis.get("mcp_suggestions", {}).get("total_tools_suggested", 0),
            "confidence_score": comprehensive_analysis.get("confidence_score", 0.0),
            "ready_for_generation": True,
            "business_domain": comprehensive_analysis.get("comprehensive_analysis", {}).get("business_logic", {}).get("primary_domain", "unknown")
        }
        
    except Exception as exc:
        logger.error(f"Enhanced analysis {analysis_id} failed: {str(exc)}")
        
        # Update analysis status to failed
        try:
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "failed"
                analysis.error_message = str(exc)
                db.commit()
        except:
            pass
        
        # Update task state
        self.update_state(
            state='FAILURE',
            meta={'current': 0, 'total': 100, 'status': f'Enhanced analysis failed: {str(exc)}'}
        )
        
        raise exc
    
    finally:
        db.close()


# MCP feasibility scoring removed - using conversational analysis instead
def calculate_enhanced_mcp_score(comprehensive_analysis: Dict[str, Any]) -> float:
    """
    DEPRECATED: Calculate enhanced MCP feasibility score based on AI analysis
    """
    try:
        score = 0.0
        
        # Get analysis components
        # Handle both direct and nested comprehensive analysis structure
        comp_analysis = comprehensive_analysis.get("comprehensive_analysis", comprehensive_analysis)
        business_logic = comp_analysis.get("business_logic", {})
        workflows = comp_analysis.get("workflows", {})
        api_capabilities = comp_analysis.get("api_capabilities", {})
        integration_opportunities = comp_analysis.get("integration_opportunities", {})
        mcp_suggestions = comprehensive_analysis.get("mcp_suggestions", {})
        
        # 1. Business Logic Quality (25 points)
        core_operations = business_logic.get("core_operations", [])
        if len(core_operations) > 0:
            score += min(len(core_operations) * 3, 15)  # Up to 15 points for operations
        
        if business_logic.get("primary_domain") != "unknown":
            score += 5  # 5 points for clear domain identification
        
        if len(business_logic.get("use_cases", [])) > 0:
            score += 5  # 5 points for clear use cases
        
        # 2. API and Integration Capabilities (25 points)
        existing_apis = api_capabilities.get("existing_apis", [])
        score += min(len(existing_apis) * 2, 10)  # Up to 10 points for existing APIs
        
        potential_apis = api_capabilities.get("potential_new_apis", [])
        score += min(len(potential_apis) * 1, 5)  # Up to 5 points for potential APIs
        
        external_integrations = integration_opportunities.get("external_services", [])
        score += min(len(external_integrations) * 2, 10)  # Up to 10 points for integrations
        
        # 3. Workflow Automation Potential (20 points)
        automation_opportunities = workflows.get("automation_opportunities", [])
        score += min(len(automation_opportunities) * 2, 10)  # Up to 10 points for automation
        
        business_processes = workflows.get("business_processes", [])
        score += min(len(business_processes) * 1, 10)  # Up to 10 points for processes
        
        # 4. MCP Tool Suggestions Quality (20 points)
        total_tools = mcp_suggestions.get("total_tools_suggested", 0)
        score += min(total_tools * 0.5, 15)  # Up to 15 points for tool variety
        
        quick_wins = len(mcp_suggestions.get("implementation_roadmap", {}).get("phase_1_quick_wins", []))
        if quick_wins > 0:
            score += 5  # 5 points for having quick wins
        
        # 5. Implementation Analysis (deprecated - using conversational analysis instead)
        complexity = comprehensive_analysis.get("implementation_complexity", {})
        total_effort = complexity.get("total_estimated_hours", 200)
        
        if total_effort <= 40:
            score += 10  # Very feasible
        elif total_effort <= 80:
            score += 7   # Moderately feasible
        elif total_effort <= 120:
            score += 5   # Challenging but doable
        else:
            score += 2   # Complex implementation
        
        # Normalize to 0-100 range
        final_score = min(max(score, 0.0), 100.0)
        
        logger.info(f"Enhanced MCP score calculated: {final_score}")
        return final_score
        
    except Exception as e:
        logger.error(f"Error calculating enhanced MCP score: {str(e)}")
        # Return a reasonable default score if calculation fails
        return 50.0


@celery_app.task(bind=True, max_retries=1)
def generate_mcp_suggestions_only(self, analysis_id: int, github_token: str):
    """
    Generate MCP tool suggestions for existing analysis
    """
    db = SessionLocal()
    try:
        # Get analysis record
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            raise ValueError(f"Analysis {analysis_id} not found")
        
        if not settings.anthropic_api_key and not settings.openai_api_key:
            raise ValueError("No AI API key configured (neither Claude nor OpenAI)")
        
        # Update status
        analysis.status = "analyzing"
        db.commit()
        
        self.update_state(
            state='PROGRESS',
            meta={'current': 20, 'total': 100, 'status': 'Generating MCP tool suggestions...'}
        )
        
        intelligent_service = IntelligentAnalysisService()
        
        # Generate suggestions based on existing basic analysis
        existing_results = analysis.analysis_results or {}
        
        # Create enhanced analysis context
        enhanced_context = {
            "repository_info": {
                "name": analysis.repo_name,
                "owner": analysis.repo_owner,
                "description": existing_results.get("repository_info", {}).get("description", ""),
                "language": existing_results.get("repository_info", {}).get("language", ""),
                "topics": existing_results.get("repository_info", {}).get("topics", [])
            },
            "existing_analysis": existing_results
        }
        
        # Gather fresh repository content for better suggestions
        try:
            logger.info("=== CALLING COMPREHENSIVE ANALYSIS (SUGGESTIONS) ===")
            logger.info(f"Repo: {analysis.repo_owner}/{analysis.repo_name}")

            comprehensive_analysis = asyncio.run(intelligent_service.analyze_repository_comprehensively(
                analysis.repo_owner,
                analysis.repo_name,
                github_token
            ))

            logger.info("=== COMPREHENSIVE ANALYSIS COMPLETED (SUGGESTIONS) ===")
        except Exception as suggestions_error:
            logger.error("=== COMPREHENSIVE ANALYSIS FAILED (SUGGESTIONS) ===")
            logger.error(f"Error: {str(suggestions_error)}")
            logger.error(f"Error type: {type(suggestions_error)}")

            # Check if it's the slice error specifically
            if "unhashable type: 'slice'" in str(suggestions_error):
                logger.error("=== DETECTED SLICE ERROR IN SUGGESTIONS ANALYSIS ===")
                import traceback
                logger.error(f"Full traceback: {traceback.format_exc()}")

            # Re-raise the error
            raise suggestions_error
        
        # Update analysis with new suggestions
        enhanced_results = existing_results.copy()

        # Extract MCP suggestions from comprehensive analysis
        # The comprehensive analysis returns mcp_suggestions as a nested object
        mcp_data = comprehensive_analysis.get("mcp_suggestions", {})
        mcp_suggestions = {
            "categories": mcp_data.get("categories", {}),
            "prioritized_recommendations": mcp_data.get("prioritized_recommendations", []),
            "implementation_roadmap": mcp_data.get("implementation_roadmap", {}),
            "total_tools_suggested": mcp_data.get("total_tools_suggested", 0),
            "repository_analysis": comprehensive_analysis.get("repository_analysis", {}),
            "tool_generation_strategy": mcp_data.get("tool_generation_strategy", {})
        }

        # Extract business analysis
        # Handle both direct and nested comprehensive analysis structure
        comp_analysis = comprehensive_analysis.get("comprehensive_analysis", comprehensive_analysis)
        business_analysis = {
            "business_logic": comp_analysis.get("business_logic", {}),
            "workflows": comp_analysis.get("workflows", {}),
            "api_capabilities": comp_analysis.get("api_capabilities", {}),
            "integration_opportunities": comp_analysis.get("integration_opportunities", {})
        }

        enhanced_results.update({
            "ai_suggestions": mcp_suggestions,
            "business_analysis": business_analysis,
            "suggestions_generated": True,
            "generation_ready": True
        })
        
        analysis.analysis_results = enhanced_results
        analysis.status = "completed"
        db.commit()
        
        self.update_state(
            state='SUCCESS',
            meta={'current': 100, 'total': 100, 'status': 'MCP tool suggestions generated successfully!'}
        )
        
        return {
            "analysis_id": analysis_id,
            "status": "completed",
            "suggestions_count": comprehensive_analysis.get("total_tools_suggested", 0),
            "ready_for_selection": True
        }
        
    except Exception as exc:
        logger.error(f"MCP suggestions generation failed for {analysis_id}: {str(exc)}")
        
        try:
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "failed"
                analysis.error_message = f"Suggestions generation failed: {str(exc)}"
                db.commit()
        except:
            pass
        
        self.update_state(
            state='FAILURE',
            meta={'current': 0, 'total': 100, 'status': f'Suggestions generation failed: {str(exc)}'}
        )
        
        raise exc
    
    finally:
        db.close()

