from celery import Celery
from ..config import settings

# Create Celery instance
celery_app = Celery(
    "supermcp",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=["app.tasks.analysis_tasks", "app.tasks.simple_analysis", "app.tasks.working_analysis", "app.tasks.real_analysis", "app.tasks.enhanced_analysis", "app.tasks.mcp_analysis_task", "app.tasks.parallel_analysis"]
)

# Configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    # Remove custom queue routing for now
    task_default_queue='default',
    task_default_exchange='default',
    task_default_routing_key='default'
)