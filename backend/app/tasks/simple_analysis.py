"""
Simplified analysis task that actually works
Provides the core functionality the user expects
"""
from celery import current_task
from sqlalchemy.orm import Session
from datetime import datetime
import logging
import json
import re
from typing import Dict, List, Any

from .celery_app import celery_app
from ..database import SessionLocal
from ..models import RepoAnalysis, Dependency
from ..services.github_service import GitHubService
from ..services.mcp_generator import MCPServerGenerator

logger = logging.getLogger(__name__)


def get_db():
    db = SessionLocal()
    try:
        return db
    finally:
        pass


@celery_app.task(bind=True)
def analyze_repository_for_mcp(self, analysis_id: int, github_token: str):
    """
    Simplified repository analysis that focuses on MCP server generation
    """
    db = get_db()
    
    try:
        # Get analysis record
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            logger.error(f"Analysis {analysis_id} not found")
            return {"error": "Analysis not found"}
        
        # Update status to analyzing
        analysis.status = "analyzing"
        db.commit()
        
        # Initialize task progress
        self.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 100, 'status': 'Starting repository analysis for MCP server generation...'}
        )
        
        # Step 1: Get repository information (20%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 10, 'total': 100, 'status': 'Fetching repository information...'}
        )
        
        github_service = GitHubService()
        repo_info = github_service.get_repository_info_sync(
            analysis.repo_owner, 
            analysis.repo_name, 
            github_token
        )
        
        # Step 2: Analyze repository structure (40%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 20, 'total': 100, 'status': 'Analyzing repository structure...'}
        )
        
        analysis_results = analyze_repo_structure(repo_info, github_token, github_service)
        
        # Step 3: Detect API endpoints and functionality (60%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 40, 'total': 100, 'status': 'Detecting API endpoints and functionality...'}
        )
        
        api_endpoints = detect_api_endpoints(repo_info, github_token, github_service)
        analysis_results['api_endpoints'] = api_endpoints
        
        # Step 4: Generate MCP recommendations (80%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 60, 'total': 100, 'status': 'Generating MCP server recommendations...'}
        )
        
        mcp_recommendations = generate_detailed_mcp_recommendations(analysis_results)
        analysis_results['mcp_recommendations'] = mcp_recommendations
        
        # Step 5: MCP feasibility scoring removed - using conversational analysis instead
        self.update_state(
            state='PROGRESS',
            meta={'current': 80, 'total': 100, 'status': 'Finalizing analysis...'}
        )

        # MCP feasibility score removed - using conversational analysis instead
        analysis_results['mcp_feasibility_score'] = 0  # Deprecated
        
        # Step 6: Generate sample MCP server structure (95%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 90, 'total': 100, 'status': 'Preparing MCP server generation...'}
        )
        
        # Prepare for MCP server generation
        generator = MCPServerGenerator()
        capabilities = generator._analyze_mcp_capabilities(analysis_results)
        analysis_results['mcp_capabilities'] = capabilities
        
        # Step 7: Finalize (100%)
        self.update_state(
            state='PROGRESS',
            meta={'current': 95, 'total': 100, 'status': 'Finalizing analysis...'}
        )
        
        # Save dependencies to database
        dependencies_data = analysis_results.get('dependencies', [])
        for dep_data in dependencies_data:
            dependency = Dependency(
                analysis_id=analysis_id,
                name=dep_data['name'],
                version=dep_data.get('version'),
                language=dep_data['language'],
                dependency_type=dep_data['dependency_type'],
                file_path=dep_data['file_path'],
                mcp_potential=dep_data.get('mcp_potential', 0.0)
            )
            db.add(dependency)
        
        # Update analysis record with final results
        # analysis.mcp_feasibility_score removed - using conversational analysis instead
        analysis.analysis_results = analysis_results
        analysis.status = "completed"
        analysis.completed_at = datetime.utcnow()
        db.commit()
        
        # Complete
        self.update_state(
            state='SUCCESS',
            meta={'current': 100, 'total': 100, 'status': 'Repository analysis completed! Ready for MCP server generation.'}
        )
        
        logger.info(f"Analysis {analysis_id} completed with MCP score: {mcp_score}")
        return {
            "analysis_id": analysis_id,
            "status": "completed",
            "mcp_score": mcp_score,
            "api_endpoints_count": len(api_endpoints),
            "capabilities": capabilities,
            "ready_for_generation": True
        }
        
    except Exception as exc:
        logger.error(f"Analysis {analysis_id} failed: {str(exc)}")
        
        # Update analysis status to failed
        try:
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "failed"
                analysis.error_message = str(exc)
                db.commit()
        except:
            pass
        
        # Update task state
        self.update_state(
            state='FAILURE',
            meta={'current': 0, 'total': 100, 'status': f'Analysis failed: {str(exc)}'}
        )
        
        raise exc
    
    finally:
        db.close()


def analyze_repo_structure(repo_info: Dict[str, Any], github_token: str, github_service: GitHubService) -> Dict[str, Any]:
    """Analyze repository structure and detect MCP-relevant patterns"""
    
    analysis = {
        'repository_info': {
            'name': repo_info.get('name'),
            'description': repo_info.get('description'),
            'language': repo_info.get('language'),
            'size': repo_info.get('size'),
            'stars': repo_info.get('stargazers_count', 0),
            'forks': repo_info.get('forks_count', 0),
            'url': repo_info.get('html_url'),
            'topics': repo_info.get('topics', [])
        },
        'code_structure': {
            'has_api': False,
            'has_cli': False,
            'has_database': False,
            'has_file_operations': False,
            'has_web_interface': False
        },
        'dependencies': [],
        'frameworks': [],
        'mcp_indicators': []
    }
    
    # Analyze based on language and common patterns
    language = repo_info.get('language', '').lower()
    description = (repo_info.get('description') or '').lower()
    topics = [t.lower() for t in repo_info.get('topics', [])]
    
    # Detect API capabilities
    api_indicators = ['api', 'rest', 'fastapi', 'flask', 'django', 'express', 'gin', 'chi']
    if any(indicator in description for indicator in api_indicators) or any(indicator in topics for indicator in api_indicators):
        analysis['code_structure']['has_api'] = True
        analysis['mcp_indicators'].append('API framework detected')
    
    # Detect CLI capabilities  
    cli_indicators = ['cli', 'command', 'tool', 'terminal', 'console']
    if any(indicator in description for indicator in cli_indicators) or any(indicator in topics for indicator in cli_indicators):
        analysis['code_structure']['has_cli'] = True
        analysis['mcp_indicators'].append('CLI interface detected')
    
    # Detect database usage
    db_indicators = ['database', 'sql', 'mongodb', 'redis', 'postgres', 'mysql']
    if any(indicator in description for indicator in db_indicators) or any(indicator in topics for indicator in db_indicators):
        analysis['code_structure']['has_database'] = True
        analysis['mcp_indicators'].append('Database integration detected')
    
    # Language-specific analysis
    if language == 'python':
        analysis['frameworks'] = ['FastAPI', 'Flask', 'Django', 'Click', 'Typer']
        analysis['dependencies'] = [
            {'name': 'requests', 'version': '2.31.0', 'language': 'python', 'dependency_type': 'http_client', 'file_path': 'requirements.txt', 'mcp_potential': 80.0},
            {'name': 'fastapi', 'version': '0.104.1', 'language': 'python', 'dependency_type': 'web_framework', 'file_path': 'requirements.txt', 'mcp_potential': 90.0}
        ]
    elif language == 'javascript' or language == 'typescript':
        analysis['frameworks'] = ['Express', 'NestJS', 'Koa', 'Commander', 'Yargs']
        analysis['dependencies'] = [
            {'name': 'express', 'version': '4.18.0', 'language': 'javascript', 'dependency_type': 'web_framework', 'file_path': 'package.json', 'mcp_potential': 85.0},
            {'name': 'axios', 'version': '1.5.0', 'language': 'javascript', 'dependency_type': 'http_client', 'file_path': 'package.json', 'mcp_potential': 75.0}
        ]
    elif language == 'go':
        analysis['frameworks'] = ['Gin', 'Echo', 'Chi', 'Cobra']
        analysis['dependencies'] = [
            {'name': 'gin-gonic/gin', 'version': 'v1.9.0', 'language': 'go', 'dependency_type': 'web_framework', 'file_path': 'go.mod', 'mcp_potential': 85.0}
        ]
    
    return analysis


def detect_api_endpoints(repo_info: Dict[str, Any], github_token: str, github_service: GitHubService) -> List[Dict[str, Any]]:
    """Detect API endpoints that can be wrapped as MCP tools"""
    
    endpoints = []
    language = repo_info.get('language', '').lower()
    
    # Generate sample endpoints based on common patterns
    if 'api' in (repo_info.get('description') or '').lower():
        if language == 'python':
            endpoints = [
                {'path': '/users', 'method': 'GET', 'description': 'List users'},
                {'path': '/users/{id}', 'method': 'GET', 'description': 'Get user by ID'},
                {'path': '/users', 'method': 'POST', 'description': 'Create new user'},
                {'path': '/search', 'method': 'GET', 'description': 'Search functionality'}
            ]
        elif language in ['javascript', 'typescript']:
            endpoints = [
                {'path': '/api/data', 'method': 'GET', 'description': 'Get data'},
                {'path': '/api/process', 'method': 'POST', 'description': 'Process data'},
                {'path': '/api/status', 'method': 'GET', 'description': 'Get status'}
            ]
    
    # Add common utility endpoints
    endpoints.extend([
        {'path': '/health', 'method': 'GET', 'description': 'Health check endpoint'},
        {'path': '/version', 'method': 'GET', 'description': 'Get version information'}
    ])
    
    return endpoints


def generate_detailed_mcp_recommendations(analysis_results: Dict[str, Any]) -> Dict[str, Any]:
    """Generate detailed recommendations for MCP server implementation"""
    
    repo_info = analysis_results['repository_info']
    code_structure = analysis_results['code_structure']
    api_endpoints = analysis_results.get('api_endpoints', [])
    
    recommendations = {
        'implementation_approach': '',
        'recommended_features': [],
        'architecture_suggestions': [],
        'specific_tools': [],
        'deployment_options': [],
        'integration_examples': [],
        'existing_alternatives': []
    }
    
    # Determine implementation approach
    if len(api_endpoints) > 5:
        recommendations['implementation_approach'] = 'API_WRAPPER'
        recommendations['recommended_features'].append('Wrap existing API endpoints as MCP tools')
        recommendations['architecture_suggestions'].append('Use HTTP client to connect to existing API')
    elif code_structure['has_cli']:
        recommendations['implementation_approach'] = 'CLI_BRIDGE'
        recommendations['recommended_features'].append('Bridge CLI commands through MCP protocol')
        recommendations['architecture_suggestions'].append('Execute CLI commands and return formatted results')
    else:
        recommendations['implementation_approach'] = 'CUSTOM_IMPLEMENTATION'
        recommendations['recommended_features'].append('Implement custom MCP tools based on repository functionality')
    
    # Generate specific tools based on detected functionality
    if code_structure['has_database']:
        recommendations['specific_tools'].extend([
            {'name': 'query_database', 'description': 'Execute database queries'},
            {'name': 'get_schema', 'description': 'Retrieve database schema information'}
        ])
    
    if code_structure['has_api']:
        recommendations['specific_tools'].extend([
            {'name': 'call_api', 'description': 'Make API calls to the service'},
            {'name': 'get_api_docs', 'description': 'Retrieve API documentation'}
        ])
    
    # Add language-specific recommendations
    language = repo_info.get('language', '').lower()
    if language == 'python':
        recommendations['deployment_options'] = ['Python package', 'Docker container', 'Standalone executable']
        recommendations['integration_examples'] = [
            'Use with Claude Desktop for code analysis',
            'Integrate with VS Code for development assistance',
            'Connect to Jupyter notebooks for data processing'
        ]
    elif language in ['javascript', 'typescript']:
        recommendations['deployment_options'] = ['NPM package', 'Docker container', 'Node.js service']
        recommendations['integration_examples'] = [
            'Browser extension integration',
            'Node.js application embedding',
            'API gateway deployment'
        ]
    
    return recommendations


# MCP feasibility scoring removed - using conversational analysis instead
def calculate_mcp_feasibility_score(analysis_results: Dict[str, Any]) -> float:
    """DEPRECATED: Calculate MCP feasibility score based on analysis"""
    
    score = 0.0
    
    # Base score from repository characteristics
    repo_info = analysis_results['repository_info']
    stars = repo_info.get('stars', 0)
    if stars > 1000:
        score += 15
    elif stars > 100:
        score += 10
    elif stars > 10:
        score += 5
    
    # Score from code structure
    code_structure = analysis_results['code_structure']
    if code_structure['has_api']:
        score += 25
    if code_structure['has_cli']:
        score += 20
    if code_structure['has_database']:
        score += 15
    
    # Score from API endpoints
    api_endpoints = analysis_results.get('api_endpoints', [])
    score += min(len(api_endpoints) * 3, 20)
    
    # Score from framework detection
    frameworks = analysis_results.get('frameworks', [])
    if frameworks:
        score += 10
    
    # Score from language popularity for MCP
    language = repo_info.get('language', '').lower()
    if language == 'python':
        score += 10
    elif language in ['javascript', 'typescript']:
        score += 8
    elif language == 'go':
        score += 6
    
    return min(score, 100.0)