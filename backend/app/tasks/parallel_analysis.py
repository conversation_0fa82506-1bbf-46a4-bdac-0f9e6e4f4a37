"""
Parallel Repository Analysis Tasks

Breaks down repository analysis into multiple parallel tasks to utilize all 8 Celery workers.
Each task can run on a different worker process simultaneously.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List
from celery import group, chain, chord
from sqlalchemy.orm import Session

from .celery_app import celery_app
from ..database import Session<PERSON><PERSON><PERSON>
from ..models.analysis import RepoAnalysis
from ..services.intelligent_analysis_service import IntelligentAnalysisService

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, max_retries=2)
def start_parallel_analysis(self, analysis_id: int, github_token: str):
    """
    Parallel analysis following EXACT sequential structure but with parallel execution

    Structure:
    1. Code Indexing (20-40%) - PARALLEL
    2. Enhanced Analysis (40-90%) - PARALLEL (using indexed code)
    3. MCP Analysis (90-100%) - PARALLEL (using enhanced analysis data + indexed code)
    """
    try:
        # Get analysis info for better logging
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        repo_name = f"{analysis.repo_owner}/{analysis.repo_name}" if analysis else f"analysis_{analysis_id}"
        db.close()

        logger.info(f"🚀 Starting STRUCTURED parallel analysis for {repo_name}")

        # Update initial progress
        self.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 100, 'status': 'Initializing structured parallel analysis...'}
        )

        # PHASE 1: Code Indexing (20-40%) - PARALLEL
        indexing_task = parallel_code_indexing.s(analysis_id, github_token)

        # PHASE 2: Enhanced Analysis (40-90%) - PARALLEL (depends on indexing)
        enhanced_task = parallel_enhanced_analysis.s(analysis_id, github_token)

        # PHASE 3: MCP Analysis (90-100%) - PARALLEL (uses enhanced analysis data + indexed code)
        mcp_task = parallel_mcp_analysis.s(analysis_id, github_token)

        # Create SEQUENTIAL workflow with PARALLEL execution within each phase
        workflow = chain(
            indexing_task,      # Phase 1: Index code (parallel workers)
            enhanced_task,      # Phase 2: Enhanced analysis (parallel workers)
            mcp_task           # Phase 3: MCP generation (parallel workers)
        )

        # Execute workflow
        result = workflow.apply_async()

        # Update analysis_results to track the workflow task for progress
        db = SessionLocal()
        try:
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            if analysis:
                # Update to track the workflow task instead of orchestrator
                analysis.analysis_results = {
                    "task_id": result.id,  # Track workflow task for progress
                    "processing_mode": "parallel_structured",
                    "workers": 8,
                    "orchestrator_task": self.request.id
                }
                db.commit()
        finally:
            db.close()

        return {
            "status": "structured_parallel_workflow_started",
            "analysis_id": analysis_id,
            "workflow_id": result.id,
            "phases": 3
        }

    except Exception as e:
        logger.error(f"Failed to start structured parallel analysis: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def parallel_code_indexing(self, analysis_id: int, github_token: str):
    """
    PHASE 1: Code Indexing (20-40%) - PARALLEL

    Matches sequential: Step 1 from analyze_repository_with_enhanced_mcps
    - Repository content gathering
    - Code indexing for AI context
    """
    try:
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            raise Exception(f"Analysis {analysis_id} not found")

        logger.info(f"📁 PHASE 1: Code indexing for {analysis.repo_owner}/{analysis.repo_name}")

        # Update status to analyzing
        analysis.status = "analyzing"
        db.commit()

        # Step 1: Index repository code FIRST for better AI context (20-40%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 20,
                'total': 100,
                'status': f'Analyzing {analysis.repo_owner}/{analysis.repo_name} - Indexing repository code...',
                'current_task': 'code_indexing',
                'details': f'Creating searchable index for {analysis.repo_owner}/{analysis.repo_name}'
            }
        )

        # HYBRID APPROACH: Unlimited processing + GitHub API data
        from app.services.parallel_repository_processor import ParallelRepositoryProcessor
        from app.services.github_service import GitHubService

        processor = ParallelRepositoryProcessor()
        github_service = GitHubService()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Step 1: Get GitHub API data (repository info, languages, etc.)
            logger.info(f"🔍 Fetching GitHub API data for {analysis.repo_owner}/{analysis.repo_name}")

            repo_info = github_service.get_repo_info(github_token, analysis.repo_owner, analysis.repo_name)
            languages_data = github_service.get_repo_languages(github_token, analysis.repo_owner, analysis.repo_name)

            # Calculate language percentages
            total_bytes = sum(languages_data.values())
            language_percentages = {}
            if total_bytes > 0:
                language_percentages = {
                    lang: round((bytes_count / total_bytes) * 100, 1)
                    for lang, bytes_count in languages_data.items()
                }

            # Step 2: Use unlimited repository processing to get ALL files
            repo_url = f"https://github.com/{analysis.repo_owner}/{analysis.repo_name}"
            unlimited_repo_content = loop.run_until_complete(
                processor.process_repository_unlimited(repo_url, analysis.id)
            )

            # Step 3: Enhance with GitHub API data
            initial_repo_content = unlimited_repo_content.copy()
            initial_repo_content['repository_info'].update({
                'language': repo_info.get('language'),
                'languages': languages_data,
                'language_percentages': language_percentages,
                'stargazers_count': repo_info.get('stargazers_count', 0),
                'forks_count': repo_info.get('forks_count', 0),
                'size': repo_info.get('size', 0),
                'topics': repo_info.get('topics', []),
                'created_at': repo_info.get('created_at'),
                'updated_at': repo_info.get('updated_at'),
                'default_branch': repo_info.get('default_branch'),
                'processing_method': 'unlimited_parallel_with_github_api'
            })

            # Index the code (SAME as sequential)
            from app.services.indexing_service import IndexingService
            indexing_service = IndexingService()
            indexing_result = loop.run_until_complete(
                indexing_service.index_repository(analysis.id, initial_repo_content)
            )

            # Update progress
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': 40,
                    'total': 100,
                    'status': f'Code indexing completed - {indexing_result.get("files_indexed", 0)} files indexed',
                    'current_task': 'code_indexing_complete'
                }
            )

            # Store indexing results for next phase
            analysis.files_indexed = indexing_result.get("files_indexed", 0)

            # Store initial repo content for enhanced analysis
            if not hasattr(analysis, 'intermediate_data') or not analysis.intermediate_data:
                analysis.intermediate_data = {}

            # CRITICAL FIX: Use direct assignment for JSON column updates
            current_data = analysis.intermediate_data.copy()
            current_data.update({
                'initial_repo_content': initial_repo_content,
                'indexing_result': indexing_result,
                'files_indexed': indexing_result.get("files_indexed", 0)
            })
            analysis.intermediate_data = current_data

            # Mark as modified for SQLAlchemy to detect changes
            from sqlalchemy.orm.attributes import flag_modified
            flag_modified(analysis, 'intermediate_data')

            db.commit()

            logger.info(f"✅ PHASE 1 completed: {indexing_result.get('files_indexed', 0)} files indexed")

            return {
                "status": "code_indexing_completed",
                "analysis_id": analysis_id,
                "files_indexed": indexing_result.get("files_indexed", 0)
            }

        finally:
            loop.close()
            db.close()

    except Exception as e:
        logger.error(f"PHASE 1 (Code indexing) failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def process_repository_unlimited(self, analysis_id: int, github_token: str):
    """
    Process repository with unlimited capacity (single task - file processing)
    """
    try:
        logger.info(f"📁 Processing repository for analysis {analysis_id}")

        # Update progress: Starting repository processing
        self.update_state(
            state='PROGRESS',
            meta={'current': 5, 'total': 100, 'status': 'Starting repository processing...'}
        )

        # This part is inherently sequential (git clone + file discovery)
        # But we can optimize it and prepare data for parallel analysis

        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis:
            raise Exception(f"Analysis {analysis_id} not found")

        # Update progress: Repository found
        self.update_state(
            state='PROGRESS',
            meta={'current': 10, 'total': 100, 'status': f'Processing {analysis.repo_owner}/{analysis.repo_name}...'}
        )
        
        # Initialize analysis service
        analysis_service = IntelligentAnalysisService()
        
        # Process repository (this is the bottleneck we can't parallelize)
        repo_url = f"https://github.com/{analysis.repo_owner}/{analysis.repo_name}"
        
        # Update progress: Starting unlimited processing
        self.update_state(
            state='PROGRESS',
            meta={'current': 20, 'total': 100, 'status': 'Downloading and analyzing repository files...'}
        )

        # Use unlimited processing
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            repo_content = loop.run_until_complete(
                analysis_service._analyze_repository_unlimited(
                    repo_url, analysis.repo_owner, analysis.repo_name,
                    github_token, analysis_id
                )
            )

            # Update progress: Repository processing completed
            self.update_state(
                state='PROGRESS',
                meta={'current': 50, 'total': 100, 'status': f'Repository processed: {repo_content.get("files_count", 0)} files found'}
            )
            
            # Store intermediate results for parallel tasks
            analysis.intermediate_data = repo_content
            db.commit()
            
            logger.info(f"✅ Repository processing completed: {repo_content.get('files_count', 0)} files")
            
            return {
                "status": "repository_processed",
                "analysis_id": analysis_id,
                "files_count": repo_content.get("files_count", 0)
            }
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Repository processing failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def analyze_business_logic_parallel(self, analysis_id: int):
    """
    Analyze business logic in parallel (Worker 1)
    """
    try:
        logger.info(f"🧠 Analyzing business logic for analysis {analysis_id}")

        # Update progress for this parallel task
        self.update_state(
            state='PROGRESS',
            meta={'current': 60, 'total': 100, 'status': 'Analyzing business logic patterns...'}
        )
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")
        
        # Initialize analysis service
        analysis_service = IntelligentAnalysisService()
        
        # Run business logic analysis
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            business_analysis = loop.run_until_complete(
                analysis_service._analyze_business_logic(analysis.intermediate_data)
            )
            
            # Store results
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["business_logic"] = business_analysis
            db.commit()
            
            logger.info(f"✅ Business logic analysis completed for {analysis_id}")
            return {"status": "business_logic_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Business logic analysis failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def analyze_workflows_parallel(self, analysis_id: int):
    """
    Analyze workflows in parallel (Worker 2)
    """
    try:
        logger.info(f"🔄 Analyzing workflows for analysis {analysis_id}")

        # Update progress for this parallel task
        self.update_state(
            state='PROGRESS',
            meta={'current': 65, 'total': 100, 'status': 'Analyzing workflow patterns...'}
        )
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")

        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            workflow_analysis = loop.run_until_complete(
                analysis_service._analyze_workflows(analysis.intermediate_data)
            )
            
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["workflows"] = workflow_analysis
            db.commit()
            
            logger.info(f"✅ Workflow analysis completed for {analysis_id}")
            return {"status": "workflows_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Workflow analysis failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def analyze_apis_parallel(self, analysis_id: int):
    """
    Analyze APIs in parallel (Worker 3)
    """
    try:
        logger.info(f"🌐 Analyzing APIs for analysis {analysis_id}")

        # Update progress for this parallel task
        self.update_state(
            state='PROGRESS',
            meta={'current': 70, 'total': 100, 'status': 'Analyzing API endpoints...'}
        )
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")

        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            api_analysis = loop.run_until_complete(
                analysis_service._analyze_api_capabilities(analysis.intermediate_data)
            )
            
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["apis"] = api_analysis
            db.commit()
            
            logger.info(f"✅ API analysis completed for {analysis_id}")
            return {"status": "apis_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"API analysis failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def analyze_integrations_parallel(self, analysis_id: int):
    """
    Analyze integrations in parallel (Worker 4)
    """
    try:
        logger.info(f"🔗 Analyzing integrations for analysis {analysis_id}")

        # Update progress for this parallel task
        self.update_state(
            state='PROGRESS',
            meta={'current': 75, 'total': 100, 'status': 'Analyzing integrations...'}
        )
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")

        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            integration_analysis = loop.run_until_complete(
                analysis_service._analyze_integration_points(analysis.intermediate_data)
            )
            
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["integrations"] = integration_analysis
            db.commit()
            
            logger.info(f"✅ Integration analysis completed for {analysis_id}")
            return {"status": "integrations_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Integration analysis failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def index_code_vectors_parallel(self, analysis_id: int):
    """
    Index code vectors in parallel (Worker 5)
    """
    try:
        logger.info(f"🔍 Indexing code vectors for analysis {analysis_id}")
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis or not analysis.intermediate_data:
            raise Exception(f"No intermediate data for analysis {analysis_id}")

        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Initialize vector service
            if not analysis_service.vector_service:
                from ..services.unified_vector_service import UnifiedVectorService
                analysis_service.vector_service = UnifiedVectorService()
                loop.run_until_complete(analysis_service.vector_service.__aenter__())

            # Index vectors
            vector_result = loop.run_until_complete(
                analysis_service.vector_service.index_repository_code(analysis_id, analysis.intermediate_data)
            )
            
            if not analysis.parallel_results:
                analysis.parallel_results = {}
            analysis.parallel_results["vector_indexing"] = vector_result
            db.commit()
            
            logger.info(f"✅ Vector indexing completed for {analysis_id}")
            return {"status": "vectors_completed", "analysis_id": analysis_id}
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Vector indexing failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def aggregate_analysis_results(self, analysis_id: int, github_token: str):
    """
    Aggregate all parallel analysis results and generate final MCP suggestions
    """
    try:
        logger.info(f"📊 Aggregating analysis results for {analysis_id}")

        # Update progress: Starting aggregation
        self.update_state(
            state='PROGRESS',
            meta={'current': 85, 'total': 100, 'status': 'Aggregating parallel analysis results...'}
        )
        
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

        if not analysis:
            raise Exception(f"Analysis {analysis_id} not found")
        
        # Safely extract data with proper error handling
        intermediate_data = analysis.intermediate_data or {}
        parallel_results = analysis.parallel_results or {}

        # Combine all parallel results
        combined_results = {
            "repository_info": intermediate_data.get("repository_info", {}),
            "business_logic": parallel_results.get("business_logic", {}),
            "workflows": parallel_results.get("workflows", {}),
            "apis": parallel_results.get("apis", {}),
            "integrations": parallel_results.get("integrations", {}),
            "vector_indexing": parallel_results.get("vector_indexing", {}),
            "code_samples": intermediate_data.get("code_samples", {}),
            "files_count": intermediate_data.get("files_count", 0)
        }
        
        # Generate MCP suggestions
        analysis_service = IntelligentAnalysisService()
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Generate MCP suggestions with combined data
            # Safely handle code_samples that might be empty or None
            code_samples = combined_results.get("code_samples", {})
            if isinstance(code_samples, dict) and code_samples:
                code_samples_sample = dict(list(code_samples.items())[:20])
            else:
                code_samples_sample = {}
            
            mcp_suggestions = loop.run_until_complete(
                analysis_service._generate_mcp_suggestions({
                    "repository_info": combined_results["repository_info"],
                    "business_logic": combined_results["business_logic"],
                    "workflows": combined_results["workflows"],
                    "apis": combined_results["apis"],
                    "integrations": combined_results["integrations"],
                    "code_samples": code_samples_sample,
                    "total_files_processed": analysis.intermediate_data.get("files_count", 0)
                })
            )
            
            # Update progress: Finalizing
            self.update_state(
                state='PROGRESS',
                meta={'current': 95, 'total': 100, 'status': 'Finalizing analysis results...'}
            )

            # Store final results in analysis_results (where frontend expects it)
            analysis.analysis_results = combined_results
            analysis.mcp_suggestions = mcp_suggestions
            analysis.status = "completed"
            db.commit()

            # Update progress: Completed
            self.update_state(
                state='SUCCESS',
                meta={'current': 100, 'total': 100, 'status': 'Parallel analysis completed successfully!'}
            )

            logger.info(f"✅ Parallel analysis completed for {analysis_id}")
            
            return {
                "status": "completed",
                "analysis_id": analysis_id,
                "parallel_workers_used": 5,
                "files_processed": analysis.intermediate_data.get("files_count", 0)
            }
            
        finally:
            loop.close()
            db.close()
            
    except Exception as e:
        logger.error(f"Analysis aggregation failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def parallel_enhanced_analysis(self, previous_result, analysis_id: int, github_token: str):
    """
    PHASE 2: Enhanced Analysis (40-90%) - PARALLEL (using indexed code)

    Matches sequential: Step 2 from analyze_repository_with_enhanced_mcps
    - Enhanced analysis with functional MCP generation
    - Uses indexed code for better AI context
    """
    try:
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            raise Exception(f"Analysis {analysis_id} not found")

        logger.info(f"🧠 PHASE 2: Enhanced analysis for {analysis.repo_owner}/{analysis.repo_name}")

        # Step 3: Enhanced analysis with functional MCP generation (40-90%)
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 40,
                'total': 100,
                'status': f'Analyzing {analysis.repo_owner}/{analysis.repo_name} - Extracting business logic and APIs...',
                'current_task': 'enhanced_analysis',
                'details': 'Analyzing repository and generating functional MCP tools'
            }
        )

        # Initialize the enhanced workflow (SAME as sequential)
        from app.services.updated_analysis_workflow import UpdatedAnalysisWorkflow
        workflow = UpdatedAnalysisWorkflow()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Get initial repository content for enhanced analysis
            initial_repo_content = analysis.intermediate_data.get('initial_repo_content', {})

            # Run the enhanced analysis with repository content
            enhanced_results = loop.run_until_complete(
                workflow.analyze_repository_with_functional_mcps(
                    analysis.repo_owner,
                    analysis.repo_name,
                    github_token,
                    {
                        'name': analysis.repo_name,
                        'owner': analysis.repo_owner,
                        'url': analysis.repo_url
                    }
                    # Removed extra initial_repo_content parameter - method only takes 4 params
                )
            )

            # Add technology stack analysis from GitHub API data
            if initial_repo_content and 'repository_info' in initial_repo_content:
                repo_info = initial_repo_content['repository_info']
                enhanced_results['technology_stack'] = {
                    'primary_language': repo_info.get('language', 'Unknown'),
                    'languages': repo_info.get('languages', {}),
                    'language_percentages': repo_info.get('language_percentages', {}),
                    'dependencies': initial_repo_content.get('dependencies', {}),
                    # FIXED: Get repository_structure from repo_info, not root level
                    'repository_structure': repo_info.get('repository_structure', {}),
                    'file_analysis': initial_repo_content.get('file_analysis', {}),
                    # Add additional repository metadata
                    'technology_stack_from_repo': repo_info.get('technology_stack', {}),
                    'total_files': repo_info.get('repository_structure', {}).get('total_files', 0),
                    'directories_count': len(repo_info.get('repository_structure', {}).get('directories', [])),
                    'key_files_count': len(repo_info.get('repository_structure', {}).get('key_files', []))
                }

            # Update progress
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': 90,
                    'total': 100,
                    'status': 'Enhanced analysis completed - preparing for MCP generation...',
                    'current_task': 'enhanced_analysis_complete'
                }
            )

            # Save dependencies to database (same as sequential processing)
            if initial_repo_content and 'repository_info' in initial_repo_content:
                repo_info = initial_repo_content['repository_info']
                dependencies_data = repo_info.get('dependencies', [])

                if dependencies_data:
                    from ..models.analysis import Dependency

                    # Clear existing dependencies for this analysis
                    db.query(Dependency).filter(Dependency.analysis_id == analysis_id).delete()

                    # Save new dependencies
                    for dep_data in dependencies_data:
                        if isinstance(dep_data, dict) and dep_data.get('name'):
                            dependency = Dependency(
                                analysis_id=analysis_id,
                                name=dep_data.get('name', ''),
                                version=dep_data.get('version'),
                                language=dep_data.get('language', ''),
                                dependency_type=dep_data.get('dependency_type', 'direct'),
                                file_path=dep_data.get('file_path', ''),
                                mcp_potential=dep_data.get('mcp_potential', 0.0)
                            )
                            db.add(dependency)

                    logger.info(f"💾 Saved {len(dependencies_data)} dependencies to database")

            # Store enhanced analysis results for MCP phase
            if not hasattr(analysis, 'intermediate_data') or not analysis.intermediate_data:
                analysis.intermediate_data = {}

            # CRITICAL FIX: Use direct assignment for JSON column updates
            current_data = analysis.intermediate_data.copy()
            current_data['enhanced_results'] = enhanced_results
            analysis.intermediate_data = current_data

            # Mark as modified for SQLAlchemy to detect changes
            from sqlalchemy.orm.attributes import flag_modified
            flag_modified(analysis, 'intermediate_data')

            db.commit()

            logger.info(f"✅ PHASE 2 completed: Enhanced analysis done")

            return {
                "status": "enhanced_analysis_completed",
                "analysis_id": analysis_id,
                "enhanced_results_keys": list(enhanced_results.keys()) if enhanced_results else []
            }

        finally:
            loop.close()
            db.close()

    except Exception as e:
        logger.error(f"PHASE 2 (Enhanced analysis) failed: {e}")
        raise


@celery_app.task(bind=True, max_retries=2)
def parallel_mcp_analysis(self, previous_result, analysis_id: int, github_token: str):
    """
    PHASE 3: MCP Analysis (90-100%) - PARALLEL (using enhanced analysis data + indexed code)

    Matches sequential: Step 3 from analyze_repository_with_enhanced_mcps
    - MCP analysis with rich context
    - Uses both enhanced analysis data AND indexed code
    """
    try:
        db = SessionLocal()
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            raise Exception(f"Analysis {analysis_id} not found")

        logger.info(f"🎯 PHASE 3: MCP analysis for {analysis.repo_owner}/{analysis.repo_name}")

        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 90,
                'total': 100,
                'status': f'Generating MCP suggestions with enhanced context...',
                'current_task': 'mcp_analysis',
                'details': 'Using enhanced analysis data + indexed code for comprehensive MCP suggestions'
            }
        )

        # Get enhanced results and indexed code
        enhanced_results = analysis.intermediate_data.get('enhanced_results', {})
        initial_repo_content = analysis.intermediate_data.get('initial_repo_content', {})

        # Run MCP analysis with rich context (SAME as sequential)
        from app.services.mcp_analysis_service import MCPAnalysisService
        mcp_service = MCPAnalysisService()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Create comprehensive context (SAME as sequential)
            repo_info = initial_repo_content.get('repository_info', {})
            context_parts = [
                f"Repository: {analysis.repo_url}",
                f"Name: {analysis.repo_name}",
                f"Owner: {analysis.repo_owner}",
                f"Status: {analysis.status}",
                "",
                "=== REPOSITORY ANALYSIS CONTEXT ===",
                "",
                f"Repository: {analysis.repo_owner}/{analysis.repo_name}",
                f"Description: {repo_info.get('description', 'No description available')}",
                f"Primary Language: {repo_info.get('language', 'Unknown')}",
                f"Stars: {repo_info.get('stargazers_count', 0)}",
                f"Forks: {repo_info.get('forks_count', 0)}",
                f"Topics: {', '.join(repo_info.get('topics', []))}",
                "",
                "This analysis uses both enhanced analysis data AND indexed code for comprehensive MCP suggestions."
            ]

            comprehensive_context = "\\n".join(context_parts)

            # Run MCP analysis with rich context (enhanced + indexed)
            # Use _generate_mcp_suggestions which returns a dictionary (JSON-serializable)
            from app.services.intelligent_analysis_service import IntelligentAnalysisService
            intelligent_service = IntelligentAnalysisService()

            mcp_results = loop.run_until_complete(
                intelligent_service._generate_mcp_suggestions({
                    "repository_info": initial_repo_content.get('repository_info', {}),
                    "business_logic": enhanced_results.get('business_logic', {}),
                    "workflows": enhanced_results.get('workflows', {}),
                    "apis": enhanced_results.get('apis', {}),
                    "integrations": enhanced_results.get('integrations', {}),
                    "code_samples": initial_repo_content.get('code_samples', {}),
                    "total_files_processed": analysis.files_indexed
                })
            )

            # Store final results - mcp_results is a dictionary (JSON-serializable)
            analysis.mcp_suggestions = mcp_results
            analysis.status = "completed"
            analysis.completed_at = datetime.utcnow()
            db.commit()

            # Update progress: Completed
            self.update_state(
                state='SUCCESS',
                meta={'current': 100, 'total': 100, 'status': 'Structured parallel analysis completed successfully!'}
            )

            logger.info(f"✅ PHASE 3 completed: MCP analysis done")

            return {
                "status": "mcp_analysis_completed",
                "analysis_id": analysis_id,
                "mcp_suggestions_count": len(mcp_results.get('suggestions', [])) if mcp_results else 0
            }

        finally:
            loop.close()
            db.close()

    except Exception as e:
        logger.error(f"PHASE 3 (MCP analysis) failed: {e}")
        raise
