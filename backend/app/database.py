from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

# Create engine with proper connection pooling to prevent exhaustion
engine = create_engine(
    settings.database_url,
    pool_size=20,  # Increase pool size for parallel processing
    max_overflow=30,  # Allow more overflow connections
    pool_timeout=60,  # Increase timeout
    pool_recycle=3600,  # Recycle connections every hour
    pool_pre_ping=True  # Verify connections before use
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()