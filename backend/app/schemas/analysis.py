from pydantic import BaseModel, HttpUrl
from datetime import datetime
from typing import Optional, List, Dict, Any


class RepoInfo(BaseModel):
    name: str
    full_name: str
    description: str
    url: str
    private: bool
    language: str
    stars: int
    forks: int
    updated_at: str


class AnalysisCreate(BaseModel):
    repo_url: HttpUrl
    repo_name: str
    repo_owner: str


class DependencyResponse(BaseModel):
    id: int
    name: str
    version: Optional[str] = None
    language: str
    dependency_type: str
    file_path: str
    mcp_potential: Optional[float] = None
    existing_mcp_servers: Optional[List[Dict[str, Any]]] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class AnalysisResponse(BaseModel):
    id: int
    repo_url: str
    repo_name: str
    repo_owner: str
    status: str
    # mcp_feasibility_score removed - using conversational analysis instead
    analysis_results: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    dependencies: List[DependencyResponse] = []
    
    class Config:
        from_attributes = True


class AnalysisStats(BaseModel):
    total_analyses: int
    completed_analyses: int
    failed_analyses: int
    pending_analyses: int
    average_mcp_score: Optional[float] = None