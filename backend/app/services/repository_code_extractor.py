"""
Repository-Specific Code Extractor
Extracts actual source code, function signatures, and implementations from analyzed repositories
"""

import os
import json
import logging
import tempfile
import subprocess
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass

from .real_code_analyzer import RealCodeAnalyzer
from .github_service import GitHubService

logger = logging.getLogger(__name__)

@dataclass
class ExtractedImplementation:
    function_name: str
    full_signature: str
    implementation_code: str
    file_path: str
    dependencies: List[str]
    parameters: List[Dict[str, Any]]
    return_type: Optional[str]
    docstring: Optional[str]
    is_async: bool
    business_context: str

class RepositoryCodeExtractor:
    """Extracts actual implementations from repository code for MCP server generation"""
    
    def __init__(self):
        self.code_analyzer = RealCodeAnalyzer()
        self.github_service = GitHubService()
    
    async def extract_implementations_for_tools(
        self, 
        repo_url: str, 
        repo_owner: str, 
        repo_name: str, 
        selected_tools: List[Dict[str, Any]], 
        github_token: str
    ) -> Dict[str, ExtractedImplementation]:
        """Extract actual implementations for selected MCP tools"""
        
        logger.info(f"Extracting implementations for {len(selected_tools)} tools from {repo_name}")
        
        # Clone repository to temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            repo_path = await self._clone_repository(repo_url, temp_dir, github_token)
            
            # Analyze repository code
            repo_info = {
                'name': repo_name,
                'owner': repo_owner,
                'url': repo_url
            }
            
            analysis_results = await self.code_analyzer.analyze_repository(repo_path, repo_info)
            
            # Extract implementations for each tool
            implementations = {}
            
            for tool in selected_tools:
                tool_name = tool.get('name', tool.get('tool_name', ''))
                if not tool_name:
                    continue
                
                implementation = await self._extract_tool_implementation(
                    tool, analysis_results, repo_path
                )
                
                if implementation:
                    implementations[tool_name] = implementation
                    logger.info(f"✅ Extracted real implementation for tool: {tool_name}")
                else:
                    logger.error(f"❌ FAILED to extract implementation for tool: {tool_name}")
                    # This is critical - we must have real implementations
                    raise Exception(f"No real implementation found for tool '{tool_name}'. Cannot generate MCP server without actual repository code.")

            if not implementations:
                raise Exception(f"No implementations could be extracted from repository {repo_name}. The repository may not contain suitable functions for the selected tools.")

            logger.info(f"🎉 Successfully extracted {len(implementations)} real implementations from {repo_name}")
            return implementations
    
    async def _clone_repository(self, repo_url: str, temp_dir: str, github_token: str) -> str:
        """Clone repository to temporary directory"""
        
        repo_path = os.path.join(temp_dir, "repo")
        
        # Prepare git clone command with authentication
        if github_token:
            # Use token for authentication
            auth_url = repo_url.replace('https://github.com/', f'https://{github_token}@github.com/')
            clone_cmd = ['git', 'clone', '--depth', '1', auth_url, repo_path]
        else:
            # Public repository
            clone_cmd = ['git', 'clone', '--depth', '1', repo_url, repo_path]
        
        try:
            result = subprocess.run(
                clone_cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode != 0:
                raise Exception(f"Git clone failed: {result.stderr}")
            
            logger.info(f"Successfully cloned repository to {repo_path}")
            return repo_path
            
        except subprocess.TimeoutExpired:
            raise Exception("Repository clone timed out")
        except Exception as e:
            raise Exception(f"Failed to clone repository: {str(e)}")
    
    async def _extract_tool_implementation(
        self, 
        tool: Dict[str, Any], 
        analysis_results: Dict[str, Any], 
        repo_path: str
    ) -> Optional[ExtractedImplementation]:
        """Extract implementation for a specific tool"""
        
        tool_name = tool.get('name', tool.get('tool_name', ''))
        tool_description = tool.get('description', '')
        
        # Get source functions and files from tool definition
        source_functions = tool.get('source_functions', [])
        source_files = tool.get('source_files', [])
        
        # If we have specific source information, use it
        if source_functions and source_files:
            return await self._extract_specific_function(
                source_functions[0], source_files[0], tool, analysis_results, repo_path
            )
        
        # Otherwise, try to find matching functions
        return await self._find_matching_implementation(
            tool_name, tool_description, analysis_results, repo_path
        )
    
    async def _extract_specific_function(
        self, 
        function_name: str, 
        file_path: str, 
        tool: Dict[str, Any], 
        analysis_results: Dict[str, Any], 
        repo_path: str
    ) -> Optional[ExtractedImplementation]:
        """Extract a specific function implementation"""
        
        # Find the function in analysis results
        extracted_functions = analysis_results.get('extracted_functions', [])
        
        target_function = None

        # Try exact match first
        for func in extracted_functions:
            if (func.get('name') == function_name and
                func.get('file_path') == file_path):
                target_function = func
                break

        # If exact match fails, try fuzzy matching
        if not target_function:
            target_function = self._find_function_fuzzy_match(
                function_name, file_path, extracted_functions
            )

        if not target_function:
            logger.warning(f"Function {function_name} not found in {file_path} (tried fuzzy matching)")
            return None
        
        # Read the actual file to get complete implementation
        full_file_path = os.path.join(repo_path, file_path)
        if not os.path.exists(full_file_path):
            logger.warning(f"Source file not found: {file_path}")
            return None
        
        try:
            with open(full_file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
            
            # Extract function implementation with context
            implementation_code = await self._extract_function_with_context(
                target_function, file_content, analysis_results
            )
            
            # Extract dependencies
            dependencies = await self._extract_function_dependencies(
                target_function, file_content, analysis_results
            )
            
            # Determine business context
            business_context = await self._determine_business_context(
                target_function, tool, analysis_results
            )
            
            return ExtractedImplementation(
                function_name=function_name,
                full_signature=target_function.get('signature', ''),
                implementation_code=implementation_code,
                file_path=file_path,
                dependencies=dependencies,
                parameters=target_function.get('parameters', []),
                return_type=target_function.get('return_type'),
                docstring=target_function.get('docstring'),
                is_async=target_function.get('is_async', False),
                business_context=business_context
            )
            
        except Exception as e:
            logger.error(f"Failed to extract function {function_name}: {str(e)}")
            return None
    
    async def _find_matching_implementation(
        self, 
        tool_name: str, 
        tool_description: str, 
        analysis_results: Dict[str, Any], 
        repo_path: str
    ) -> Optional[ExtractedImplementation]:
        """Find a matching implementation based on tool name and description"""
        
        extracted_functions = analysis_results.get('extracted_functions', [])
        
        # Score functions based on name similarity and description matching
        scored_functions = []
        
        for func in extracted_functions:
            score = self._calculate_function_match_score(
                func, tool_name, tool_description
            )
            if score > 0.3:  # Minimum threshold
                scored_functions.append((func, score))
        
        if not scored_functions:
            logger.warning(f"No matching functions found for tool: {tool_name}")
            return None
        
        # Sort by score and take the best match
        scored_functions.sort(key=lambda x: x[1], reverse=True)
        best_function, best_score = scored_functions[0]
        
        logger.info(f"Found matching function {best_function.get('name')} with score {best_score}")
        
        # Extract the implementation
        return await self._extract_specific_function(
            best_function.get('name'),
            best_function.get('file_path'),
            {'name': tool_name, 'description': tool_description},
            analysis_results,
            repo_path
        )
    
    def _calculate_function_match_score(
        self, 
        func: Dict[str, Any], 
        tool_name: str, 
        tool_description: str
    ) -> float:
        """Calculate how well a function matches a tool"""
        
        score = 0.0
        func_name = func.get('name', '').lower()
        func_docstring = (func.get('docstring') or '').lower()
        
        tool_name_lower = tool_name.lower()
        tool_desc_lower = tool_description.lower()
        
        # Name similarity
        if tool_name_lower in func_name or func_name in tool_name_lower:
            score += 0.5
        
        # Check for common words
        tool_words = set(tool_name_lower.replace('_', ' ').split())
        func_words = set(func_name.replace('_', ' ').split())
        
        common_words = tool_words.intersection(func_words)
        if common_words:
            score += len(common_words) * 0.2
        
        # Description matching
        if tool_desc_lower and func_docstring:
            desc_words = set(tool_desc_lower.split())
            doc_words = set(func_docstring.split())
            common_desc_words = desc_words.intersection(doc_words)
            if common_desc_words:
                score += len(common_desc_words) * 0.1
        
        # Function type bonuses
        if any(keyword in func_name for keyword in ['send', 'email', 'mail']):
            if any(keyword in tool_name_lower for keyword in ['send', 'email', 'mail']):
                score += 0.3
        
        if any(keyword in func_name for keyword in ['get', 'fetch', 'retrieve']):
            if any(keyword in tool_name_lower for keyword in ['get', 'fetch', 'retrieve']):
                score += 0.3
        
        return min(score, 1.0)  # Cap at 1.0
    
    async def _extract_function_with_context(
        self, 
        function: Dict[str, Any], 
        file_content: str, 
        analysis_results: Dict[str, Any]
    ) -> str:
        """Extract function implementation with necessary context"""
        
        lines = file_content.split('\n')
        start_line = function.get('line_number', 1) - 1
        
        # Find the end of the function
        end_line = start_line
        indent_level = None
        
        for i in range(start_line, len(lines)):
            line = lines[i]
            
            if indent_level is None and line.strip():
                # Determine the indentation level of the function
                indent_level = len(line) - len(line.lstrip())
            
            if (i > start_line and line.strip() and 
                len(line) - len(line.lstrip()) <= indent_level and 
                not line.lstrip().startswith(('@', '#', '"""', "'''"))):
                end_line = i
                break
        else:
            end_line = len(lines)
        
        # Extract the function code
        function_lines = lines[start_line:end_line]
        
        # Add necessary imports and context
        imports = self._extract_relevant_imports(file_content, function_lines)
        
        # Combine imports and function
        complete_code = '\n'.join(imports) + '\n\n' + '\n'.join(function_lines)
        
        return complete_code
    
    def _extract_relevant_imports(self, file_content: str, function_lines: List[str]) -> List[str]:
        """Extract imports that are relevant to the function"""
        
        imports = []
        function_text = '\n'.join(function_lines)
        
        # Find all imports in the file
        import_lines = []
        for line in file_content.split('\n'):
            stripped = line.strip()
            if (stripped.startswith('import ') or 
                stripped.startswith('from ') or
                stripped.startswith('const ') and 'require(' in stripped or
                stripped.startswith('import {') or
                stripped.startswith('import *')):
                import_lines.append(line)
        
        # Check which imports are used in the function
        for import_line in import_lines:
            # Simple heuristic: if any word from the import appears in the function
            import_words = import_line.replace(',', ' ').replace('{', ' ').replace('}', ' ').split()
            for word in import_words:
                if len(word) > 2 and word in function_text:
                    imports.append(import_line)
                    break
        
        return imports
    
    async def _extract_function_dependencies(
        self, 
        function: Dict[str, Any], 
        file_content: str, 
        analysis_results: Dict[str, Any]
    ) -> List[str]:
        """Extract dependencies needed by the function"""
        
        dependencies = []
        
        # Extract from function body
        function_body = function.get('body', '')
        
        # Common dependency patterns
        dependency_patterns = [
            r'import\s+(\w+)',
            r'from\s+(\w+)\s+import',
            r'require\([\'"]([^\'"]+)[\'"]',
            r'@(\w+)',  # Decorators
        ]
        
        import re
        for pattern in dependency_patterns:
            matches = re.findall(pattern, function_body)
            dependencies.extend(matches)
        
        # Remove duplicates and common built-ins
        builtin_modules = {'os', 'sys', 'json', 'time', 'datetime', 'logging', 're'}
        dependencies = list(set(dep for dep in dependencies if dep not in builtin_modules))
        
        return dependencies
    
    async def _determine_business_context(
        self, 
        function: Dict[str, Any], 
        tool: Dict[str, Any], 
        analysis_results: Dict[str, Any]
    ) -> str:
        """Determine the business context of the function"""
        
        context_parts = []
        
        # Add function purpose
        if function.get('docstring'):
            context_parts.append(f"Purpose: {function['docstring']}")
        
        # Add business patterns
        business_patterns = analysis_results.get('business_patterns', {})
        func_name = function.get('name', '')
        
        for pattern_type, patterns in business_patterns.items():
            for pattern in patterns:
                if pattern.get('function') == func_name:
                    context_parts.append(f"Business Pattern: {pattern_type}")
                    break
        
        # Add API context if applicable
        api_endpoints = analysis_results.get('api_endpoints', [])
        file_path = function.get('file_path', '')
        
        related_endpoints = [ep for ep in api_endpoints if ep.get('file_path') == file_path]
        if related_endpoints:
            context_parts.append(f"Related API endpoints: {len(related_endpoints)}")
        
        return '; '.join(context_parts) if context_parts else "General utility function"

    def _find_function_fuzzy_match(
        self,
        target_function_name: str,
        target_file_path: str,
        extracted_functions: List[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Find function using fuzzy matching for better compatibility"""

        # Common function name mappings and patterns
        function_mappings = {
            'send_email': ['send_welcome_email', 'send_mail', 'email_send', 'send_notification'],
            'print_banner': ['print_banner', 'show_banner', 'display_banner'],
            'print_step': ['print_step', 'show_step', 'display_step'],
            'print_info': ['print_info', 'log_info', 'show_info'],
            'print_success': ['print_success', 'log_success', 'show_success'],
            'print_warning': ['print_warning', 'log_warning', 'show_warning'],
            'print_error': ['print_error', 'log_error', 'show_error'],
            'load_env_vars': ['load_existing_env_vars', 'load_env', 'get_env_vars'],
            'mask_value': ['mask_sensitive_value', 'mask_data', 'hide_sensitive'],
            'save_progress': ['save_progress', 'save_state', 'store_progress']
        }

        # Get potential matches for the target function
        potential_matches = function_mappings.get(target_function_name, [target_function_name])

        # Look for functions in the same file that match any of the potential names
        for func in extracted_functions:
            func_name = func.get('name', '')
            func_file = func.get('file_path', '')

            # Check if it's in the right file (or similar file)
            if target_file_path in func_file or func_file in target_file_path:
                # Check if function name matches any potential match
                for potential_name in potential_matches:
                    if potential_name in func_name or func_name in potential_name:
                        logger.info(f"Fuzzy match found: {target_function_name} -> {func_name} in {func_file}")
                        return func

                # Check for partial matches (contains the target name)
                if target_function_name in func_name:
                    logger.info(f"Partial match found: {target_function_name} -> {func_name} in {func_file}")
                    return func

        # If still no match, look for functions with similar names in any file
        for func in extracted_functions:
            func_name = func.get('name', '')

            # Check for substring matches
            if target_function_name in func_name or func_name in target_function_name:
                logger.info(f"Cross-file match found: {target_function_name} -> {func_name} in {func.get('file_path', '')}")
                return func

        return None
