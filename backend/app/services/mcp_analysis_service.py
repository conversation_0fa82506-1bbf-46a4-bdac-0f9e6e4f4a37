import os
import async<PERSON>
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import anthropic
import requests
from app.services.github_service import GitHubService

# Try to import Tavily<PERSON><PERSON>, but make it optional
try:
    from app.services.tavily_client import TavilyClient
    TAVILY_AVAILABLE = True
except ImportError:
    print("Warning: TavilyClient not available. MCP marketplace search will be limited.")
    TavilyClient = None
    TAVILY_AVAILABLE = False

@dataclass
class MCPCapability:
    capability: str
    underlying_tech: str
    exposed_via_api: str
    candidate_tool_name: str

@dataclass
class MCPServerSpec:
    name: str
    description: str
    parameters: List[Dict[str, Any]]

@dataclass
class ExistingMCPServer:
    server_name: str
    overlapping_tools: str
    when_to_reuse: str

@dataclass
class MCPAnalysisResult:
    executive_summary: str
    capability_matrix: List[MCPCapability]
    new_mcp_server_specs: List[MCPServerSpec]
    existing_mcp_servers: List[ExistingMCPServer]
    gap_analysis: str
    implementation_starter: str
    client_config_snippet: str

class MCPAnalysisService:
    def __init__(self):
        # Initialize Anthropic client for analysis
        anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')
        if anthropic_api_key:
            self.anthropic_client = anthropic.Anthropic(api_key=anthropic_api_key)
        else:
            self.anthropic_client = None
            print("Warning: ANTHROPIC_API_KEY not set. MCP analysis will use mock data.")

        # Initialize Tavily client for MCP marketplace search
        tavily_api_key = os.getenv('TAVILY_API_KEY')
        if tavily_api_key and TAVILY_AVAILABLE:
            self.tavily_client = TavilyClient()
        else:
            self.tavily_client = None
            if not TAVILY_AVAILABLE:
                print("Warning: TavilyClient not available. MCP marketplace search will be limited.")
            elif not tavily_api_key:
                print("Warning: TAVILY_API_KEY not set. MCP marketplace search will be limited.")

        self.github_service = GitHubService()

    async def analyze_mcp_opportunities(self, repo_url: str, analysis_id: int = None) -> MCPAnalysisResult:
        """Analyze a repository for MCP opportunities using the specific prompt template."""
        try:
            # Get repository context
            repo_context = await self.get_repository_context(repo_url, analysis_id)

            # Generate MCP analysis using the specific prompt template
            analysis = await self.generate_mcp_analysis(repo_url, repo_context)

            return analysis
        except Exception as e:
            print(f"Error analyzing MCP opportunities: {e}")
            raise Exception("Failed to analyze MCP opportunities")

    async def analyze_mcp_opportunities_with_context(self, repo_url: str, comprehensive_context: str) -> MCPAnalysisResult:
        """Analyze a repository for MCP opportunities using provided comprehensive context."""
        try:
            # Generate MCP analysis using the comprehensive context
            analysis = await self.generate_mcp_analysis(repo_url, comprehensive_context)

            return analysis
        except Exception as e:
            print(f"Error analyzing MCP opportunities with context: {e}")
            raise Exception("Failed to analyze MCP opportunities with context")

    async def get_repository_context(self, repo_url: str, analysis_id: int = None) -> str:
        """Get comprehensive repository context for analysis."""
        try:
            # Extract owner and repo from URL
            url_parts = repo_url.replace('https://github.com/', '').split('/')
            owner = url_parts[0]
            repo_name = url_parts[1]

            # Try to get existing analysis data from database
            context_parts = [f"Repository: {repo_url}"]

            if analysis_id:
                try:
                    from ..database import SessionLocal
                    from ..models import RepoAnalysis

                    db = SessionLocal()
                    analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

                    if analysis and analysis.analysis_results:
                        results = analysis.analysis_results

                        # Add basic repository info
                        context_parts.extend([
                            f"Name: {analysis.repo_name}",
                            f"Owner: {analysis.repo_owner}",
                            f"Status: {analysis.status}"
                        ])

                        # Add analysis results
                        if isinstance(results, dict):
                            if 'languages' in results:
                                context_parts.append(f"Languages: {results['languages']}")

                            if 'dependencies' in results:
                                deps = results.get('dependencies', [])
                                if deps:
                                    context_parts.append(f"Dependencies ({len(deps)}): {', '.join(deps[:10])}")

                            if 'api_endpoints' in results:
                                endpoints = results.get('api_endpoints', [])
                                if endpoints:
                                    context_parts.append(f"API Endpoints ({len(endpoints)}): {', '.join(endpoints[:5])}")

                            if 'file_structure' in results:
                                structure = results.get('file_structure', {})
                                if structure:
                                    context_parts.append(f"File Structure: {str(structure)[:500]}...")

                            if 'technologies' in results:
                                tech = results.get('technologies', [])
                                if tech:
                                    context_parts.append(f"Technologies: {', '.join(tech)}")

                            if 'frameworks' in results:
                                frameworks = results.get('frameworks', [])
                                if frameworks:
                                    context_parts.append(f"Frameworks: {', '.join(frameworks)}")

                    db.close()

                except Exception as db_error:
                    print(f"Error getting analysis data: {db_error}")

            # If we don't have much context, add some basic info
            if len(context_parts) <= 3:
                context_parts.extend([
                    f"Repository Name: {repo_name}",
                    f"Owner: {owner}",
                ])

                # Add specific context for known repositories
                if repo_name.lower() == 'supermcp':
                    context_parts.extend([
                        "Description: SuperMCP - A comprehensive MCP (Model Context Protocol) analysis and generation platform",
                        "Technologies: Python (FastAPI backend), TypeScript/React (Next.js frontend), Docker, Redis, PostgreSQL, Celery",
                        "Languages: Python, TypeScript, JavaScript",
                        "Frameworks: FastAPI, Next.js, React, Tailwind CSS",
                        "Dependencies: anthropic, openai, tavily-python, sqlalchemy, redis, celery, pydantic",
                        "API Endpoints: /analysis, /mcp-generation, /auth, /mcp-analysis, /chat",
                        "File Structure: backend/ (Python FastAPI), frontend/ (Next.js), docker-compose.yml, requirements.txt",
                        "Key Features: Repository analysis, MCP server generation, AI-powered suggestions, chat interface, background task processing",
                        "Database: PostgreSQL with SQLAlchemy ORM",
                        "Background Tasks: Celery with Redis broker",
                        "AI Integration: Anthropic Claude, OpenAI, Tavily search",
                        "Authentication: JWT-based auth system",
                        "Deployment: Docker containerized with multi-service architecture"
                    ])
                else:
                    context_parts.append("This is a software repository that may contain various technologies and frameworks suitable for MCP server development.")

            return "\n".join(context_parts)

        except Exception as e:
            print(f"Error getting repository context: {e}")
            return f"Repository: {repo_url}\nBasic analysis for MCP opportunities."

    async def search_existing_mcp_servers(self, capabilities: List[str]) -> str:
        """Search for existing MCP servers using Tavily."""
        if not self.tavily_client:
            # Return mock search results when Tavily is not available
            return """Found existing MCP servers:
- filesystem-mcp: https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem
- git-mcp: https://github.com/modelcontextprotocol/servers/tree/main/src/git
- github-mcp: https://github.com/modelcontextprotocol/servers/tree/main/src/github
- sqlite-mcp: https://github.com/modelcontextprotocol/servers/tree/main/src/sqlite
- web-search-mcp: https://github.com/modelcontextprotocol/servers/tree/main/src/web-search"""

        try:
            # Search for MCP servers related to the capabilities
            search_queries = [
                f"MCP server {capability} site:github.com/modelcontextprotocol/servers"
                for capability in capabilities[:3]  # Limit to top 3 capabilities
            ]

            # Also search marketplace sites
            marketplace_queries = []
            for capability in capabilities[:3]:
                marketplace_queries.extend([
                    f"MCP server {capability} site:mcp.so",
                    f"MCP server {capability} site:mcpmarket.com",
                    f"Model Context Protocol {capability} site:mcpmarketplace.org"
                ])

            all_results = []

            # Search each query
            for query in search_queries + marketplace_queries:
                try:
                    results = await self.tavily_client.search(query, max_results=3)
                    if results and 'results' in results:
                        all_results.extend(results['results'])
                except Exception as e:
                    print(f"Error searching for '{query}': {e}")
                    continue

            # Format results for the prompt
            if all_results:
                formatted_results = "\n".join([
                    f"- {result.get('title', 'Unknown')}: {result.get('url', '')}"
                    for result in all_results[:10]  # Limit to top 10 results
                ])
                return f"Found existing MCP servers:\n{formatted_results}"
            else:
                return "No existing MCP servers found in marketplace search"

        except Exception as e:
            print(f"Error searching MCP marketplace: {e}")
            return "Error occurred while searching MCP marketplace"

    def extract_basic_capabilities(self, repo_context: str) -> List[str]:
        """Extract basic capabilities from repository context for search."""
        capabilities = []

        # Look for common patterns in the context
        context_lower = repo_context.lower()

        # Common capability keywords
        capability_keywords = [
            'api', 'database', 'web', 'server', 'client', 'auth', 'authentication',
            'file', 'storage', 'cache', 'queue', 'message', 'notification',
            'email', 'sms', 'payment', 'analytics', 'logging', 'monitoring',
            'docker', 'kubernetes', 'aws', 'gcp', 'azure', 'cloud',
            'git', 'github', 'gitlab', 'ci', 'cd', 'deployment',
            'test', 'testing', 'mock', 'stub', 'data', 'json', 'xml',
            'http', 'rest', 'graphql', 'websocket', 'grpc'
        ]

        for keyword in capability_keywords:
            if keyword in context_lower:
                capabilities.append(keyword)

        # Return top 5 capabilities
        return capabilities[:5] if capabilities else ['general', 'utility']

    def get_mock_analysis_result(self, repo_url: str) -> MCPAnalysisResult:
        """NO MOCK DATA - Fail fast if real analysis is not available"""
        raise Exception(f"Mock analysis not allowed for {repo_url}. MCP generation requires real repository analysis and cannot proceed with mock/placeholder data.")

    async def generate_mcp_analysis(self, repo_url: str, repo_context: str) -> MCPAnalysisResult:
        """Generate MCP analysis using the specific prompt template."""

        # Extract basic capabilities for marketplace search
        basic_capabilities = self.extract_basic_capabilities(repo_context)

        # Search for existing MCP servers
        marketplace_search_results = await self.search_existing_mcp_servers(basic_capabilities)

        prompt = f"""
## Role
You are "MCP Architect", an expert in mapping GitHub repositories to Model-Context-Protocol (MCP) servers and client workflows. You produce actionable, strategic analysis that helps developers leverage existing MCP servers and build targeted new ones.

---

## Inputs
1. Repository: {repo_url}
2. Goal: Identify (a) net-new MCP servers we could build from this repo, and (b) existing MCP servers that already cover parts of its surface area.

## Repository Context (All information provided below):
{repo_context}

**IMPORTANT**: Use ONLY the repository context provided above. Do not attempt to access the repository URL directly. All necessary information about the repository is included in the context above.

## Existing MCP Marketplace Intelligence:
{marketplace_search_results}

---

## Analysis Steps

### 1. 📦 Capability Inventory (analyze the provided repository context)
Based on the repository context provided above, identify SPECIFIC capabilities with their underlying technologies. Focus on capabilities that could be exposed as MCP tools:

| Capability | Underlying Tech | Exposed via API/CLI? | Candidate MCP Tool Name |
|------------|-----------------|----------------------|-------------------------|
|            |                 |                      |                         |

### 2. 🧩 Map to MCP Server Ideas
Turn every row in step 1 into a **one-sentence YAML spec**:

```yaml
- name: <tool_name>
  description: One-line purpose
  parameters:
    - name: <arg>
      type: string|int|bool
      required: true|false
```

### 3. 🔍 Existing-MCP Scan
Based on marketplace search results:
{marketplace_search_results}

List the **top 5 servers** whose tools overlap with the repo's features:

| Existing Server | Overlapping Tools | When to Re-use Instead of Building |
|-----------------|-------------------|------------------------------------|
|                 |                   |                                    |

### 4. 🚦 Gap Analysis
Highlight capabilities **not** covered by any existing server.  
These justify a **new** MCP server.

### 5. 🛠️ Minimal Implementation Starter
Pick the **highest-value gap** and show a < 30-line Python or Node starter:

```python
# mcp_<repo>_gap/server.py
from mcp.server import Server
...

@app.call_tool()
async def <tool>(...): ...
```

### 6. 🧪 Client Workflow Recipe
Provide a ready-to-paste `claude_desktop_config.json` (or Cursor/Windsurf equivalent) that mixes **existing** and **new** MCP servers.

---

## Output Format
**CRITICAL**: You MUST provide your analysis in the following EXACT structured format with numbered sections. Do not deviate from this format:

1. Executive Summary
[Provide a strategic overview in ≤100 words that includes: what the repository does, percentage of capabilities that can be leveraged through existing MCP servers, number of net-new MCP servers recommended, and key value proposition for MCP integration]

2. Capability Matrix
[Create a table with columns: Capability | Underlying Tech | Exposed via API/CLI? | Candidate MCP Tool Name]

3. New MCP Server Specs
[Provide YAML specifications for new MCP servers with name, description, and parameters]

4. Existing MCP Servers to Re-use
[Create a table with columns: Existing Server | Overlapping Tools | When to Re-use Instead of Building]

5. Gap Analysis
[Highlight capabilities not covered by existing servers that justify new development]

6. Implementation Starter
[Provide 25-30 line working Python MCP server code for the highest-value gap]

7. Client Config Snippet
[Provide ready-to-paste JSON configuration mixing existing and new servers]

## Strategic Guidelines:
- **Be specific**: Use actual technology names, API endpoints, and implementation details from the repository context
- **Think strategically**: Consider what percentage of capabilities can be reused vs built new
- **Focus on value**: Prioritize capabilities that provide the most value when exposed as MCP tools
- **Be actionable**: Provide working code examples and ready-to-use configurations
- **Consider the ecosystem**: Leverage existing MCP servers when possible, build new ones only for unique capabilities

Please ensure each section is clearly marked and formatted for easy parsing.
        """

        try:
            if self.anthropic_client is None:
                # Return mock data when Anthropic is not available
                return self.get_mock_analysis_result(repo_url)

            response = await asyncio.to_thread(
                self.anthropic_client.messages.create,
                model='claude-3-5-sonnet-20241022',
                max_tokens=4000,
                temperature=0.7,
                system='You are an expert MCP architect. Provide detailed, structured analysis following the exact format requested.',
                messages=[
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            )

            analysis_text = response.content[0].text if response.content else ''

            # Debug: Log the raw response
            print(f"DEBUG: Raw Anthropic response length: {len(analysis_text)}")
            print(f"DEBUG: First 1000 chars: {analysis_text[:1000]}")
            print(f"DEBUG: Last 500 chars: {analysis_text[-500:]}")
            print(f"DEBUG: Full response: {analysis_text}")

            # Parse the structured response
            return self.parse_analysis_response(analysis_text)
        except Exception as e:
            print(f"Error generating MCP analysis: {e}")
            # Return a default structure if analysis fails
            return MCPAnalysisResult(
                executive_summary="Analysis in progress...",
                capability_matrix=[],
                new_mcp_server_specs=[],
                existing_mcp_servers=[],
                gap_analysis="Gap analysis in progress...",
                implementation_starter="# Implementation starter will be generated...",
                client_config_snippet='{"mcps": {}}'
            )

    def parse_analysis_response(self, analysis_text: str) -> MCPAnalysisResult:
        """Parse the structured response from the LLM."""
        sections = self.split_into_sections(analysis_text)

        # Debug: Log parsed sections
        print(f"DEBUG: Parsed sections: {list(sections.keys())}")
        for key, value in sections.items():
            print(f"DEBUG: Section '{key}': {len(value)} chars")

        return MCPAnalysisResult(
            executive_summary=self.extract_section(sections, 'Executive Summary') or 'Analysis completed.',
            capability_matrix=self.parse_capability_matrix(self.extract_section(sections, 'Capability Matrix')),
            new_mcp_server_specs=self.parse_mcp_server_specs(self.extract_section(sections, 'New MCP Server Specs')),
            existing_mcp_servers=self.parse_existing_servers(self.extract_section(sections, 'Existing MCP Servers')),
            gap_analysis=self.extract_section(sections, 'Gap Analysis') or 'Gap analysis completed.',
            implementation_starter=self.extract_code_block(self.extract_section(sections, 'Implementation Starter')),
            client_config_snippet=self.extract_code_block(self.extract_section(sections, 'Client Config Snippet')),
        )

    def split_into_sections(self, text: str) -> Dict[str, str]:
        """Split the analysis text into sections with enhanced parsing."""
        sections = {}
        lines = text.split('\n')
        current_section = ''
        current_content = []

        for line in lines:
            line_stripped = line.strip()

            # Check for numbered sections (1., 2., 3., etc.)
            if line_stripped.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.')):
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()
                current_section = line_stripped.split('.', 1)[1].strip()
                current_content = []
            # Check for markdown headers (### 1., ## 1., etc.)
            elif line_stripped.startswith(('### 1.', '### 2.', '### 3.', '### 4.', '### 5.', '### 6.', '### 7.',
                                          '## 1.', '## 2.', '## 3.', '## 4.', '## 5.', '## 6.', '## 7.')):
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()
                # Extract section name after the number
                section_part = line_stripped.replace('###', '').replace('##', '').strip()
                current_section = section_part.split('.', 1)[1].strip()
                current_content = []
            # Check for section headers without numbers
            elif any(keyword in line_stripped.lower() for keyword in [
                'executive summary', 'capability matrix', 'new mcp server specs',
                'existing mcp servers', 'gap analysis', 'implementation starter',
                'client config snippet'
            ]) and (line_stripped.startswith('#') or line_stripped.endswith(':')):
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()
                current_section = line_stripped.replace('#', '').replace(':', '').strip()
                current_content = []
            else:
                current_content.append(line)

        if current_section:
            sections[current_section] = '\n'.join(current_content).strip()

        return sections

    def extract_section(self, sections: Dict[str, str], section_name: str) -> str:
        """Extract a specific section from the parsed sections."""
        for key, value in sections.items():
            if section_name.lower() in key.lower():
                return value
        return ''

    def parse_capability_matrix(self, text: str) -> List[MCPCapability]:
        """Parse the capability matrix table."""
        capabilities = []
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # Filter out header separator lines and empty lines
        data_lines = []
        for line in lines:
            # Skip lines that are just dashes/separators
            if line.startswith('---') or line.startswith('----') or line.startswith('==='):
                continue
            # Skip lines that are mostly dashes and spaces
            if all(c in '-| \t' for c in line):
                continue
            data_lines.append(line)

        # Skip header line and process data
        for line in data_lines[1:]:  # Skip header
            if '|' in line:
                cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                if len(cells) >= 4:
                    # Skip if this looks like a separator row
                    if all(cell in ['---', '----', '-----', '------'] or 'No' in cell for cell in cells[:2]):
                        continue
                    capabilities.append(MCPCapability(
                        capability=cells[0],
                        underlying_tech=cells[1],
                        exposed_via_api=cells[2],
                        candidate_tool_name=cells[3]
                    ))

        return capabilities

    def parse_mcp_server_specs(self, text: str) -> List[MCPServerSpec]:
        """Parse MCP server specifications from YAML."""
        specs = []
        try:
            import re
            import yaml

            # Extract YAML blocks from the text
            yaml_blocks = re.findall(r'```yaml\s*(.*?)\s*```', text, re.DOTALL)

            for yaml_block in yaml_blocks:
                try:
                    # Parse YAML content
                    yaml_data = yaml.safe_load(yaml_block)

                    if isinstance(yaml_data, list):
                        for item in yaml_data:
                            if isinstance(item, dict) and 'name' in item:
                                # Extract parameters
                                parameters = {}
                                if 'parameters' in item and isinstance(item['parameters'], list):
                                    for param in item['parameters']:
                                        if isinstance(param, dict) and 'name' in param:
                                            parameters[param['name']] = {
                                                'type': param.get('type', 'string'),
                                                'required': param.get('required', False),
                                                'description': param.get('description', '')
                                            }

                                specs.append(MCPServerSpec(
                                    name=item.get('name', ''),
                                    description=item.get('description', ''),
                                    parameters=parameters
                                ))
                except yaml.YAMLError as e:
                    print(f"YAML parsing error: {e}")
                    continue

        except Exception as e:
            print(f"Error parsing MCP server specs: {e}")

        return specs

    def parse_existing_servers(self, text: str) -> List[ExistingMCPServer]:
        """Parse existing MCP servers table."""
        servers = []
        lines = [line.strip() for line in text.split('\n') if line.strip() and not line.strip().startswith('---')]
        
        for line in lines[1:]:  # Skip header
            if '|' in line:
                cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                if len(cells) >= 3:
                    servers.append(ExistingMCPServer(
                        server_name=cells[0],
                        overlapping_tools=cells[1],
                        when_to_reuse=cells[2]
                    ))
        
        return servers

    def extract_code_block(self, text: str) -> str:
        """Extract code blocks from text."""
        import re
        code_block_match = re.search(r'```[\s\S]*?```', text)
        return code_block_match.group(0) if code_block_match else text.strip()
