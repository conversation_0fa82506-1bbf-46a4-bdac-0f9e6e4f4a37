"""
MCP Marketplace Integration Service
Searches and suggests existing MCPs from various marketplaces
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .tavily_client import TavilyClient
from .intelligent_analysis_service import IntelligentAnalysisService

logger = logging.getLogger(__name__)

@dataclass
class MarketplaceMCP:
    name: str
    description: str
    url: str
    category: str
    compatibility_score: float
    installation_method: str
    author: str
    tags: List[str]

class MCPMarketplaceService:
    """Service for discovering and suggesting MCPs from marketplaces"""
    
    def __init__(self):
        self.tavily_client = TavilyClient()
        self.ai_service = IntelligentAnalysisService()
        
        # Known MCP marketplaces and repositories
        self.marketplace_sources = [
            "https://github.com/modelcontextprotocol/servers",
            "https://mcpmarket.com",
            "https://mcp.so",
            "https://github.com/topics/mcp-server",
            "https://github.com/topics/model-context-protocol"
        ]
    
    async def search_compatible_mcps(
        self, 
        user_goals: str, 
        repository_context: Dict[str, Any]
    ) -> List[MarketplaceMCP]:
        """Search for MCPs that are compatible with user goals and repository"""
        
        logger.info(f"Searching for MCPs compatible with: {user_goals}")
        
        # Build search queries based on user goals and repository context
        search_queries = await self._build_search_queries(user_goals, repository_context)
        
        # Search across different sources
        all_mcps = []
        for query in search_queries:
            mcps = await self._search_mcps_by_query(query, repository_context)
            all_mcps.extend(mcps)
        
        # Deduplicate and score MCPs
        unique_mcps = self._deduplicate_mcps(all_mcps)
        scored_mcps = await self._score_mcp_compatibility(unique_mcps, user_goals, repository_context)
        
        # Return top 10 most compatible MCPs
        return sorted(scored_mcps, key=lambda x: x.compatibility_score, reverse=True)[:10]
    
    async def get_category_mcps(self, category: str) -> List[MarketplaceMCP]:
        """Get MCPs for a specific category"""
        
        category_queries = {
            'content_management': ['strapi mcp', 'cms mcp', 'content management mcp'],
            'api_integration': ['api mcp', 'rest api mcp', 'webhook mcp'],
            'database': ['database mcp', 'sql mcp', 'mongodb mcp', 'postgres mcp'],
            'file_management': ['file mcp', 'storage mcp', 's3 mcp'],
            'notification': ['notification mcp', 'email mcp', 'slack mcp'],
            'automation': ['automation mcp', 'workflow mcp', 'task mcp'],
            'development': ['git mcp', 'github mcp', 'ci cd mcp']
        }
        
        queries = category_queries.get(category.lower(), [f'{category} mcp'])
        
        all_mcps = []
        for query in queries:
            mcps = await self._search_mcps_by_query(query, {})
            all_mcps.extend(mcps)
        
        return self._deduplicate_mcps(all_mcps)[:15]
    
    async def _build_search_queries(
        self, 
        user_goals: str, 
        repository_context: Dict[str, Any]
    ) -> List[str]:
        """Build targeted search queries based on user goals and repository"""
        
        repo_language = repository_context.get('language', '').lower()
        repo_technologies = repository_context.get('technologies', [])
        
        # Base queries from user goals
        base_queries = [
            f"{user_goals} mcp",
            f"{user_goals} model context protocol"
        ]
        
        # Technology-specific queries
        tech_queries = []
        for tech in repo_technologies[:3]:  # Top 3 technologies
            tech_queries.extend([
                f"{tech} mcp server",
                f"{tech} {user_goals} mcp"
            ])
        
        # Language-specific queries
        if repo_language:
            tech_queries.extend([
                f"{repo_language} mcp",
                f"{repo_language} {user_goals} mcp"
            ])
        
        # Combine and limit queries
        all_queries = base_queries + tech_queries
        return all_queries[:8]  # Limit to 8 queries to avoid rate limits
    
    async def _search_mcps_by_query(
        self, 
        query: str, 
        repository_context: Dict[str, Any]
    ) -> List[MarketplaceMCP]:
        """Search for MCPs using a specific query"""
        
        try:
            # Use Tavily to search for MCPs
            search_results = await self.tavily_client.search(
                query=query,
                max_results=5
            )
            
            mcps = []
            for result in search_results:
                # Parse search result into MCP format
                mcp = await self._parse_search_result_to_mcp(result, query)
                if mcp:
                    mcps.append(mcp)
            
            return mcps
            
        except Exception as e:
            logger.error(f"Failed to search MCPs for query '{query}': {str(e)}")
            return []
    
    async def _parse_search_result_to_mcp(
        self, 
        search_result: Dict[str, Any], 
        query: str
    ) -> Optional[MarketplaceMCP]:
        """Parse a search result into an MCP object"""
        
        try:
            url = search_result.get('url', '')
            title = search_result.get('title', '')
            content = search_result.get('content', '')
            
            # Skip if not MCP-related
            if not any(keyword in content.lower() for keyword in ['mcp', 'model context protocol', 'mcp-server']):
                return None
            
            # Extract MCP information using AI
            mcp_info = await self._extract_mcp_info_with_ai(title, content, url)
            
            return MarketplaceMCP(
                name=mcp_info.get('name', title),
                description=mcp_info.get('description', content[:200]),
                url=url,
                category=mcp_info.get('category', 'general'),
                compatibility_score=0.5,  # Will be scored later
                installation_method=mcp_info.get('installation', 'npm install'),
                author=mcp_info.get('author', 'Unknown'),
                tags=mcp_info.get('tags', [])
            )
            
        except Exception as e:
            logger.error(f"Failed to parse search result: {str(e)}")
            return None
    
    async def _extract_mcp_info_with_ai(
        self, 
        title: str, 
        content: str, 
        url: str
    ) -> Dict[str, Any]:
        """Extract structured MCP information using AI"""
        
        prompt = f"""Extract MCP information from this search result:

Title: {title}
URL: {url}
Content: {content[:1000]}

Extract and return JSON with:
{{
  "name": "MCP name",
  "description": "Brief description",
  "category": "category (api, database, file, notification, etc.)",
  "installation": "installation command",
  "author": "author name",
  "tags": ["tag1", "tag2"]
}}"""

        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=300)
            
            import json
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                return json.loads(json_content)
            
            # Fallback parsing
            return {
                'name': title,
                'description': content[:200],
                'category': 'general',
                'installation': 'npm install',
                'author': 'Unknown',
                'tags': []
            }
            
        except Exception as e:
            logger.error(f"Failed to extract MCP info with AI: {str(e)}")
            return {
                'name': title,
                'description': content[:200],
                'category': 'general',
                'installation': 'npm install',
                'author': 'Unknown',
                'tags': []
            }
    
    def _deduplicate_mcps(self, mcps: List[MarketplaceMCP]) -> List[MarketplaceMCP]:
        """Remove duplicate MCPs based on URL and name similarity"""
        
        seen_urls = set()
        seen_names = set()
        unique_mcps = []
        
        for mcp in mcps:
            # Skip if URL already seen
            if mcp.url in seen_urls:
                continue
            
            # Skip if very similar name already seen
            name_lower = mcp.name.lower()
            if any(abs(len(name_lower) - len(seen_name)) < 3 and 
                   name_lower in seen_name or seen_name in name_lower 
                   for seen_name in seen_names):
                continue
            
            seen_urls.add(mcp.url)
            seen_names.add(name_lower)
            unique_mcps.append(mcp)
        
        return unique_mcps
    
    async def _score_mcp_compatibility(
        self, 
        mcps: List[MarketplaceMCP], 
        user_goals: str, 
        repository_context: Dict[str, Any]
    ) -> List[MarketplaceMCP]:
        """Score MCPs based on compatibility with user goals and repository"""
        
        for mcp in mcps:
            score = await self._calculate_compatibility_score(mcp, user_goals, repository_context)
            mcp.compatibility_score = score
        
        return mcps
    
    async def _calculate_compatibility_score(
        self, 
        mcp: MarketplaceMCP, 
        user_goals: str, 
        repository_context: Dict[str, Any]
    ) -> float:
        """Calculate compatibility score for an MCP"""
        
        score = 0.0
        
        # Goal relevance (40% of score)
        goal_keywords = user_goals.lower().split()
        mcp_text = f"{mcp.name} {mcp.description}".lower()
        goal_matches = sum(1 for keyword in goal_keywords if keyword in mcp_text)
        score += (goal_matches / max(len(goal_keywords), 1)) * 0.4
        
        # Technology compatibility (30% of score)
        repo_technologies = [tech.lower() for tech in repository_context.get('technologies', [])]
        tech_matches = sum(1 for tech in repo_technologies if tech in mcp_text)
        score += (tech_matches / max(len(repo_technologies), 1)) * 0.3
        
        # Language compatibility (20% of score)
        repo_language = repository_context.get('language', '').lower()
        if repo_language and repo_language in mcp_text:
            score += 0.2
        
        # Category relevance (10% of score)
        if mcp.category and mcp.category != 'general':
            score += 0.1
        
        return min(score, 1.0)  # Cap at 1.0
