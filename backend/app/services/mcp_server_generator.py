"""
MCP Server Generator Service
Generates functional MCP servers based on repository analysis and user requirements.
"""
import asyncio
import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime

class MCPServerGenerator:
    """Service for generating functional MCP servers based on repository analysis."""
    
    def __init__(self):
        self.anthropic_client = None
        self._initialize_anthropic()
    
    def _initialize_anthropic(self):
        """Initialize Anthropic client for code generation."""
        try:
            import anthropic
            import os
            
            api_key = os.getenv('ANTHROPIC_API_KEY')
            if api_key:
                self.anthropic_client = anthropic.Anthropic(api_key=api_key)
        except ImportError:
            print("Anthropic library not available")
        except Exception as e:
            print(f"Failed to initialize Anthropic client: {e}")
    
    async def generate_server(
        self,
        repository: Dict[str, str],
        analysis_data: Optional[Dict] = None,
        mcp_analysis: Optional[Dict] = None,
        chat_context: str = "",
        user_requirements: str = "",
        server_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate a functional MCP server based on the provided context."""
        
        # Create comprehensive context for generation
        context = self._create_generation_context(
            repository, analysis_data, mcp_analysis, chat_context, user_requirements, server_name
        )
        
        # Generate the MCP server code
        server_code = await self._generate_server_code(context)
        
        # Generate installation instructions
        installation_instructions = self._generate_installation_instructions(context)
        
        # Generate usage example
        usage_example = self._generate_usage_example(context)
        
        # Create downloadable files
        files = self._create_downloadable_files(server_code, context)
        
        return {
            "server_code": server_code,
            "installation_instructions": installation_instructions,
            "usage_example": usage_example,
            "files": files,
            "generated_at": datetime.now().isoformat()
        }
    
    def _create_generation_context(
        self,
        repository: Dict[str, str],
        analysis_data: Optional[Dict],
        mcp_analysis: Optional[Dict],
        chat_context: str,
        user_requirements: str,
        server_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create comprehensive context for MCP server generation."""
        
        context = {
            "repository": repository,
            "user_requirements": user_requirements,
            "chat_context": chat_context,
            "server_name": server_name or f"{repository.get('name', 'repository')}_mcp"
        }
        
        # Extract key information from analysis data
        if analysis_data:
            context["technologies"] = self._extract_technologies(analysis_data)
            context["api_capabilities"] = self._extract_api_capabilities(analysis_data)
            context["business_logic"] = self._extract_business_logic(analysis_data)
        
        # Extract MCP-specific information
        if mcp_analysis:
            context["capability_matrix"] = mcp_analysis.get("capability_matrix", [])
            context["new_mcp_specs"] = mcp_analysis.get("new_mcp_server_specs", [])
            context["executive_summary"] = mcp_analysis.get("executive_summary", "")
        
        return context
    
    def _extract_technologies(self, analysis_data: Dict) -> List[str]:
        """Extract technologies from analysis data."""
        technologies = []
        
        # Extract from various possible locations
        if "technologies" in analysis_data:
            technologies.extend(analysis_data["technologies"])
        
        if "comprehensive_analysis" in analysis_data:
            comp_analysis = analysis_data["comprehensive_analysis"]
            if "technologies" in comp_analysis:
                technologies.extend(comp_analysis["technologies"])
        
        return list(set(technologies))  # Remove duplicates
    
    def _extract_api_capabilities(self, analysis_data: Dict) -> Dict[str, Any]:
        """Extract API capabilities from analysis data."""
        api_capabilities = {}
        
        if "comprehensive_analysis" in analysis_data:
            comp_analysis = analysis_data["comprehensive_analysis"]
            if "api_capabilities" in comp_analysis:
                api_capabilities = comp_analysis["api_capabilities"]
        
        return api_capabilities
    
    def _extract_business_logic(self, analysis_data: Dict) -> Dict[str, Any]:
        """Extract business logic from analysis data."""
        business_logic = {}
        
        if "comprehensive_analysis" in analysis_data:
            comp_analysis = analysis_data["comprehensive_analysis"]
            if "business_logic" in comp_analysis:
                business_logic = comp_analysis["business_logic"]
        
        return business_logic
    
    async def _generate_server_code(self, context: Dict[str, Any]) -> str:
        """Generate the actual MCP server code using AI."""
        
        if not self.anthropic_client:
            return self._generate_fallback_server_code(context)
        
        prompt = self._create_code_generation_prompt(context)
        
        try:
            response = await asyncio.to_thread(
                self.anthropic_client.messages.create,
                model="claude-3-sonnet-20240229",
                max_tokens=3000,
                messages=[{"role": "user", "content": prompt}]
            )
            
            generated_code = response.content[0].text if response.content else ""
            
            # Extract code blocks from the response
            code_blocks = re.findall(r'```python\s*(.*?)\s*```', generated_code, re.DOTALL)
            if code_blocks:
                return code_blocks[0].strip()
            else:
                # If no code blocks found, return the whole response
                return generated_code.strip()
                
        except Exception as e:
            print(f"Error generating server code with AI: {e}")
            return self._generate_fallback_server_code(context)
    
    def _create_code_generation_prompt(self, context: Dict[str, Any]) -> str:
        """Create a prompt for generating MCP server code."""

        repo_name = context["repository"].get("name", "repository")
        user_requirements = context.get("user_requirements", "")
        chat_context = context.get("chat_context", "")
        server_name = context.get("server_name", f"{repo_name}_mcp")

        # Extract specific capabilities from MCP analysis
        mcp_analysis = context.get("mcp_analysis", {})
        capabilities = []

        if isinstance(mcp_analysis, dict):
            capabilities = mcp_analysis.get("capability_matrix", [])

        capability_descriptions = []
        for cap in capabilities[:5]:  # Top 5 capabilities
            if isinstance(cap, dict):
                capability_descriptions.append(f"- {cap.get('capability', 'Unknown')}: {cap.get('underlying_tech', 'Unknown tech')}")

        capabilities_text = '\n'.join(capability_descriptions) if capability_descriptions else "No specific capabilities identified"

        prompt = f"""
Generate a complete, functional MCP (Model Context Protocol) server for the repository "{repo_name}".

## Specific Server Requirements:
Server Name: {server_name}
User Requirements: {user_requirements}
Chat Context: {chat_context}

## Repository Context:
Repository: {context["repository"]}
Technologies: {', '.join(context.get("technologies", []))}

## Repository Capabilities to Expose as MCP Tools:
{capabilities_text}

## API Capabilities Available:
{json.dumps(context.get("api_capabilities", {}), indent=2)}

## Business Logic Context:
{json.dumps(context.get("business_logic", {}), indent=2)}

## CRITICAL Requirements:
1. Create a complete, working MCP server in Python using the mcp library
2. Generate SPECIFIC tools based on the repository's actual capabilities
3. Use the repository's actual API endpoints, business logic, and technologies
4. Include proper error handling, logging, and documentation
5. Make it production-ready with real functionality
6. Focus on the specific use case mentioned in user requirements
7. Create 3-5 tools that provide REAL VALUE for this specific repository
8. Use actual repository data, not generic placeholders

## Tool Ideas Based on Repository Analysis:
- If it's a web app: create tools for API interactions, data retrieval, user management
- If it's a data processing app: create tools for data analysis, transformation, reporting
- If it's a browser automation app: create tools for scraping, automation, testing
- If it's an AI/ML app: create tools for model interaction, data processing, inference

## Output Format:
Provide ONLY the complete Python code for the MCP server. No explanations or markdown.
The code must be functional and ready to run with minimal setup.
Include proper imports, error handling, and at least 3-5 meaningful tools.
"""

        return prompt
    
    def _generate_fallback_server_code(self, context: Dict[str, Any]) -> str:
        """NO FALLBACK SERVER CODE - Fail fast when real generation fails"""
        raise Exception("Fallback server code generation not allowed. MCP generation must use real repository implementations and cannot proceed with template-based fallbacks.")

        return f'''#!/usr/bin/env python3
"""
MCP Server for {repo_name}
Generated by SuperMCP
"""

import asyncio
import logging
from typing import Any, Dict, List
from mcp.server import Server
from mcp.types import Tool, TextContent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize MCP server
app = Server("{repo_name}_mcp")

@app.list_tools()
async def list_tools() -> List[Tool]:
    """List available tools."""
    return [
        {tools_code}
    ]

@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls."""
    try:
        if name == "get_info":
            query = arguments.get("query", "")
            result = f"Information about {repo_name}: {{query}}"
            return [TextContent(type="text", text=result)]
        
        elif name == "analyze_data":
            data = arguments.get("data", "")
            result = f"Analysis of data: {{data[:100]}}..."
            return [TextContent(type="text", text=result)]
        
        else:
            return [TextContent(type="text", text=f"Unknown tool: {{name}}")]
    
    except Exception as e:
        logger.error(f"Error in tool {{name}}: {{e}}")
        return [TextContent(type="text", text=f"Error: {{str(e)}}")]

async def main():
    """Run the MCP server."""
    from mcp.server.stdio import stdio_server
    
    logger.info("Starting {repo_name} MCP server...")
    async with stdio_server(app) as (read_stream, write_stream):
        await app.run(read_stream, write_stream, app.create_initialization_options())

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    def _generate_installation_instructions(self, context: Dict[str, Any]) -> str:
        """Generate installation instructions for the MCP server."""
        
        repo_name = context["repository"].get("name", "repository")
        
        return f"""## Installation Instructions

1. **Install Dependencies:**
   ```bash
   pip install mcp
   ```

2. **Save the Server Code:**
   Save the generated code as `{repo_name}_mcp_server.py`

3. **Make it Executable:**
   ```bash
   chmod +x {repo_name}_mcp_server.py
   ```

4. **Test the Server:**
   ```bash
   python {repo_name}_mcp_server.py
   ```

5. **Configure in Claude Desktop:**
   Add to your `claude_desktop_config.json`:
   ```json
   {{
     "mcpServers": {{
       "{repo_name}_mcp": {{
         "command": "python",
         "args": ["/path/to/{repo_name}_mcp_server.py"]
       }}
     }}
   }}
   ```
"""
    
    def _generate_usage_example(self, context: Dict[str, Any]) -> str:
        """Generate usage examples for the MCP server."""
        
        repo_name = context["repository"].get("name", "repository")
        
        return f"""# Run the server
python {repo_name}_mcp_server.py

# The server will be available for MCP clients to connect to
# You can now use it in Claude Desktop or other MCP-compatible clients
"""
    
    def _create_downloadable_files(self, server_code: str, context: Dict[str, Any]) -> List[Dict[str, str]]:
        """Create downloadable files for the generated MCP server."""
        
        repo_name = context["repository"].get("name", "repository")
        
        files = [
            {
                "filename": f"{repo_name}_mcp_server.py",
                "content": server_code,
                "type": "python"
            },
            {
                "filename": "requirements.txt",
                "content": "mcp>=0.1.0\n",
                "type": "text"
            },
            {
                "filename": "README.md",
                "content": f"# {repo_name} MCP Server\n\nGenerated by SuperMCP\n\n{self._generate_installation_instructions(context)}",
                "type": "markdown"
            }
        ]
        
        return files

    def _generate_context_specific_tools(self, context: Dict[str, Any]) -> str:
        """Generate context-specific tools based on repository analysis."""

        repo_name = context["repository"].get("name", "repository")
        user_requirements = context.get("user_requirements", "")

        # Default tools
        tools = []

        # Browser-related tools if mentioned in requirements
        if "browser" in user_requirements.lower() or "suna" in repo_name.lower():
            tools.extend([
                '''Tool(
            name="navigate_page",
            description="Navigate to a specific URL",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "URL to navigate to"},
                    "wait_for": {"type": "string", "description": "Element to wait for (optional)"}
                },
                "required": ["url"]
            }
        )''',
                '''Tool(
            name="take_screenshot",
            description="Take a screenshot of the current page",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {"type": "string", "description": "CSS selector to screenshot (optional)"},
                    "full_page": {"type": "boolean", "description": "Take full page screenshot"}
                }
            }
        )''',
                '''Tool(
            name="extract_content",
            description="Extract content from the page",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {"type": "string", "description": "CSS selector to extract"},
                    "attribute": {"type": "string", "description": "Attribute to extract (optional)"}
                },
                "required": ["selector"]
            }
        )'''
            ])

        # API-related tools for general repositories
        tools.extend([
            '''Tool(
            name="get_repository_info",
            description="Get information about the repository",
            inputSchema={
                "type": "object",
                "properties": {
                    "info_type": {"type": "string", "description": "Type of information to retrieve"}
                },
                "required": ["info_type"]
            }
        )''',
            '''Tool(
            name="analyze_data",
            description="Analyze data from the repository",
            inputSchema={
                "type": "object",
                "properties": {
                    "data": {"type": "string", "description": "Data to analyze"},
                    "analysis_type": {"type": "string", "description": "Type of analysis to perform"}
                },
                "required": ["data"]
            }
        )'''
        ])

        return ',\n        '.join(tools)
