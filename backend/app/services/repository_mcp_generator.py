"""
Repository-Specific MCP Suggestions Generator
Creates contextual MCP tools based on actual repository functionality
"""

import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

from .intelligent_repository_analyzer import IntelligentRepositoryAnalyzer, RepositoryArchitecture
from .domain_specific_prompts import DomainSpecificPrompts
from .enhanced_mcp_marketplace import EnhancedMCPMarketplace, MCPServerInfo
from .intelligent_analysis_service import IntelligentAnalysisService
from .indexing_service import IndexingService

logger = logging.getLogger(__name__)

@dataclass
class MCPToolSuggestion:
    name: str
    description: str
    category: str
    implementation_approach: str
    parameters: List[Dict[str, Any]]
    return_type: str
    business_value: str
    use_cases: List[str]
    integration_points: List[str]
    code_examples: List[str]
    estimated_effort: str

@dataclass
class RepositoryMCPAnalysis:
    domain: str
    architecture: RepositoryArchitecture
    custom_mcp_suggestions: List[MCPToolSuggestion]
    existing_mcp_recommendations: List[MCPServerInfo]
    workflow_integrations: List[Dict[str, Any]]
    implementation_roadmap: List[Dict[str, Any]]
    total_estimated_effort: str

class RepositoryMCPGenerator:
    """Generate repository-specific MCP suggestions based on actual functionality"""
    
    def __init__(self):
        self.repo_analyzer = IntelligentRepositoryAnalyzer()
        self.prompt_service = DomainSpecificPrompts()
        self.marketplace = EnhancedMCPMarketplace()
        self.ai_service = IntelligentAnalysisService()
        self.indexing_service = IndexingService()
    
    async def generate_comprehensive_mcp_analysis(
        self, 
        analysis_id: int,
        user_goals: str,
        repository_context: Dict[str, Any]
    ) -> RepositoryMCPAnalysis:
        """Generate comprehensive MCP analysis for repository"""
        
        logger.info(f"Generating comprehensive MCP analysis for analysis {analysis_id}")
        
        # Step 1: Analyze repository architecture
        architecture = await self.repo_analyzer.analyze_repository_architecture(repository_context)
        
        # Step 2: Get indexed code summary for context
        indexed_summary = await self._get_indexed_code_summary(analysis_id)
        
        # Step 3: Generate custom MCP suggestions
        custom_mcps = await self._generate_custom_mcp_suggestions(
            architecture, user_goals, repository_context, indexed_summary
        )
        
        # Step 4: Find existing MCP recommendations
        existing_mcps = await self.marketplace.discover_domain_mcps(
            architecture.domain, user_goals, repository_context
        )
        
        # Step 5: Suggest workflow integrations
        workflow_integrations = await self.marketplace.suggest_workflow_integrations(
            architecture.domain, user_goals, existing_mcps
        )
        
        # Step 6: Create implementation roadmap
        roadmap = await self._create_implementation_roadmap(
            custom_mcps, existing_mcps, workflow_integrations
        )
        
        # Step 7: Calculate total effort
        total_effort = self._calculate_total_effort(custom_mcps, existing_mcps)
        
        return RepositoryMCPAnalysis(
            domain=architecture.domain,
            architecture=architecture,
            custom_mcp_suggestions=custom_mcps,
            existing_mcp_recommendations=existing_mcps[:8],  # Top 8
            workflow_integrations=workflow_integrations,
            implementation_roadmap=roadmap,
            total_estimated_effort=total_effort
        )
    
    async def _get_indexed_code_summary(self, analysis_id: int) -> Dict[str, Any]:
        """Get summary of indexed code for context"""
        
        try:
            # Get indexed code summary
            summary = await self.indexing_service.get_code_summary(analysis_id)
            return summary
        except Exception as e:
            logger.error(f"Failed to get indexed code summary: {str(e)}")
            return {
                'summary': 'No indexed code available',
                'main_functions': [],
                'api_endpoints': [],
                'business_logic': [],
                'capabilities': []
            }
    
    async def _generate_custom_mcp_suggestions(
        self,
        architecture: RepositoryArchitecture,
        user_goals: str,
        repository_context: Dict[str, Any],
        indexed_summary: Dict[str, Any]
    ) -> List[MCPToolSuggestion]:
        """Generate custom MCP tool suggestions based on repository analysis"""
        
        # Get domain-specific prompt
        prompt = self.prompt_service.get_mcp_suggestion_prompt(
            architecture.domain,
            repository_context,
            asdict(architecture),
            user_goals
        )
        
        # Add indexed code context
        enhanced_prompt = f"""{prompt}

INDEXED CODE ANALYSIS:
{json.dumps(indexed_summary, indent=2)}

Based on the actual repository code and capabilities, suggest 5-7 specific MCP tools that:

1. **Leverage Real Repository Functions**: Use actual functions, classes, and APIs found in the code
2. **Address User Goals**: Directly support the stated user objectives
3. **Follow Domain Best Practices**: Align with {architecture.domain} domain standards
4. **Provide Business Value**: Solve real problems and improve workflows

For each MCP tool, provide:
{{
  "name": "tool_name",
  "description": "What this tool does and why it's valuable",
  "category": "functional category",
  "implementation_approach": "How to implement using repository code",
  "parameters": [
    {{"name": "param1", "type": "string", "description": "Parameter description", "required": true}}
  ],
  "return_type": "What the tool returns",
  "business_value": "Why this tool is valuable",
  "use_cases": ["use_case1", "use_case2"],
  "integration_points": ["existing_api1", "existing_function2"],
  "code_examples": ["example implementation snippet"],
  "estimated_effort": "hours or days"
}}

Return as JSON array of tool objects. Focus on REAL implementations, not generic templates."""

        try:
            response = await self.ai_service._call_ai_service(enhanced_prompt, max_tokens=2000)
            
            # Parse MCP suggestions
            suggestions = self._parse_mcp_suggestions(response)
            return suggestions
            
        except Exception as e:
            logger.error(f"Failed to generate custom MCP suggestions: {str(e)}")
            return []
    
    def _parse_mcp_suggestions(self, response: str) -> List[MCPToolSuggestion]:
        """Parse MCP suggestions from AI response"""
        
        try:
            # Extract JSON from response
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                suggestions_data = json.loads(json_content)
            else:
                suggestions_data = json.loads(response)
            
            suggestions = []
            for suggestion_data in suggestions_data:
                suggestion = MCPToolSuggestion(
                    name=suggestion_data.get('name', ''),
                    description=suggestion_data.get('description', ''),
                    category=suggestion_data.get('category', ''),
                    implementation_approach=suggestion_data.get('implementation_approach', ''),
                    parameters=suggestion_data.get('parameters', []),
                    return_type=suggestion_data.get('return_type', ''),
                    business_value=suggestion_data.get('business_value', ''),
                    use_cases=suggestion_data.get('use_cases', []),
                    integration_points=suggestion_data.get('integration_points', []),
                    code_examples=suggestion_data.get('code_examples', []),
                    estimated_effort=suggestion_data.get('estimated_effort', 'Unknown')
                )
                suggestions.append(suggestion)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Failed to parse MCP suggestions: {str(e)}")
            return []
    
    async def _create_implementation_roadmap(
        self,
        custom_mcps: List[MCPToolSuggestion],
        existing_mcps: List[MCPServerInfo],
        workflow_integrations: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Create implementation roadmap for MCP integration"""
        
        roadmap_prompt = f"""Create an implementation roadmap for integrating these MCPs:

CUSTOM MCP TOOLS:
{self._format_custom_mcps_for_prompt(custom_mcps)}

EXISTING MCP SERVERS:
{self._format_existing_mcps_for_prompt(existing_mcps)}

WORKFLOW INTEGRATIONS:
{json.dumps(workflow_integrations, indent=2)}

Create a phased implementation roadmap with 3-4 phases:

{{
  "phase": 1,
  "name": "Foundation Phase",
  "duration": "1-2 weeks",
  "objectives": ["objective1", "objective2"],
  "deliverables": ["deliverable1", "deliverable2"],
  "mcps_to_implement": ["mcp1", "mcp2"],
  "dependencies": ["dependency1"],
  "success_criteria": ["criteria1", "criteria2"]
}}

Return as JSON array of phase objects. Prioritize high-value, low-effort items first."""

        try:
            response = await self.ai_service._call_ai_service(roadmap_prompt, max_tokens=1200)
            
            # Parse roadmap
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                roadmap = json.loads(json_content)
            else:
                roadmap = json.loads(response)
            
            return roadmap
            
        except Exception as e:
            logger.error(f"Failed to create implementation roadmap: {str(e)}")
            return []
    
    def _calculate_total_effort(
        self, 
        custom_mcps: List[MCPToolSuggestion], 
        existing_mcps: List[MCPServerInfo]
    ) -> str:
        """Calculate total implementation effort"""
        
        # Estimate effort for custom MCPs
        custom_effort_hours = 0
        for mcp in custom_mcps:
            effort_str = mcp.estimated_effort.lower()
            if 'hour' in effort_str:
                hours = int(''.join(filter(str.isdigit, effort_str)) or '8')
                custom_effort_hours += hours
            elif 'day' in effort_str:
                days = int(''.join(filter(str.isdigit, effort_str)) or '1')
                custom_effort_hours += days * 8
            else:
                custom_effort_hours += 16  # Default 2 days
        
        # Estimate effort for existing MCP integration
        existing_effort_hours = len(existing_mcps) * 4  # 4 hours per integration
        
        total_hours = custom_effort_hours + existing_effort_hours
        
        if total_hours < 40:
            return f"{total_hours} hours (1 week)"
        elif total_hours < 160:
            weeks = total_hours // 40
            return f"{total_hours} hours ({weeks} weeks)"
        else:
            months = total_hours // 160
            return f"{total_hours} hours ({months} months)"
    
    def _format_custom_mcps_for_prompt(self, mcps: List[MCPToolSuggestion]) -> str:
        """Format custom MCPs for prompt"""
        
        formatted = []
        for mcp in mcps:
            formatted.append(f"""
- {mcp.name}: {mcp.description}
  Category: {mcp.category}
  Effort: {mcp.estimated_effort}
  Value: {mcp.business_value}
""")
        
        return '\n'.join(formatted)
    
    def _format_existing_mcps_for_prompt(self, mcps: List[MCPServerInfo]) -> str:
        """Format existing MCPs for prompt"""
        
        formatted = []
        for mcp in mcps:
            formatted.append(f"""
- {mcp.name}: {mcp.description}
  Category: {mcp.category}
  Installation: {mcp.installation_method}
""")
        
        return '\n'.join(formatted)
