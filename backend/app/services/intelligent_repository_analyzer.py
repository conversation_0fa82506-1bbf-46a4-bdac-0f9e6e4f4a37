"""
Intelligent Repository Analyzer
Provides deep architectural understanding and domain classification for repositories
"""

import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import re

from .intelligent_analysis_service import IntelligentAnalysisService
from .indexing_service import IndexingService

logger = logging.getLogger(__name__)

@dataclass
class RepositoryArchitecture:
    domain: str  # 'cms', 'testing', 'ai_workflow', 'web_framework', etc.
    architecture_type: str  # 'microservices', 'monolith', 'library', 'framework'
    primary_purpose: str
    core_capabilities: List[str]
    technology_stack: List[str]
    integration_points: List[str]
    deployment_patterns: List[str]
    api_types: List[str]  # 'REST', 'GraphQL', 'gRPC', etc.

@dataclass
class DomainInsights:
    domain_category: str
    typical_workflows: List[str]
    common_integrations: List[str]
    standard_tools: List[str]
    pain_points: List[str]
    mcp_opportunities: List[str]

class IntelligentRepositoryAnalyzer:
    """Advanced repository analysis with architectural understanding"""
    
    def __init__(self):
        self.ai_service = IntelligentAnalysisService()
        self.indexing_service = IndexingService()
        
        # NO HARDCODED PATTERNS - Use dynamic classification instead
        self.universal_analyzer = None  # Will be initialized when needed
    
    async def analyze_repository_architecture(
        self, 
        analysis_data: Dict[str, Any]
    ) -> RepositoryArchitecture:
        """Analyze repository architecture and classify domain"""
        
        logger.info("Performing intelligent repository architecture analysis...")
        
        # Extract repository information
        repo_info = analysis_data.get('repository_info', {})
        comprehensive_analysis = analysis_data.get('comprehensive_analysis', {})
        
        # Build analysis context
        context = self._build_analysis_context(repo_info, comprehensive_analysis)
        
        # Classify domain
        domain = await self._classify_repository_domain(context)
        
        # Analyze architecture
        architecture = await self._analyze_architecture_patterns(context, domain)
        
        return architecture
    
    async def get_domain_insights(self, domain: str) -> DomainInsights:
        """Get domain-specific insights and MCP opportunities"""
        
        domain_knowledge = await self._get_domain_knowledge(domain)
        
        return DomainInsights(
            domain_category=domain,
            typical_workflows=domain_knowledge.get('workflows', []),
            common_integrations=domain_knowledge.get('integrations', []),
            standard_tools=domain_knowledge.get('tools', []),
            pain_points=domain_knowledge.get('pain_points', []),
            mcp_opportunities=domain_knowledge.get('mcp_opportunities', [])
        )
    
    def _build_analysis_context(
        self, 
        repo_info: Dict[str, Any], 
        comprehensive_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Build comprehensive context for analysis"""
        
        return {
            'name': repo_info.get('name', ''),
            'description': repo_info.get('description', ''),
            'language': repo_info.get('primary_language', ''),
            'technologies': comprehensive_analysis.get('technologies', []),
            'dependencies': comprehensive_analysis.get('dependencies', []),
            'file_structure': comprehensive_analysis.get('file_structure', {}),
            'api_endpoints': comprehensive_analysis.get('api_endpoints', []),
            'database_schemas': comprehensive_analysis.get('database_schemas', []),
            'configuration_files': comprehensive_analysis.get('configuration_files', []),
            'documentation': comprehensive_analysis.get('documentation', {}),
            'business_logic': comprehensive_analysis.get('business_logic', {}),
            'integrations': comprehensive_analysis.get('integrations', [])
        }
    
    async def _classify_repository_domain(self, context: Dict[str, Any]) -> str:
        """Classify repository domain using DYNAMIC analysis without hardcoded patterns"""

        # Dynamic domain classification based on repository content
        text_content = f"{context['name']} {context['description']} {' '.join(context['technologies'])}"

        # Extract meaningful keywords dynamically
        keywords = []
        words = text_content.lower().split()
        for word in words:
            # Filter out common words and keep meaningful terms
            if len(word) > 3 and word not in ['with', 'that', 'this', 'from', 'they', 'have', 'will', 'been', 'were']:
                keywords.append(word)
        
        # Use AI for dynamic domain classification
        ai_classification = await self._ai_classify_domain(context, keywords)

        return ai_classification or 'general'
    
    async def _ai_classify_domain(self, context: Dict[str, Any], keywords: List[str]) -> Optional[str]:
        """Use AI to classify repository domain dynamically without hardcoded assumptions"""

        prompt = f"""Analyze this repository and determine its primary purpose/domain:

Repository: {context['name']}
Description: {context['description']}
Language: {context['language']}
Technologies: {', '.join(context['technologies'])}
Dependencies: {', '.join(context['dependencies'][:10])}
Key Terms: {', '.join(keywords[:10])}

Based on the repository characteristics, determine the most appropriate domain category.
Consider what the repository is primarily used for and respond with a single descriptive term.
Examples: web_framework, testing, cms, database, ai_workflow, api_gateway, devops, data_processing, etc.

Respond with ONLY a single domain term (lowercase, underscore-separated if needed):
- database (Database/Data Storage)
- api_gateway (API Gateway/Proxy)
- devops (DevOps/Infrastructure)
- data_processing (Data Processing/Analytics)
- ecommerce (E-commerce Platform)
- social (Social/Communication Platform)
- general (General Purpose/Other)

Respond with ONLY the domain name (e.g., 'cms', 'testing', etc.)"""

        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=50)
            domain = response.strip().lower()
            
            # Return the AI-classified domain directly (no validation against hardcoded list)
            if domain and len(domain) > 0:
                return domain

            return 'general'
            
        except Exception as e:
            logger.error(f"Failed to classify domain with AI: {str(e)}")
            return None
    
    async def _analyze_architecture_patterns(
        self, 
        context: Dict[str, Any], 
        domain: str
    ) -> RepositoryArchitecture:
        """Analyze architecture patterns using AI"""
        
        prompt = f"""Analyze the architecture of this {domain} repository:

Repository: {context['name']}
Description: {context['description']}
Domain: {domain}
Technologies: {', '.join(context['technologies'])}
API Endpoints: {len(context['api_endpoints'])} endpoints found
File Structure: {json.dumps(context['file_structure'], indent=2)[:500]}

Provide a comprehensive architectural analysis in JSON format:
{{
  "architecture_type": "microservices|monolith|library|framework|plugin",
  "primary_purpose": "Brief description of main purpose",
  "core_capabilities": ["capability1", "capability2", "capability3"],
  "technology_stack": ["tech1", "tech2", "tech3"],
  "integration_points": ["integration1", "integration2"],
  "deployment_patterns": ["pattern1", "pattern2"],
  "api_types": ["REST", "GraphQL", "gRPC"]
}}

Focus on identifying the actual capabilities and integration points that could be exposed as MCP tools."""

        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=800)
            
            # Parse JSON response
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                architecture_data = json.loads(json_content)
            else:
                # Try to parse the entire response as JSON
                architecture_data = json.loads(response)
            
            return RepositoryArchitecture(
                domain=domain,
                architecture_type=architecture_data.get('architecture_type', 'unknown'),
                primary_purpose=architecture_data.get('primary_purpose', ''),
                core_capabilities=architecture_data.get('core_capabilities', []),
                technology_stack=architecture_data.get('technology_stack', context['technologies']),
                integration_points=architecture_data.get('integration_points', []),
                deployment_patterns=architecture_data.get('deployment_patterns', []),
                api_types=architecture_data.get('api_types', [])
            )
            
        except Exception as e:
            logger.error(f"Failed to analyze architecture: {str(e)}")
            
            # Fallback architecture analysis
            return RepositoryArchitecture(
                domain=domain,
                architecture_type='unknown',
                primary_purpose=context['description'],
                core_capabilities=[],
                technology_stack=context['technologies'],
                integration_points=[],
                deployment_patterns=[],
                api_types=[]
            )
    
    async def _get_domain_knowledge(self, domain: str) -> Dict[str, Any]:
        """Get domain-specific knowledge and insights"""
        
        domain_knowledge_base = {
            'cms': {
                'workflows': ['Content creation', 'Publishing', 'Media management', 'User management'],
                'integrations': ['CDN', 'Analytics', 'Email services', 'Payment gateways'],
                'tools': ['Content editors', 'Media processors', 'SEO tools', 'Backup systems'],
                'pain_points': ['Content versioning', 'Multi-language support', 'Performance optimization'],
                'mcp_opportunities': ['Content CRUD operations', 'Media management', 'Publishing workflows', 'SEO automation']
            },
            'testing': {
                'workflows': ['Test execution', 'Report generation', 'CI/CD integration', 'Cross-browser testing'],
                'integrations': ['CI/CD pipelines', 'Bug tracking', 'Performance monitoring', 'Code coverage'],
                'tools': ['Test runners', 'Assertion libraries', 'Mock services', 'Visual testing'],
                'pain_points': ['Flaky tests', 'Test maintenance', 'Parallel execution', 'Environment setup'],
                'mcp_opportunities': ['Test generation', 'Result analysis', 'Performance monitoring', 'Visual regression']
            },
            'ai_workflow': {
                'workflows': ['Agent orchestration', 'Task management', 'Memory management', 'Tool integration'],
                'integrations': ['LLM providers', 'Vector databases', 'APIs', 'Data sources'],
                'tools': ['Agent frameworks', 'Memory systems', 'Tool connectors', 'Workflow engines'],
                'pain_points': ['Agent coordination', 'Context management', 'Tool discovery', 'State persistence'],
                'mcp_opportunities': ['Multi-agent coordination', 'Memory management', 'Task orchestration', 'Tool integration']
            }
        }
        
        return domain_knowledge_base.get(domain, {
            'workflows': ['General operations'],
            'integrations': ['APIs', 'Databases'],
            'tools': ['Standard tools'],
            'pain_points': ['Integration complexity'],
            'mcp_opportunities': ['API exposure', 'Workflow automation']
        })
