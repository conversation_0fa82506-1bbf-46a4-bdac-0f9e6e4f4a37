"""
MCP Architect Service
Comprehensive repository analysis using the MCP Architect prompt approach
"""

import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .intelligent_analysis_service import IntelligentAnalysisService
from .mcp_marketplace_service import MCPMarketplaceService
from ..config import settings

logger = logging.getLogger(__name__)


@dataclass
class MCPCapability:
    name: str
    underlying_tech: str
    exposed_via_api: bool
    candidate_tool_name: str
    description: str
    parameters: List[Dict[str, Any]]


@dataclass
class ExistingMCPServer:
    name: str
    overlapping_tools: List[str]
    when_to_reuse: str
    url: str
    category: str


@dataclass
class MCPArchitectAnalysis:
    repository_summary: Dict[str, Any]
    capability_inventory: List[MCPCapability]
    new_mcp_server_specs: List[Dict[str, Any]]
    existing_mcp_servers: List[ExistingMCPServer]
    gap_analysis: List[str]
    implementation_starter: str
    client_config: Dict[str, Any]
    executive_summary: str


class MCPArchitectService:
    """
    Expert service for mapping GitHub repositories to MCP servers and workflows
    using the MCP Architect methodology
    """
    
    def __init__(self):
        self.ai_service = IntelligentAnalysisService()
        self.marketplace_service = MCPMarketplaceService()
    
    async def analyze_repository_comprehensive(
        self,
        repo_owner: str,
        repo_name: str,
        github_token: str,
        repo_info: Optional[Dict] = None
    ) -> MCPArchitectAnalysis:
        """
        Perform comprehensive MCP Architect analysis of a repository
        """
        logger.info(f"Starting MCP Architect analysis for {repo_owner}/{repo_name}")
        
        # Step 1: Get repository content and basic analysis
        repo_content = await self.ai_service._get_repository_content(
            repo_owner, repo_name, github_token, repo_info
        )
        
        # Step 2: Perform MCP Architect analysis
        architect_analysis = await self._perform_mcp_architect_analysis(repo_content)
        
        # Step 3: Discover existing MCP servers
        existing_mcps = await self._discover_existing_mcp_servers(architect_analysis)
        
        # Step 4: Generate gap analysis and implementation plan
        gap_analysis = await self._generate_gap_analysis(
            architect_analysis, existing_mcps
        )
        
        # Step 5: Create implementation starter code
        implementation_starter = await self._generate_implementation_starter(
            architect_analysis, gap_analysis
        )
        
        # Step 6: Generate client configuration
        client_config = await self._generate_client_config(
            architect_analysis, existing_mcps
        )
        
        return MCPArchitectAnalysis(
            repository_summary=architect_analysis.get('repository_summary', {}),
            capability_inventory=architect_analysis.get('capability_inventory', []),
            new_mcp_server_specs=architect_analysis.get('new_mcp_server_specs', []),
            existing_mcp_servers=existing_mcps,
            gap_analysis=gap_analysis,
            implementation_starter=implementation_starter,
            client_config=client_config,
            executive_summary=architect_analysis.get('executive_summary', '')
        )
    
    async def _perform_mcp_architect_analysis(
        self, repo_content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Perform the core MCP Architect analysis using the comprehensive prompt
        """
        
        repo_info = repo_content.get('repository_info', {})
        code_samples = repo_content.get('code_samples', {})
        
        # Build comprehensive analysis prompt
        prompt = f"""
## Role
You are "MCP Architect", an expert in mapping GitHub repositories to Model-Context-Protocol (MCP) servers and client workflows.

## Repository Analysis
Repository: {repo_info.get('name', 'Unknown')}
URL: {repo_info.get('html_url', 'Unknown')}
Description: {repo_info.get('description', 'No description')}
Language: {repo_info.get('language', 'Unknown')}
Stars: {repo_info.get('stargazers_count', 0)}

## Code Analysis
{json.dumps({k: v for k, v in list(code_samples.items())[:10]}, indent=2)}

## Analysis Steps

### 1. 📦 Capability Inventory
For each capability you find, analyze:
- Capability name
- Underlying technology
- Whether it's exposed via API/CLI
- Candidate MCP tool name
- Implementation feasibility

### 2. 🧩 Map to MCP Server Ideas
Turn capabilities into MCP tool specifications:
```yaml
- name: <tool_name>
  description: One-line purpose
  parameters:
    - name: <arg>
      type: string|int|bool
      required: true|false
```

### 3. 🚦 Gap Analysis
Identify capabilities not covered by existing MCP servers that justify new development.

### 4. 🛠️ Implementation Strategy
Prioritize tools by business value and implementation complexity.

## Output Format (JSON)
{{
  "executive_summary": "Brief 100-word summary of MCP opportunities",
  "repository_summary": {{
    "purpose": "Main repository purpose",
    "architecture": "Architecture type (microservices/monolith/library/etc)",
    "tech_stack": ["technology1", "technology2"],
    "use_cases": ["use_case1", "use_case2"]
  }},
  "capability_inventory": [
    {{
      "name": "capability_name",
      "underlying_tech": "technology",
      "exposed_via_api": true/false,
      "candidate_tool_name": "mcp_tool_name",
      "description": "What this capability does",
      "parameters": [
        {{"name": "param1", "type": "string", "required": true}},
        {{"name": "param2", "type": "int", "required": false}}
      ]
    }}
  ],
  "new_mcp_server_specs": [
    {{
      "name": "server_name",
      "description": "Server purpose",
      "tools": ["tool1", "tool2"],
      "implementation_effort": "low|medium|high",
      "business_value": "low|medium|high",
      "priority": 1-10
    }}
  ]
}}

Focus on practical, implementable MCP tools that leverage the repository's actual capabilities.
"""
        
        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=3000)
            
            # Parse JSON response
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                return json.loads(json_content)
            else:
                # Try to parse the entire response as JSON
                return json.loads(response)
                
        except Exception as e:
            logger.error(f"MCP Architect analysis failed: {str(e)}")
            return {
                "executive_summary": "Analysis failed - using fallback data",
                "repository_summary": {
                    "purpose": repo_info.get('description', 'Unknown purpose'),
                    "architecture": "unknown",
                    "tech_stack": [repo_info.get('language', 'Unknown')],
                    "use_cases": ["general_purpose"]
                },
                "capability_inventory": [],
                "new_mcp_server_specs": []
            }
    
    async def _discover_existing_mcp_servers(
        self, architect_analysis: Dict[str, Any]
    ) -> List[ExistingMCPServer]:
        """
        Discover existing MCP servers that could complement the repository
        """
        try:
            # Get capabilities from analysis
            capabilities = architect_analysis.get('capability_inventory', [])
            tech_stack = architect_analysis.get('repository_summary', {}).get('tech_stack', [])
            
            # Search for relevant MCP servers
            search_terms = []
            for cap in capabilities:
                search_terms.append(cap.get('underlying_tech', ''))
            search_terms.extend(tech_stack)
            
            # Use marketplace service to find relevant MCPs
            relevant_mcps = await self.marketplace_service.search_mcps_by_capabilities(
                search_terms[:5]  # Limit to top 5 terms
            )
            
            existing_servers = []
            for mcp in relevant_mcps[:10]:  # Limit to top 10 results
                existing_servers.append(ExistingMCPServer(
                    name=mcp.get('name', 'Unknown'),
                    overlapping_tools=mcp.get('capabilities', [])[:3],
                    when_to_reuse=f"When you need {', '.join(mcp.get('capabilities', [])[:2])}",
                    url=mcp.get('url', ''),
                    category=mcp.get('category', 'general')
                ))
            
            return existing_servers
            
        except Exception as e:
            logger.error(f"Failed to discover existing MCP servers: {str(e)}")
            return []
    
    async def _generate_gap_analysis(
        self, 
        architect_analysis: Dict[str, Any], 
        existing_mcps: List[ExistingMCPServer]
    ) -> List[str]:
        """
        Generate gap analysis highlighting capabilities not covered by existing servers
        """
        capabilities = architect_analysis.get('capability_inventory', [])
        existing_capabilities = set()
        
        for mcp in existing_mcps:
            existing_capabilities.update(mcp.overlapping_tools)
        
        gaps = []
        for cap in capabilities:
            cap_name = cap.get('name', '')
            if cap_name and cap_name not in existing_capabilities:
                gaps.append(f"{cap_name}: {cap.get('description', 'No description')}")
        
        return gaps[:5]  # Return top 5 gaps
    
    async def _generate_implementation_starter(
        self, 
        architect_analysis: Dict[str, Any], 
        gap_analysis: List[str]
    ) -> str:
        """
        Generate minimal implementation starter code
        """
        if not gap_analysis:
            return "# No implementation gaps identified"
        
        top_gap = gap_analysis[0].split(':')[0] if gap_analysis else "repository_tool"
        
        return f"""# MCP Server Starter for {top_gap}
from mcp.server import Server
from mcp.types import Tool, TextContent
import asyncio

app = Server("repository-mcp")

@app.call_tool()
async def {top_gap.lower().replace(' ', '_')}(arguments: dict) -> list[TextContent]:
    \"\"\"
    {gap_analysis[0].split(':', 1)[1] if ':' in gap_analysis[0] else 'Repository operation'}
    \"\"\"
    # TODO: Implement actual functionality
    return [TextContent(type="text", text="Tool implementation needed")]

if __name__ == "__main__":
    asyncio.run(app.run())
"""
    
    async def _generate_client_config(
        self, 
        architect_analysis: Dict[str, Any], 
        existing_mcps: List[ExistingMCPServer]
    ) -> Dict[str, Any]:
        """
        Generate client configuration snippet
        """
        config = {
            "mcpServers": {}
        }
        
        # Add custom server
        repo_name = architect_analysis.get('repository_summary', {}).get('purpose', 'repository')
        config["mcpServers"]["custom-repo-server"] = {
            "command": "python",
            "args": ["path/to/your/mcp_server.py"],
            "description": f"Custom MCP server for {repo_name}"
        }
        
        # Add recommended existing servers
        for mcp in existing_mcps[:3]:  # Top 3 recommendations
            server_key = mcp.name.lower().replace(' ', '-').replace('_', '-')
            config["mcpServers"][server_key] = {
                "command": "npx",
                "args": ["-y", f"@{mcp.name}"],
                "description": f"Marketplace MCP: {mcp.name}"
            }
        
        return config
