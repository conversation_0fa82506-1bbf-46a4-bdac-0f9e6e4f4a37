"""
Conversational MCP Assistant Service
Helps users understand what MCPs they can create based on their goals and repository analysis
"""

import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .analysis_cache import analysis_cache
from .intelligent_analysis_service import IntelligentAnalysisService
from .indexing_service import IndexingService
from .mcp_marketplace_service import MCPMarketplaceService
from .workflow_builder import WorkflowBuilder
from .real_mcp_generator import RealMCPGenerator
from .mcp_architect_service import MCPArchitectService

logger = logging.getLogger(__name__)

@dataclass
class MCPSuggestion:
    type: str  # 'existing_mcp', 'custom_mcp', 'workflow'
    title: str
    description: str
    implementation: Optional[str] = None
    marketplace_url: Optional[str] = None

@dataclass
class ChatResponse:
    response: str
    suggestions: List[MCPSuggestion]
    workflow_step: Optional[str] = None

class MCPAssistantService:
    """Conversational assistant for MCP planning and creation"""
    
    def __init__(self):
        self.ai_service = IntelligentAnalysisService()
        self.indexing_service = IndexingService()
        self.marketplace_service = MCPMarketplaceService()
        self.workflow_builder = WorkflowBuilder()
        self.mcp_generator = RealMCPGenerator()
        self.architect_service = MCPArchitectService()
    
    async def chat(
        self, 
        analysis_id: int, 
        user_message: str, 
        conversation_history: List[Dict[str, Any]]
    ) -> ChatResponse:
        """Handle conversational interaction about MCP creation"""
        
        logger.info(f"MCP Assistant chat for analysis {analysis_id}: {user_message[:100]}...")
        
        # Get repository analysis
        analysis_data = await self._get_analysis_data(analysis_id)
        if not analysis_data:
            return ChatResponse(
                response="I couldn't find the repository analysis. Please make sure the analysis is completed.",
                suggestions=[]
            )
        
        # Build context for AI (including indexed code capabilities)
        context = await self._build_conversation_context(analysis_data, conversation_history, user_message, analysis_id)
        
        # Check if this is a code-specific question
        if await self._is_code_question(user_message):
            # Use indexed code to answer
            code_answer = await self.indexing_service.answer_repository_question(analysis_id, user_message)
            ai_response = f"{code_answer}\n\nBased on this code analysis, here are some MCP suggestions:"
            # Still get AI suggestions
            ai_suggestions = await self._generate_mcp_conversation_response(context)
            ai_response += f"\n\n{ai_suggestions}"
        else:
            # Get AI response
            ai_response = await self._generate_mcp_conversation_response(context)
        
        # Parse response and extract suggestions
        response_text, suggestions = self._parse_ai_response(ai_response)

        # Enhance with marketplace suggestions if relevant
        marketplace_suggestions = await self._get_marketplace_suggestions(user_message, analysis_data)
        suggestions.extend(marketplace_suggestions)
        
        return ChatResponse(
            response=response_text,
            suggestions=suggestions
        )
    
    async def _get_analysis_data(self, analysis_id: int) -> Optional[Dict[str, Any]]:
        """Get repository analysis data from database"""
        try:
            # Import here to avoid circular imports
            from ..database import get_db
            from ..models import RepoAnalysis
            from sqlalchemy.orm import Session

            # Get database session
            db_gen = get_db()
            db: Session = next(db_gen)

            try:
                # Fetch analysis from database
                analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
                if not analysis:
                    logger.warning(f"Analysis {analysis_id} not found in database")
                    return None

                # Return analysis data
                return {
                    'id': analysis.id,
                    'repository_info': {
                        'name': analysis.repo_name,
                        'url': analysis.repo_url,
                        'owner': analysis.repo_owner,
                        'description': getattr(analysis, 'repo_description', ''),
                        'primary_language': getattr(analysis, 'primary_language', 'Unknown')
                    },
                    'comprehensive_analysis': analysis.analysis_results or {},
                    'status': analysis.status
                }
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Failed to get analysis data: {str(e)}")
            return None
    
    async def _build_conversation_context(
        self,
        analysis_data: Dict[str, Any],
        conversation_history: List[Dict[str, Any]],
        user_message: str,
        analysis_id: int
    ) -> Dict[str, Any]:
        """Build context for AI conversation"""
        
        repo_info = analysis_data.get('repository_info', {})
        comprehensive_analysis = analysis_data.get('comprehensive_analysis', {})
        
        # Extract key repository information
        repo_context = {
            'name': repo_info.get('name', 'Unknown'),
            'description': repo_info.get('description', ''),
            'language': repo_info.get('primary_language', 'Unknown'),
            'technologies': comprehensive_analysis.get('technologies', []),
            'business_logic': comprehensive_analysis.get('business_logic', {}),
            'api_endpoints': comprehensive_analysis.get('api_endpoints', []),
            'integrations': comprehensive_analysis.get('integrations', [])
        }

        # Enhance with indexed code capabilities
        try:
            capabilities_summary = await self.indexing_service.get_repository_capabilities_summary(analysis_id)
            repo_context['indexed_capabilities'] = capabilities_summary
        except Exception as e:
            logger.warning(f"Failed to get indexed capabilities: {str(e)}")
            repo_context['indexed_capabilities'] = {'summary': 'Code indexing not available'}
        
        # Build conversation context
        context = {
            'repository': repo_context,
            'conversation_history': conversation_history,
            'user_message': user_message,
            'task': 'mcp_assistant_conversation'
        }
        
        return context

    async def _generate_mcp_conversation_response(self, context: Dict[str, Any]) -> str:
        """Generate AI response for MCP conversation"""

        repo_context = context['repository']
        user_message = context['user_message']
        conversation_history = context.get('conversation_history', [])

        # Build conversation context
        history_text = ""
        if conversation_history:
            for msg in conversation_history[-5:]:  # Last 5 messages for context
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                history_text += f"{role.title()}: {content}\n\n"

        prompt = f"""You are "MCP Architect", an expert assistant helping users create Model Context Protocol (MCP) servers and workflows for their repositories.

## Repository Analysis
**Repository:** {repo_context['name']}
**Description:** {repo_context['description']}
**Primary Language:** {repo_context['language']}
**Technologies:** {', '.join(repo_context.get('technologies', []))}

## Code Capabilities
{repo_context.get('indexed_capabilities', {}).get('summary', 'No code analysis available')}

**Available Functions:** {', '.join(repo_context.get('indexed_capabilities', {}).get('main_functions', []))}
**API Endpoints:** {', '.join(repo_context.get('indexed_capabilities', {}).get('api_endpoints', []))}
**Business Logic:** {', '.join(repo_context.get('indexed_capabilities', {}).get('business_logic', []))}

## Conversation Context
{history_text}

## User Request
{user_message}

## Your Mission
As MCP Architect, provide comprehensive, actionable guidance using the MCP methodology:

1. **Capability Analysis** - Identify repository capabilities that can be exposed as MCP tools
2. **Architecture Mapping** - Map capabilities to specific MCP server designs
3. **Implementation Strategy** - Provide concrete implementation approaches
4. **Marketplace Integration** - Recommend existing MCPs that complement the repository

## Response Format
Provide a well-structured markdown response that includes:

- **Executive Summary** (2-3 sentences)
- **Capability Inventory** (bulleted list of key capabilities)
- **MCP Server Recommendations** (specific tools to build)
- **Existing MCP Integrations** (marketplace recommendations)
- **Implementation Roadmap** (prioritized next steps)

Use proper markdown formatting with headers, bullet points, and emphasis. Be specific and actionable.

If you have specific suggestions, format them as JSON at the end:

```json
{{
  "response": "Your conversational response here",
  "suggestions": [
    {{
      "type": "existing_mcp|custom_mcp|workflow",
      "title": "Suggestion title",
      "description": "Brief description",
      "implementation": "Optional implementation details",
      "marketplace_url": "Optional marketplace URL"
    }}
  ]
}}
```

Focus on practical, actionable advice based on their repository's actual capabilities."""

        return await self.ai_service._call_ai_service(prompt, max_tokens=2000)

    def _parse_ai_response(self, ai_response: str) -> tuple[str, List[MCPSuggestion]]:
        """Parse AI response to extract text and suggestions"""
        
        try:
            # Try to parse as JSON if it contains suggestions
            if '```json' in ai_response:
                # Extract JSON block
                json_start = ai_response.find('```json') + 7
                json_end = ai_response.find('```', json_start)
                json_content = ai_response[json_start:json_end].strip()
                
                parsed = json.loads(json_content)
                response_text = parsed.get('response', ai_response)
                suggestions_data = parsed.get('suggestions', [])
                
                suggestions = []
                for suggestion in suggestions_data:
                    suggestions.append(MCPSuggestion(
                        type=suggestion.get('type', 'workflow'),
                        title=suggestion.get('title', ''),
                        description=suggestion.get('description', ''),
                        implementation=suggestion.get('implementation'),
                        marketplace_url=suggestion.get('marketplace_url')
                    ))
                
                return response_text, suggestions
            
            # If no JSON, return the response as-is with default suggestions
            return ai_response, self._get_default_suggestions()
            
        except Exception as e:
            logger.error(f"Failed to parse AI response: {str(e)}")
            return ai_response, self._get_default_suggestions()

    async def _is_code_question(self, message: str) -> bool:
        """Determine if the user is asking about specific code functionality"""
        code_keywords = [
            'function', 'method', 'class', 'api', 'endpoint', 'how does', 'what does',
            'can this', 'does this have', 'is there a', 'show me', 'find', 'search',
            'code', 'implementation', 'feature', 'capability', 'functionality'
        ]

        message_lower = message.lower()
        return any(keyword in message_lower for keyword in code_keywords)

    async def _get_marketplace_suggestions(
        self,
        user_message: str,
        analysis_data: Dict[str, Any]
    ) -> List[MCPSuggestion]:
        """Get marketplace MCP suggestions based on user message"""

        try:
            # Extract repository context
            repo_info = analysis_data.get('repository_info', {})
            comprehensive_analysis = analysis_data.get('comprehensive_analysis', {})

            repository_context = {
                'name': repo_info.get('name', 'Unknown'),
                'language': repo_info.get('primary_language', 'Unknown'),
                'technologies': comprehensive_analysis.get('technologies', [])
            }

            # Search for compatible MCPs
            marketplace_mcps = await self.marketplace_service.search_compatible_mcps(
                user_goals=user_message,
                repository_context=repository_context
            )

            # Convert to MCPSuggestion format
            suggestions = []
            for mcp in marketplace_mcps[:3]:  # Top 3 marketplace suggestions
                suggestions.append(MCPSuggestion(
                    type='existing_mcp',
                    title=f"Use {mcp.name}",
                    description=f"{mcp.description} (Compatibility: {mcp.compatibility_score:.1%})",
                    marketplace_url=mcp.url,
                    implementation=f"Install: {mcp.installation_method}"
                ))

            return suggestions

        except Exception as e:
            logger.error(f"Failed to get marketplace suggestions: {str(e)}")
            return []

    async def start_workflow(
        self,
        analysis_id: int,
        user_goals: str,
        repository_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Start a new MCP workflow"""

        try:
            workflow = await self.workflow_builder.start_workflow(
                user_goals=user_goals,
                repository_context=repository_context,
                analysis_id=analysis_id
            )

            return {
                'workflow_id': workflow.id,
                'workflow': {
                    'name': workflow.name,
                    'description': workflow.description,
                    'steps': [
                        {
                            'id': step.id,
                            'type': step.type,
                            'title': step.title,
                            'description': step.description,
                            'status': step.status
                        }
                        for step in workflow.steps
                    ],
                    'status': workflow.status
                }
            }

        except Exception as e:
            logger.error(f"Failed to start workflow: {str(e)}")
            raise

    async def refine_workflow(
        self,
        workflow_id: str,
        user_feedback: str
    ) -> Dict[str, Any]:
        """Refine existing workflow based on user feedback"""

        try:
            workflow = await self.workflow_builder.refine_workflow(workflow_id, user_feedback)

            return {
                'workflow_id': workflow.id,
                'workflow': {
                    'name': workflow.name,
                    'description': workflow.description,
                    'steps': [
                        {
                            'id': step.id,
                            'type': step.type,
                            'title': step.title,
                            'description': step.description,
                            'status': step.status
                        }
                        for step in workflow.steps
                    ],
                    'status': workflow.status
                }
            }

        except Exception as e:
            logger.error(f"Failed to refine workflow: {str(e)}")
            raise

    async def finalize_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Finalize workflow for MCP generation"""

        try:
            result = await self.workflow_builder.finalize_workflow(workflow_id)
            return result

        except Exception as e:
            logger.error(f"Failed to finalize workflow: {str(e)}")
            raise

    async def generate_mcp_from_workflow(
        self,
        workflow_id: str,
        analysis_id: int,
        target_language: str = 'python'
    ) -> Dict[str, Any]:
        """Generate MCP server from finalized workflow"""

        try:
            # Get workflow
            workflow = self.workflow_builder.get_workflow(workflow_id)
            if not workflow:
                raise ValueError(f"Workflow {workflow_id} not found")

            if workflow.status != 'finalized':
                raise ValueError("Workflow must be finalized before generation")

            # Convert workflow steps to MCP tools
            selected_tools = await self._convert_workflow_to_tools(workflow)

            # Get repository information
            analysis_data = await self._get_analysis_data(analysis_id)
            if not analysis_data:
                raise ValueError(f"Analysis {analysis_id} not found")

            repo_info = analysis_data.get('repository_info', {})

            # Generate MCP server using the real generator
            result = await self.mcp_generator.generate_mcp_server(
                analysis_id=analysis_id,
                repo_url=repo_info.get('url', ''),
                repo_owner=repo_info.get('owner', ''),
                repo_name=repo_info.get('name', ''),
                selected_tools=selected_tools,
                target_language=target_language,
                github_token=None,  # Use public access
                server_name=f"{repo_info.get('name', 'custom')}-workflow-mcp"
            )

            return {
                'success': True,
                'workflow_id': workflow_id,
                'generation_result': result,
                'message': f"Successfully generated MCP server from workflow with {len(selected_tools)} tools"
            }

        except Exception as e:
            logger.error(f"Failed to generate MCP from workflow: {str(e)}")
            raise

    async def _convert_workflow_to_tools(self, workflow) -> List[Dict[str, Any]]:
        """Convert workflow steps to MCP tools format"""

        tools = []

        for step in workflow.steps:
            if step.type == 'custom_mcp':
                # Convert custom MCP step to tool format
                tool = {
                    'tool_name': step.title.lower().replace(' ', '_'),
                    'description': step.description,
                    'category': 'Business Logic',
                    'source_functions': [step.title.lower().replace(' ', '_')],
                    'source_files': ['main.py'],  # Default file
                    'implementation_hints': step.configuration.get('implementation', ''),
                    'workflow_step_id': step.id
                }
                tools.append(tool)

            elif step.type == 'integration':
                # Convert integration step to tool format
                tool = {
                    'tool_name': f"integrate_{step.title.lower().replace(' ', '_')}",
                    'description': f"Integration tool: {step.description}",
                    'category': 'API Integration',
                    'source_functions': ['integration_handler'],
                    'source_files': ['integrations.py'],
                    'implementation_hints': step.configuration.get('integration_details', ''),
                    'workflow_step_id': step.id
                }
                tools.append(tool)

        # Ensure we have at least one tool
        if not tools:
            tools.append({
                'tool_name': 'workflow_executor',
                'description': f"Execute workflow: {workflow.description}",
                'category': 'Business Logic',
                'source_functions': ['execute_workflow'],
                'source_files': ['workflow.py'],
                'implementation_hints': workflow.user_goals
            })

        return tools

    def _get_default_suggestions(self) -> List[MCPSuggestion]:
        """Get default suggestions when parsing fails"""
        return [
            MCPSuggestion(
                type='workflow',
                title='Tell me more about your goals',
                description='Describe what specific tasks you want to automate or integrate'
            ),
            MCPSuggestion(
                type='workflow',
                title='Explore existing MCPs',
                description='Let me show you MCPs from the marketplace that might help'
            ),
            MCPSuggestion(
                type='custom_mcp',
                title='Create custom MCP',
                description='Build a custom MCP based on your repository functions'
            )
        ]

class MCPWorkflowBuilder:
    """Helps build MCP workflows through conversation"""
    
    def __init__(self):
        self.ai_service = IntelligentAnalysisService()
    
    async def suggest_workflow(
        self, 
        user_goals: str, 
        repository_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Suggest MCP workflow based on user goals and repository"""
        
        context = {
            'user_goals': user_goals,
            'repository': repository_context,
            'task': 'mcp_workflow_suggestion'
        }
        
        workflow_suggestion = await self._generate_mcp_workflow(context)
        
        return {
            'workflow': workflow_suggestion,
            'next_steps': [
                'Review the suggested workflow',
                'Identify existing MCPs that can be used',
                'Plan custom MCPs that need to be created',
                'Generate the MCP server code'
            ]
        }

    async def _generate_mcp_workflow(self, context: Dict[str, Any]) -> str:
        """Generate MCP workflow suggestion using AI"""

        user_goals = context['user_goals']
        repo_context = context['repository']

        prompt = f"""Based on the user's goals and repository context, suggest a comprehensive MCP workflow.

User Goals: {user_goals}

Repository Context:
- Name: {repo_context['name']}
- Language: {repo_context['language']}
- Technologies: {', '.join(repo_context.get('technologies', []))}

Provide a detailed workflow that includes:
1. Existing MCPs that can be leveraged
2. Custom MCPs that need to be created
3. Integration points and data flow
4. Implementation steps

Format as a clear, actionable workflow."""

        return await self.ai_service._call_ai_service(prompt, max_tokens=1500)

    async def finalize_workflow(
        self, 
        workflow_steps: List[Dict[str, Any]], 
        repository_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Finalize the MCP workflow for generation"""
        
        # Analyze workflow steps to determine what needs to be built
        custom_mcps = []
        existing_mcps = []
        
        for step in workflow_steps:
            if step.get('type') == 'custom_mcp':
                custom_mcps.append(step)
            elif step.get('type') == 'existing_mcp':
                existing_mcps.append(step)
        
        return {
            'custom_mcps': custom_mcps,
            'existing_mcps': existing_mcps,
            'implementation_plan': {
                'total_mcps': len(custom_mcps),
                'estimated_effort': f"{len(custom_mcps) * 2} hours",
                'complexity': 'medium' if len(custom_mcps) > 2 else 'low'
            }
        }
