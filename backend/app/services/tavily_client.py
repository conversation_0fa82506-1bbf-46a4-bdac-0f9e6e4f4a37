"""
Tavily API Client

Integrates with Tavily search API to find MCP alternatives for detected integrations.
Uses web search to discover existing MCP servers that could replace heavyweight integrations.
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging
from ..config import settings

logger = logging.getLogger(__name__)


@dataclass
class TavilySearchResult:
    """Represents a search result from <PERSON>ly"""
    title: str
    url: str
    content: str
    score: float
    published_date: Optional[str] = None


@dataclass
class MCPDiscoveryResult:
    """Represents an MCP server discovered through search"""
    name: str
    description: str
    url: str
    github_url: Optional[str]
    integration_type: str
    service_compatibility: List[str]
    confidence_score: float
    benefits: List[str]
    installation_complexity: str


class TavilyClient:
    """Client for Tavily search API to discover MCP alternatives"""
    
    def __init__(self):
        self.api_key = settings.tavily_api_key
        self.base_url = "https://api.tavily.com"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def search(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """Generic search method for marketplace integration"""

        try:
            if not self.api_key:
                logger.warning("Tavily API key not configured, returning empty results")
                return []

            if not self.session:
                self.session = aiohttp.ClientSession()

            search_data = {
                "api_key": self.api_key,
                "query": query,
                "search_depth": "basic",
                "include_answer": False,
                "include_images": False,
                "include_raw_content": False,
                "max_results": max_results
            }

            async with self.session.post(f"{self.base_url}/search", json=search_data) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []

                    for result in data.get("results", []):
                        results.append({
                            "title": result.get("title", ""),
                            "url": result.get("url", ""),
                            "content": result.get("content", ""),
                            "score": result.get("score", 0.0)
                        })

                    return results
                else:
                    logger.error(f"Tavily search failed with status {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Tavily search error: {str(e)}")
            return []
    
    async def search_mcp_alternatives(self, integration_type: str, service_name: str) -> List[MCPDiscoveryResult]:
        """
        Search for MCP alternatives to a specific integration
        
        Args:
            integration_type: Type of integration (payment, email, sms, etc.)
            service_name: Name of the service (stripe, sendgrid, twilio, etc.)
            
        Returns:
            List of discovered MCP alternatives
        """
        if not self.api_key:
            logger.warning("Tavily API key not configured, skipping MCP discovery")
            return []
        
        logger.info(f"Searching for MCP alternatives to {service_name} ({integration_type})")
        
        # Generate search queries
        queries = self._generate_search_queries(integration_type, service_name)
        
        all_results = []
        for query in queries:
            try:
                search_results = await self._search(query)
                mcp_results = self._parse_mcp_results(search_results, integration_type, service_name)
                all_results.extend(mcp_results)
                
                # Add delay to respect rate limits
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error searching for query '{query}': {str(e)}")
                continue
        
        # Deduplicate and rank results
        unique_results = self._deduplicate_results(all_results)
        ranked_results = self._rank_results(unique_results, service_name)
        
        logger.info(f"Found {len(ranked_results)} MCP alternatives for {service_name}")
        return ranked_results[:5]  # Return top 5 results
    
    def _generate_search_queries(self, integration_type: str, service_name: str) -> List[str]:
        """Generate search queries to find MCP alternatives"""
        queries = [
            f"{service_name} MCP server Model Context Protocol",
            f"{integration_type} MCP server alternative to {service_name}",
            f"Model Context Protocol {integration_type} {service_name}",
            f"MCP {service_name} replacement lightweight",
            f"{service_name} MCP tool AI assistant integration"
        ]
        
        # Add generic queries for the integration type
        queries.extend([
            f"{integration_type} MCP server Model Context Protocol",
            f"MCP {integration_type} tools AI assistant",
            f"Model Context Protocol {integration_type} integration"
        ])
        
        return queries
    
    async def _search(self, query: str, max_results: int = 10) -> List[TavilySearchResult]:
        """Perform a search using Tavily API"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        payload = {
            "api_key": self.api_key,
            "query": query,
            "search_depth": "advanced",
            "include_answer": False,
            "include_images": False,
            "include_raw_content": True,
            "max_results": max_results,
            "include_domains": [
                "github.com",
                "docs.anthropic.com", 
                "modelcontextprotocol.io",
                "mcp.so",
                "npmjs.com",
                "pypi.org"
            ]
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/search",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_search_response(data)
                else:
                    logger.error(f"Tavily API error: {response.status} - {await response.text()}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error calling Tavily API: {str(e)}")
            return []
    
    def _parse_search_response(self, data: Dict[str, Any]) -> List[TavilySearchResult]:
        """Parse Tavily API response into search results"""
        results = []
        
        for item in data.get("results", []):
            result = TavilySearchResult(
                title=item.get("title", ""),
                url=item.get("url", ""),
                content=item.get("content", ""),
                score=item.get("score", 0.0),
                published_date=item.get("published_date")
            )
            results.append(result)
        
        return results
    
    def _parse_mcp_results(self, search_results: List[TavilySearchResult], 
                          integration_type: str, service_name: str) -> List[MCPDiscoveryResult]:
        """Parse search results to extract MCP server information"""
        mcp_results = []
        
        for result in search_results:
            # Check if this looks like an MCP server
            if self._is_mcp_related(result):
                mcp_result = self._extract_mcp_info(result, integration_type, service_name)
                if mcp_result:
                    mcp_results.append(mcp_result)
        
        return mcp_results
    
    def _is_mcp_related(self, result: TavilySearchResult) -> bool:
        """Check if a search result is related to MCP servers"""
        mcp_indicators = [
            "mcp", "model context protocol", "mcp server", "mcp tool",
            "anthropic mcp", "claude mcp", "ai assistant tool"
        ]
        
        text = f"{result.title} {result.content}".lower()
        return any(indicator in text for indicator in mcp_indicators)
    
    def _extract_mcp_info(self, result: TavilySearchResult, 
                         integration_type: str, service_name: str) -> Optional[MCPDiscoveryResult]:
        """Extract MCP server information from search result"""
        try:
            # Extract GitHub URL if available
            github_url = None
            if "github.com" in result.url:
                github_url = result.url
            
            # Determine service compatibility
            compatibility = self._determine_compatibility(result, service_name)
            
            # Calculate confidence score
            confidence = self._calculate_confidence(result, integration_type, service_name)
            
            # Extract benefits
            benefits = self._extract_benefits(result, integration_type)
            
            # Determine installation complexity
            complexity = self._determine_complexity(result)
            
            return MCPDiscoveryResult(
                name=self._extract_name(result),
                description=result.content[:200] + "..." if len(result.content) > 200 else result.content,
                url=result.url,
                github_url=github_url,
                integration_type=integration_type,
                service_compatibility=compatibility,
                confidence_score=confidence,
                benefits=benefits,
                installation_complexity=complexity
            )
            
        except Exception as e:
            logger.error(f"Error extracting MCP info: {str(e)}")
            return None
    
    def _extract_name(self, result: TavilySearchResult) -> str:
        """Extract MCP server name from result"""
        title = result.title
        
        # Clean up common patterns
        title = title.replace("GitHub - ", "")
        title = title.split(":")[0]  # Take part before colon
        title = title.split("|")[0]  # Take part before pipe
        
        return title.strip()
    
    def _determine_compatibility(self, result: TavilySearchResult, service_name: str) -> List[str]:
        """Determine which services this MCP is compatible with"""
        content = result.content.lower()
        title = result.title.lower()
        text = f"{title} {content}"
        
        compatibility = []
        
        # Check for specific service mentions
        services = ["stripe", "paypal", "sendgrid", "mailgun", "twilio", "aws", "google", "azure"]
        for service in services:
            if service in text:
                compatibility.append(service)
        
        # If no specific services found, add the target service
        if not compatibility and service_name:
            compatibility.append(service_name)
        
        return compatibility
    
    def _calculate_confidence(self, result: TavilySearchResult, 
                            integration_type: str, service_name: str) -> float:
        """Calculate confidence score for MCP match"""
        score = result.score
        
        # Boost score for GitHub repositories
        if "github.com" in result.url:
            score += 0.2
        
        # Boost score for official MCP documentation
        if any(domain in result.url for domain in ["anthropic.com", "modelcontextprotocol.io"]):
            score += 0.3
        
        # Boost score for service name match
        if service_name.lower() in result.content.lower():
            score += 0.2
        
        # Boost score for integration type match
        if integration_type.lower() in result.content.lower():
            score += 0.1
        
        return min(1.0, score)
    
    def _extract_benefits(self, result: TavilySearchResult, integration_type: str) -> List[str]:
        """Extract potential benefits of using this MCP"""
        benefits = []
        
        content = result.content.lower()
        
        # Common MCP benefits
        if "lightweight" in content:
            benefits.append("Lightweight integration")
        if "standardized" in content:
            benefits.append("Standardized AI interface")
        if "secure" in content:
            benefits.append("Enhanced security")
        if "simple" in content or "easy" in content:
            benefits.append("Simplified implementation")
        
        # Integration-specific benefits
        if integration_type == "payment":
            benefits.extend(["Reduced PCI compliance scope", "Simplified payment flows"])
        elif integration_type == "email":
            benefits.extend(["Better deliverability tracking", "Unified email interface"])
        elif integration_type == "sms":
            benefits.extend(["Multi-provider support", "Cost optimization"])
        
        return benefits[:4]  # Limit to 4 benefits
    
    def _determine_complexity(self, result: TavilySearchResult) -> str:
        """Determine installation complexity"""
        content = result.content.lower()
        
        if any(word in content for word in ["simple", "easy", "quick", "minimal"]):
            return "low"
        elif any(word in content for word in ["complex", "advanced", "detailed", "extensive"]):
            return "high"
        else:
            return "medium"
    
    def _deduplicate_results(self, results: List[MCPDiscoveryResult]) -> List[MCPDiscoveryResult]:
        """Remove duplicate MCP results"""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                unique_results.append(result)
        
        return unique_results
    
    def _rank_results(self, results: List[MCPDiscoveryResult], service_name: str) -> List[MCPDiscoveryResult]:
        """Rank MCP results by relevance"""
        def rank_key(result):
            score = result.confidence_score
            
            # Boost GitHub repositories
            if result.github_url:
                score += 0.1
            
            # Boost exact service matches
            if service_name.lower() in result.name.lower():
                score += 0.2
            
            # Boost based on number of benefits
            score += len(result.benefits) * 0.05
            
            return score
        
        return sorted(results, key=rank_key, reverse=True)
