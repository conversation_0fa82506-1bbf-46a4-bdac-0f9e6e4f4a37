"""
Unified Context Service
Provides comprehensive repository context across all MCP workflow stages
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from .indexing_service import IndexingService
from .mcp_analysis_service import MCPAnalysisService
from ..models import RepoAnalysis
from ..database import get_db

logger = logging.getLogger(__name__)

@dataclass
class RepositoryCapability:
    """Represents a specific capability found in the repository"""
    name: str
    description: str
    category: str  # 'api_endpoint', 'function', 'class', 'module'
    file_path: str
    confidence_score: float
    implementation_details: Dict[str, Any]
    dependencies: List[str]
    use_cases: List[str]

@dataclass
class UnifiedRepositoryContext:
    """Complete repository context for MCP workflow"""
    # Basic repository info
    repo_info: Dict[str, Any]
    
    # Analysis results
    analysis_results: Dict[str, Any]
    tech_stack: Dict[str, Any]
    
    # Indexed code capabilities
    indexed_capabilities: List[RepositoryCapability]
    code_summary: str
    
    # MCP analysis
    mcp_suggestions: List[Dict[str, Any]]
    existing_mcp_recommendations: List[Dict[str, Any]]
    
    # Workflow state
    workflow_stage: str  # 'analysis', 'conversation', 'playground'
    conversation_context: List[Dict[str, Any]]
    validated_requirements: Optional[Dict[str, Any]]

class UnifiedContextService:
    """Service that provides unified repository context across all workflow stages"""
    
    def __init__(self):
        self.indexing_service = IndexingService()
        self.mcp_analysis_service = MCPAnalysisService()
    
    async def get_complete_repository_context(
        self, 
        analysis_id: int,
        include_conversation: bool = True,
        include_validation: bool = True
    ) -> UnifiedRepositoryContext:
        """Get complete repository context for any workflow stage"""
        
        logger.info(f"Building complete repository context for analysis {analysis_id}")
        
        # Get basic repository analysis
        repo_analysis = await self._get_repository_analysis(analysis_id)
        if not repo_analysis:
            raise Exception(f"Repository analysis {analysis_id} not found")
        
        # Get indexed code capabilities
        indexed_capabilities = await self._get_indexed_capabilities(analysis_id)
        code_summary = await self._get_code_summary(analysis_id)
        
        # Get MCP analysis results
        mcp_context = await self._get_mcp_analysis_context(analysis_id)
        
        # Get conversation context if requested
        conversation_context = []
        if include_conversation:
            conversation_context = await self._get_conversation_context(analysis_id)
        
        # Get validated requirements if available
        validated_requirements = None
        if include_validation:
            validated_requirements = await self._get_validated_requirements(analysis_id)
        
        # Determine current workflow stage
        workflow_stage = self._determine_workflow_stage(repo_analysis, conversation_context)
        
        # Extract repo info and add analysis_id
        repo_info = self._extract_repo_info(repo_analysis)
        repo_info['analysis_id'] = analysis_id

        return UnifiedRepositoryContext(
            repo_info=repo_info,
            analysis_results=repo_analysis.analysis_results or {},
            tech_stack=self._extract_tech_stack(repo_analysis),
            indexed_capabilities=indexed_capabilities,
            code_summary=code_summary,
            mcp_suggestions=mcp_context.get('suggestions', []),
            existing_mcp_recommendations=mcp_context.get('recommendations', []),
            workflow_stage=workflow_stage,
            conversation_context=conversation_context,
            validated_requirements=validated_requirements
        )
    
    async def validate_user_requirements_against_repository(
        self,
        analysis_id: int,
        user_requirements: str,
        context: Optional[UnifiedRepositoryContext] = None
    ) -> Dict[str, Any]:
        """Validate if user requirements can be fulfilled by repository capabilities"""
        
        if not context:
            context = await self.get_complete_repository_context(analysis_id)
        
        # Extract requirement keywords and intent
        requirement_analysis = await self._analyze_user_requirements(user_requirements)
        
        # Check against repository capabilities (indexed + MCP analysis)
        capability_matches = await self._match_requirements_to_capabilities(
            requirement_analysis, context.indexed_capabilities
        )

        # Check against MCP analysis capabilities
        mcp_capability_matches = await self._match_requirements_to_mcp_capabilities(
            requirement_analysis, context
        )

        # Combine capability matches
        all_capability_matches = capability_matches + mcp_capability_matches

        # Check against existing MCP suggestions
        mcp_alignment = await self._check_mcp_suggestion_alignment(
            requirement_analysis, context.mcp_suggestions
        )
        
        # Calculate feasibility score
        feasibility_score = self._calculate_feasibility_score(
            all_capability_matches, mcp_alignment, context
        )

        # Generate validation result
        validation_result = {
            'is_feasible': feasibility_score >= 0.6,
            'feasibility_score': feasibility_score,
            'requirement_analysis': requirement_analysis,
            'capability_matches': all_capability_matches,
            'mcp_capability_matches': mcp_capability_matches,
            'indexed_capability_matches': capability_matches,
            'mcp_alignment': mcp_alignment,
            'missing_capabilities': self._identify_missing_capabilities(
                requirement_analysis, all_capability_matches
            ),
            'suggestions': self._generate_feasibility_suggestions(
                requirement_analysis, all_capability_matches, context
            ),
            'repository_scope': self._check_repository_scope(requirement_analysis, context)
        }
        
        # Store validation result for later use
        await self._store_validation_result(analysis_id, validation_result)
        
        return validation_result
    
    async def get_contextual_mcp_suggestions(
        self,
        analysis_id: int,
        user_message: str,
        conversation_history: List[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Get MCP suggestions that are contextually relevant to user's message and repository"""
        
        context = await self.get_complete_repository_context(analysis_id)
        
        # Analyze user intent from message
        user_intent = await self._analyze_user_intent(user_message, conversation_history)
        
        # Filter MCP suggestions based on intent and repository capabilities
        relevant_suggestions = await self._filter_relevant_suggestions(
            context.mcp_suggestions, user_intent, context.indexed_capabilities
        )
        
        # Enhance suggestions with repository-specific details
        enhanced_suggestions = await self._enhance_suggestions_with_repository_context(
            relevant_suggestions, context
        )
        
        return enhanced_suggestions
    
    async def prepare_playground_context(
        self,
        analysis_id: int,
        validated_requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Prepare complete context for MCP playground generation"""
        
        context = await self.get_complete_repository_context(analysis_id)
        
        # Prepare generation context
        playground_context = {
            'analysis_id': analysis_id,
            'repository_context': {
                'name': context.repo_info.get('name'),
                'owner': context.repo_info.get('owner'),
                'url': context.repo_info.get('url'),
                'language': context.tech_stack.get('primary_language'),
                'description': context.repo_info.get('description'),
                'technologies': context.tech_stack.get('technologies', [])
            },
            'analysis_results': context.analysis_results,
            'indexed_capabilities': [cap.__dict__ for cap in context.indexed_capabilities],
            'validated_requirements': validated_requirements,
            'mcp_suggestions': context.mcp_suggestions,
            'conversation_context': context.conversation_context[-5:],  # Last 5 messages
            'generation_approach': 'requirements_driven'
        }
        
        return playground_context
    
    async def _get_repository_analysis(self, analysis_id: int) -> Optional[RepoAnalysis]:
        """Get repository analysis from database"""
        try:
            from sqlalchemy.orm import sessionmaker
            from ..database import engine

            # Create a new session
            SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
            db = SessionLocal()

            try:
                analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

                if analysis:
                    # Eagerly load the dependencies to avoid lazy loading issues
                    _ = analysis.dependencies  # This will load the dependencies

                    # Create a detached copy with all needed data
                    analysis_data = {
                        'id': analysis.id,
                        'repo_name': analysis.repo_name,
                        'repo_owner': analysis.repo_owner,
                        'repo_url': analysis.repo_url,
                        'status': analysis.status,
                        'analysis_results': analysis.analysis_results,
                        'dependencies': [dep.name for dep in analysis.dependencies] if analysis.dependencies else []
                    }

                    # Create a simple object to return
                    class AnalysisResult:
                        def __init__(self, data):
                            for key, value in data.items():
                                setattr(self, key, value)

                    return AnalysisResult(analysis_data)

                return None

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Failed to get repository analysis: {str(e)}")
            return None
    
    async def _get_indexed_capabilities(self, analysis_id: int) -> List[RepositoryCapability]:
        """Get indexed code capabilities"""
        try:
            # Get capabilities from indexing service
            capabilities_data = await self.indexing_service.get_repository_capabilities_summary(analysis_id)

            capabilities = []

            # Handle different capability formats
            raw_capabilities = capabilities_data.get('capabilities', [])

            for i, cap_data in enumerate(raw_capabilities):
                if isinstance(cap_data, str):
                    # Simple string capability
                    capability = RepositoryCapability(
                        name=f"capability_{i+1}",
                        description=cap_data,
                        category='general',
                        file_path='',
                        confidence_score=0.7,
                        implementation_details={},
                        dependencies=[],
                        use_cases=[cap_data]
                    )
                elif isinstance(cap_data, dict):
                    # Structured capability object
                    capability = RepositoryCapability(
                        name=cap_data.get('name', f'capability_{i+1}'),
                        description=cap_data.get('description', ''),
                        category=cap_data.get('category', 'function'),
                        file_path=cap_data.get('file_path', ''),
                        confidence_score=cap_data.get('confidence_score', 0.5),
                        implementation_details=cap_data.get('implementation_details', {}),
                        dependencies=cap_data.get('dependencies', []),
                        use_cases=cap_data.get('use_cases', [])
                    )
                else:
                    continue

                capabilities.append(capability)

            # Also extract functions, API endpoints, and business logic
            for func in capabilities_data.get('main_functions', []):
                if isinstance(func, dict):
                    capability = RepositoryCapability(
                        name=func.get('name', 'unknown_function'),
                        description=func.get('description', 'Repository function'),
                        category='function',
                        file_path=func.get('file_path', ''),
                        confidence_score=0.8,
                        implementation_details=func,
                        dependencies=func.get('dependencies', []),
                        use_cases=func.get('use_cases', [])
                    )
                    capabilities.append(capability)

            for endpoint in capabilities_data.get('api_endpoints', []):
                if isinstance(endpoint, dict):
                    capability = RepositoryCapability(
                        name=f"{endpoint.get('method', 'GET')}_{endpoint.get('path', '').replace('/', '_')}",
                        description=f"API endpoint: {endpoint.get('method', 'GET')} {endpoint.get('path', '')}",
                        category='api_endpoint',
                        file_path=endpoint.get('file_path', ''),
                        confidence_score=0.9,
                        implementation_details=endpoint,
                        dependencies=[],
                        use_cases=[f"API access via {endpoint.get('method', 'GET')} {endpoint.get('path', '')}"]
                    )
                    capabilities.append(capability)

            logger.info(f"Extracted {len(capabilities)} capabilities for analysis {analysis_id}")
            return capabilities

        except Exception as e:
            logger.warning(f"Failed to get indexed capabilities: {str(e)}")
            return []
    
    async def _get_code_summary(self, analysis_id: int) -> str:
        """Get code summary from indexing service"""
        try:
            summary = await self.indexing_service.get_repository_capabilities_summary(analysis_id)
            return summary.get('summary', 'No code summary available')
        except Exception as e:
            logger.warning(f"Failed to get code summary: {str(e)}")
            return 'Code summary not available'
    
    async def _get_mcp_analysis_context(self, analysis_id: int) -> Dict[str, Any]:
        """Get MCP analysis context from Redis cache"""
        try:
            # Import here to avoid circular imports
            from ..tasks.mcp_analysis_task import get_mcp_analysis_result

            # Get MCP analysis results from Redis
            mcp_analysis = get_mcp_analysis_result(analysis_id)

            if mcp_analysis:
                # Convert MCP analysis to context format
                suggestions = []

                # Add capability matrix as suggestions
                for cap in mcp_analysis.get('capability_matrix', []):
                    suggestions.append({
                        'type': 'capability',
                        'name': cap.get('capability', ''),
                        'description': f"Capability: {cap.get('capability', '')} using {cap.get('underlying_tech', '')}",
                        'tool_name': cap.get('candidate_tool_name', ''),
                        'underlying_tech': cap.get('underlying_tech', ''),
                        'exposed_via_api': cap.get('exposed_via_api', 'No')
                    })

                # Add MCP server specs as suggestions
                for spec in mcp_analysis.get('new_mcp_server_specs', []):
                    suggestions.append({
                        'type': 'mcp_server_spec',
                        'name': spec.get('name', ''),
                        'description': spec.get('description', ''),
                        'parameters': spec.get('parameters', [])
                    })

                return {
                    'suggestions': suggestions,
                    'recommendations': mcp_analysis.get('existing_mcp_servers', []),
                    'executive_summary': mcp_analysis.get('executive_summary', ''),
                    'capability_matrix': mcp_analysis.get('capability_matrix', []),
                    'new_mcp_server_specs': mcp_analysis.get('new_mcp_server_specs', []),
                    'gap_analysis': mcp_analysis.get('gap_analysis', ''),
                    'implementation_starter': mcp_analysis.get('implementation_starter', '')
                }
            else:
                logger.warning(f"No MCP analysis found for analysis_id {analysis_id}")
                return {
                    'suggestions': [],
                    'recommendations': [],
                    'executive_summary': 'MCP analysis not available',
                    'capability_matrix': [],
                    'new_mcp_server_specs': [],
                    'gap_analysis': 'No analysis available',
                    'implementation_starter': ''
                }
        except Exception as e:
            logger.error(f"Failed to get MCP analysis context: {str(e)}")
            return {
                'suggestions': [],
                'recommendations': [],
                'executive_summary': 'Error retrieving MCP analysis',
                'capability_matrix': [],
                'new_mcp_server_specs': [],
                'gap_analysis': 'Error occurred during analysis retrieval',
                'implementation_starter': ''
            }
    
    async def _get_conversation_context(self, analysis_id: int) -> List[Dict[str, Any]]:
        """Get conversation context from database or cache"""
        # This would retrieve conversation history
        # For now, return empty list
        return []
    
    async def _get_validated_requirements(self, analysis_id: int) -> Optional[Dict[str, Any]]:
        """Get previously validated requirements"""
        # This would retrieve stored validation results
        # For now, return None
        return None
    
    def _determine_workflow_stage(
        self, 
        repo_analysis: RepoAnalysis, 
        conversation_context: List[Dict[str, Any]]
    ) -> str:
        """Determine current workflow stage"""
        if not repo_analysis.analysis_results:
            return 'analysis'
        elif not conversation_context:
            return 'conversation'
        else:
            return 'playground'
    
    def _extract_repo_info(self, repo_analysis) -> Dict[str, Any]:
        """Extract repository info from analysis"""
        analysis_results = getattr(repo_analysis, 'analysis_results', {}) or {}
        repo_info = analysis_results.get('repository_info', {})

        return {
            'name': getattr(repo_analysis, 'repo_name', 'Unknown'),
            'owner': getattr(repo_analysis, 'repo_owner', 'Unknown'),
            'url': getattr(repo_analysis, 'repo_url', ''),
            'description': repo_info.get('description', ''),
            'language': repo_info.get('language', 'Unknown'),
            'size': repo_info.get('size', 0),
            'stars': repo_info.get('stargazers_count', 0),
            'forks': repo_info.get('forks_count', 0)
        }
    
    def _extract_tech_stack(self, repo_analysis) -> Dict[str, Any]:
        """Extract tech stack information"""
        analysis_results = getattr(repo_analysis, 'analysis_results', {}) or {}
        repo_info = analysis_results.get('repository_info', {})
        dependencies = getattr(repo_analysis, 'dependencies', [])

        return {
            'primary_language': repo_info.get('language', 'Unknown'),
            'languages': repo_info.get('languages', {}),
            'language_percentages': repo_info.get('language_percentages', {}),
            'frameworks': analysis_results.get('frameworks', []),
            'technologies': analysis_results.get('technologies', []),
            'dependencies_count': len(dependencies) if dependencies else 0
        }

    async def _analyze_user_requirements(self, user_requirements: str) -> Dict[str, Any]:
        """Analyze user requirements to extract intent and scope"""

        # Simple keyword-based analysis for now
        # In production, this would use AI to parse requirements

        requirement_keywords = user_requirements.lower().split()

        # Categorize requirements
        categories = {
            'api_integration': ['api', 'endpoint', 'request', 'response', 'http', 'rest'],
            'data_processing': ['data', 'process', 'transform', 'parse', 'convert'],
            'workflow_automation': ['workflow', 'automate', 'task', 'job', 'schedule'],
            'file_management': ['file', 'upload', 'download', 'storage', 'document'],
            'database_operations': ['database', 'db', 'query', 'insert', 'update'],
            'monitoring': ['monitor', 'track', 'log', 'metric', 'health'],
            'testing': ['test', 'validate', 'verify', 'check']
        }

        detected_categories = []
        for category, keywords in categories.items():
            if any(keyword in requirement_keywords for keyword in keywords):
                detected_categories.append(category)

        # Determine scope
        scope_indicators = {
            'repository_specific': ['this repo', 'current code', 'existing functions', 'repository'],
            'external_integration': ['external', 'third party', 'api', 'service'],
            'generic_functionality': ['general', 'generic', 'common', 'standard']
        }

        detected_scope = 'repository_specific'  # Default
        for scope, indicators in scope_indicators.items():
            if any(indicator in user_requirements.lower() for indicator in indicators):
                detected_scope = scope
                break

        return {
            'original_text': user_requirements,
            'categories': detected_categories,
            'scope': detected_scope,
            'keywords': requirement_keywords,
            'complexity': 'medium'  # Simple heuristic
        }

    async def _match_requirements_to_capabilities(
        self,
        requirement_analysis: Dict[str, Any],
        capabilities: List[RepositoryCapability]
    ) -> List[Dict[str, Any]]:
        """Match user requirements to repository capabilities"""

        matches = []
        requirement_keywords = requirement_analysis['keywords']

        for capability in capabilities:
            # Calculate match score
            score = 0.0

            # Name matching
            cap_name_words = capability.name.lower().split('_')
            for keyword in requirement_keywords:
                if keyword in cap_name_words:
                    score += 0.3

            # Description matching
            cap_desc_words = capability.description.lower().split()
            for keyword in requirement_keywords:
                if keyword in cap_desc_words:
                    score += 0.2

            # Category matching
            if capability.category in requirement_analysis['categories']:
                score += 0.4

            # Use case matching
            for use_case in capability.use_cases:
                use_case_words = use_case.lower().split()
                for keyword in requirement_keywords:
                    if keyword in use_case_words:
                        score += 0.1

            if score > 0.3:  # Minimum threshold
                matches.append({
                    'capability': capability,
                    'match_score': min(score, 1.0),
                    'match_reasons': self._explain_capability_match(capability, requirement_keywords)
                })

        # Sort by match score
        matches.sort(key=lambda x: x['match_score'], reverse=True)
        return matches[:10]  # Top 10 matches

    async def _match_requirements_to_mcp_capabilities(
        self,
        requirement_analysis: Dict[str, Any],
        context: UnifiedRepositoryContext
    ) -> List[Dict[str, Any]]:
        """Match user requirements to MCP analysis capabilities"""

        matches = []
        requirement_keywords = requirement_analysis['keywords']

        # Get MCP analysis context
        mcp_context = context.__dict__.get('mcp_suggestions', [])

        # Check capability matrix from MCP analysis
        for suggestion in mcp_context:
            if suggestion.get('type') == 'capability':
                score = 0.0

                # Name matching
                cap_name = suggestion.get('name', '').lower()
                for keyword in requirement_keywords:
                    if keyword in cap_name:
                        score += 0.4

                # Description matching
                cap_desc = suggestion.get('description', '').lower()
                for keyword in requirement_keywords:
                    if keyword in cap_desc:
                        score += 0.3

                # Tool name matching
                tool_name = suggestion.get('tool_name', '').lower()
                for keyword in requirement_keywords:
                    if keyword in tool_name:
                        score += 0.3

                if score > 0.2:  # Lower threshold for MCP capabilities
                    # Create a RepositoryCapability-like object for consistency
                    mcp_capability = RepositoryCapability(
                        name=suggestion.get('name', ''),
                        description=suggestion.get('description', ''),
                        category='mcp_capability',
                        file_path='mcp_analysis',
                        confidence_score=score,
                        implementation_details={
                            'tool_name': suggestion.get('tool_name', ''),
                            'underlying_tech': suggestion.get('underlying_tech', ''),
                            'exposed_via_api': suggestion.get('exposed_via_api', 'No')
                        },
                        dependencies=[],
                        use_cases=[suggestion.get('name', '')]
                    )

                    matches.append({
                        'capability': mcp_capability,
                        'match_score': min(score, 1.0),
                        'match_reasons': [f"MCP capability: {suggestion.get('name', '')}"],
                        'source': 'mcp_analysis'
                    })

        # Check MCP server specs
        for suggestion in mcp_context:
            if suggestion.get('type') == 'mcp_server_spec':
                score = 0.0

                # Name matching
                spec_name = suggestion.get('name', '').lower()
                for keyword in requirement_keywords:
                    if keyword in spec_name:
                        score += 0.5

                # Description matching
                spec_desc = suggestion.get('description', '').lower()
                for keyword in requirement_keywords:
                    if keyword in spec_desc:
                        score += 0.4

                if score > 0.3:
                    # Create a RepositoryCapability-like object for MCP server spec
                    mcp_server_capability = RepositoryCapability(
                        name=suggestion.get('name', ''),
                        description=suggestion.get('description', ''),
                        category='mcp_server_spec',
                        file_path='mcp_analysis',
                        confidence_score=score,
                        implementation_details={
                            'parameters': suggestion.get('parameters', []),
                            'type': 'mcp_server_spec'
                        },
                        dependencies=[],
                        use_cases=[suggestion.get('name', '')]
                    )

                    matches.append({
                        'capability': mcp_server_capability,
                        'match_score': min(score, 1.0),
                        'match_reasons': [f"MCP server spec: {suggestion.get('name', '')}"],
                        'source': 'mcp_analysis'
                    })

        # Sort by match score
        matches.sort(key=lambda x: x['match_score'], reverse=True)
        return matches[:5]  # Top 5 MCP matches

    async def _check_mcp_suggestion_alignment(
        self,
        requirement_analysis: Dict[str, Any],
        mcp_suggestions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Check if requirements align with existing MCP suggestions"""

        aligned_suggestions = []
        requirement_keywords = requirement_analysis['keywords']

        for suggestion in mcp_suggestions:
            # Simple keyword matching for now
            suggestion_text = f"{suggestion.get('name', '')} {suggestion.get('description', '')}".lower()

            alignment_score = 0.0
            for keyword in requirement_keywords:
                if keyword in suggestion_text:
                    alignment_score += 0.2

            if alignment_score > 0.2:
                aligned_suggestions.append({
                    'suggestion': suggestion,
                    'alignment_score': min(alignment_score, 1.0)
                })

        return {
            'aligned_suggestions': aligned_suggestions,
            'alignment_ratio': len(aligned_suggestions) / len(mcp_suggestions) if mcp_suggestions else 0
        }

    def _calculate_feasibility_score(
        self,
        capability_matches: List[Dict[str, Any]],
        mcp_alignment: Dict[str, Any],
        context: UnifiedRepositoryContext
    ) -> float:
        """Calculate overall feasibility score"""

        score = 0.0

        # Capability matching score (40% weight)
        if capability_matches:
            avg_capability_score = sum(m['match_score'] for m in capability_matches) / len(capability_matches)
            score += avg_capability_score * 0.4

        # MCP alignment score (30% weight)
        alignment_ratio = mcp_alignment.get('alignment_ratio', 0)
        score += alignment_ratio * 0.3

        # Repository completeness score (20% weight)
        completeness_score = self._calculate_repository_completeness(context)
        score += completeness_score * 0.2

        # Tech stack compatibility (10% weight)
        tech_compatibility = self._calculate_tech_compatibility(context)
        score += tech_compatibility * 0.1

        return min(score, 1.0)

    def _identify_missing_capabilities(
        self,
        requirement_analysis: Dict[str, Any],
        capability_matches: List[Dict[str, Any]]
    ) -> List[str]:
        """Identify what capabilities are missing"""

        missing = []

        # Check if we have matches for each requirement category
        matched_categories = set()
        for match in capability_matches:
            if match['match_score'] > 0.5:
                matched_categories.add(match['capability'].category)

        required_categories = set(requirement_analysis['categories'])
        missing_categories = required_categories - matched_categories

        for category in missing_categories:
            missing.append(f"No strong matches found for {category} functionality")

        # Check for low overall match scores
        if capability_matches:
            avg_score = sum(m['match_score'] for m in capability_matches) / len(capability_matches)
            if avg_score < 0.5:
                missing.append("Low confidence in capability matches")
        else:
            missing.append("No matching capabilities found in repository")

        return missing

    def _generate_feasibility_suggestions(
        self,
        requirement_analysis: Dict[str, Any],
        capability_matches: List[Dict[str, Any]],
        context: UnifiedRepositoryContext
    ) -> List[str]:
        """Generate suggestions for improving feasibility"""

        suggestions = []

        # Suggest refinement if matches are weak
        if capability_matches:
            avg_score = sum(m['match_score'] for m in capability_matches) / len(capability_matches)
            if avg_score < 0.5:
                suggestions.append("Consider refining requirements to better match repository capabilities")

        # Suggest exploring existing functions
        if len(capability_matches) < 3:
            suggestions.append("Explore existing repository functions that might be adapted")

        # Suggest scope adjustment
        if requirement_analysis['scope'] != 'repository_specific':
            suggestions.append("Consider focusing on repository-specific functionality")

        # Suggest using existing MCP suggestions
        if context.mcp_suggestions:
            suggestions.append("Review existing MCP suggestions that align with your goals")

        return suggestions

    def _check_repository_scope(
        self,
        requirement_analysis: Dict[str, Any],
        context: UnifiedRepositoryContext
    ) -> Dict[str, Any]:
        """Check if requirements are within repository scope"""

        scope = requirement_analysis['scope']

        return {
            'is_in_scope': scope == 'repository_specific',
            'detected_scope': scope,
            'scope_confidence': 0.8,  # Simple heuristic
            'scope_explanation': self._explain_scope_decision(scope, requirement_analysis)
        }

    def _explain_scope_decision(self, scope: str, requirement_analysis: Dict[str, Any]) -> str:
        """Explain why a particular scope was detected"""

        if scope == 'repository_specific':
            return "Requirements appear to focus on existing repository functionality"
        elif scope == 'external_integration':
            return "Requirements involve external services or APIs"
        else:
            return "Requirements appear to be generic functionality"

    def _explain_capability_match(
        self,
        capability: RepositoryCapability,
        requirement_keywords: List[str]
    ) -> List[str]:
        """Explain why a capability matches requirements"""

        reasons = []

        # Check name matches
        cap_name_words = capability.name.lower().split('_')
        for keyword in requirement_keywords:
            if keyword in cap_name_words:
                reasons.append(f"Function name contains '{keyword}'")

        # Check description matches
        cap_desc_words = capability.description.lower().split()
        for keyword in requirement_keywords:
            if keyword in cap_desc_words:
                reasons.append(f"Description mentions '{keyword}'")

        if not reasons:
            reasons.append("General functionality alignment")

        return reasons[:3]  # Limit to 3 reasons

    def _calculate_repository_completeness(self, context: UnifiedRepositoryContext) -> float:
        """Calculate how complete the repository analysis is"""

        score = 0.0

        # Check if we have analysis results
        if context.analysis_results:
            score += 0.3

        # Check if we have indexed capabilities
        if context.indexed_capabilities:
            score += 0.3

        # Check if we have tech stack info
        if context.tech_stack.get('primary_language') != 'Unknown':
            score += 0.2

        # Check if we have MCP suggestions
        if context.mcp_suggestions:
            score += 0.2

        return score

    def _calculate_tech_compatibility(self, context: UnifiedRepositoryContext) -> float:
        """Calculate tech stack compatibility for MCP generation"""

        # Simple heuristic based on language support
        supported_languages = ['python', 'javascript', 'typescript', 'java', 'go', 'rust']
        primary_language = context.tech_stack.get('primary_language', '').lower()

        if primary_language in supported_languages:
            return 1.0
        elif primary_language and primary_language != 'unknown':
            return 0.7
        else:
            return 0.3

    async def _store_validation_result(self, analysis_id: int, validation_result: Dict[str, Any]):
        """Store validation result for later use"""
        # This would store the validation result in database or cache
        # For now, just log it
        logger.info(f"Validation result for analysis {analysis_id}: feasibility={validation_result['feasibility_score']:.2f}")

    async def _analyze_user_intent(
        self,
        user_message: str,
        conversation_history: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Analyze user intent from message and conversation history"""

        # Simple intent analysis
        message_lower = user_message.lower()

        intents = {
            'generate_mcp': ['generate', 'create', 'build', 'make mcp'],
            'explore_capabilities': ['what can', 'show me', 'list', 'capabilities'],
            'ask_question': ['how', 'what', 'why', 'when', 'where'],
            'get_suggestions': ['suggest', 'recommend', 'ideas', 'options'],
            'validate_idea': ['can i', 'is it possible', 'feasible', 'validate']
        }

        detected_intent = 'general'
        for intent, keywords in intents.items():
            if any(keyword in message_lower for keyword in keywords):
                detected_intent = intent
                break

        return {
            'primary_intent': detected_intent,
            'confidence': 0.8,
            'message_analysis': {
                'length': len(user_message),
                'question_words': sum(1 for word in ['how', 'what', 'why', 'when', 'where'] if word in message_lower),
                'action_words': sum(1 for word in ['generate', 'create', 'build', 'make'] if word in message_lower)
            }
        }

    async def _filter_relevant_suggestions(
        self,
        mcp_suggestions: List[Dict[str, Any]],
        user_intent: Dict[str, Any],
        capabilities: List[RepositoryCapability]
    ) -> List[Dict[str, Any]]:
        """Filter MCP suggestions based on user intent and repository capabilities"""

        # For now, return all suggestions
        # In production, this would filter based on intent and capability alignment
        return mcp_suggestions

    async def _enhance_suggestions_with_repository_context(
        self,
        suggestions: List[Dict[str, Any]],
        context: UnifiedRepositoryContext
    ) -> List[Dict[str, Any]]:
        """Enhance suggestions with repository-specific context"""

        enhanced = []
        for suggestion in suggestions:
            enhanced_suggestion = suggestion.copy()

            # Add repository context
            enhanced_suggestion['repository_context'] = {
                'language': context.tech_stack.get('primary_language'),
                'capabilities_count': len(context.indexed_capabilities),
                'has_analysis': bool(context.analysis_results)
            }

            # Add feasibility indicator
            enhanced_suggestion['feasibility'] = 'high'  # Simple heuristic

            enhanced.append(enhanced_suggestion)

        return enhanced
