"""
Real Repository Code Analyzer
Analyzes actual repository code to extract functions, classes, and business logic
"""

import os
import ast
import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import tempfile
import subprocess
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class PythonScopeVisitor(ast.NodeVisitor):
    """AST visitor that tracks function and class scope properly"""

    def __init__(self, file_path: str, lines: List[str]):
        self.file_path = file_path
        self.lines = lines
        self.scope_stack = []  # Track current scope depth
        self.top_level_functions = []
        self.classes = []
        self.current_class = None

    def visit_FunctionDef(self, node):
        self._visit_function(node, is_async=False)

    def visit_AsyncFunctionDef(self, node):
        self._visit_function(node, is_async=True)

    def _visit_function(self, node, is_async=False):
        """Visit a function definition"""
        # Check if this is a top-level function (not nested)
        if len(self.scope_stack) == 0:
            # Top-level function
            func_info = {
                'node': node,
                'is_async': is_async,
                'scope': 'top_level',
                'parent_class': self.current_class
            }
            self.top_level_functions.append(func_info)
        elif len(self.scope_stack) == 1 and self.current_class:
            # Method inside a class
            func_info = {
                'node': node,
                'is_async': is_async,
                'scope': 'method',
                'parent_class': self.current_class
            }
            # Add to current class methods
            if hasattr(self.current_class, 'methods'):
                self.current_class['methods'].append(func_info)
        # Ignore nested functions (scope > 1 or nested inside other functions)

        # Enter function scope
        self.scope_stack.append(('function', node.name))
        self.generic_visit(node)
        self.scope_stack.pop()

    def visit_ClassDef(self, node):
        """Visit a class definition"""
        if len(self.scope_stack) == 0:  # Only top-level classes
            class_info = {
                'node': node,
                'methods': [],
                'scope': 'top_level'
            }
            self.classes.append(class_info)

            # Set current class context
            old_class = self.current_class
            self.current_class = class_info

            # Enter class scope
            self.scope_stack.append(('class', node.name))
            self.generic_visit(node)
            self.scope_stack.pop()

            # Restore previous class context
            self.current_class = old_class

@dataclass
class ExtractedFunction:
    name: str
    signature: str
    docstring: Optional[str]
    body: str
    file_path: str
    line_number: int
    parameters: List[Dict[str, Any]]
    return_type: Optional[str]
    is_async: bool
    decorators: List[str]

@dataclass
class ExtractedClass:
    name: str
    docstring: Optional[str]
    methods: List[ExtractedFunction]
    file_path: str
    line_number: int
    base_classes: List[str]

@dataclass
class ExtractedAPI:
    endpoint: str
    method: str
    function_name: str
    file_path: str
    parameters: List[Dict[str, Any]]
    description: Optional[str]

class RealCodeAnalyzer:
    """Analyzes actual repository code to extract real functions and business logic"""
    
    def __init__(self):
        self.supported_languages = {
            'python': ['.py'],
            'javascript': ['.js', '.mjs'],
            'typescript': ['.ts'],
            'java': ['.java'],
            'go': ['.go'],
            'rust': ['.rs'],
            'php': ['.php'],
            'ruby': ['.rb']
        }
    
    async def analyze_repository(self, repo_path: str, repo_info: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze repository and extract real code structures"""
        
        logger.info(f"Starting real code analysis for repository: {repo_info.get('name', 'unknown')}")
        
        # Detect primary language
        primary_language = self._detect_primary_language(repo_path)
        
        # Extract code based on language
        extracted_code = {}
        
        if primary_language == 'python':
            extracted_code = await self._analyze_python_code(repo_path)
        elif primary_language in ['javascript', 'typescript']:
            extracted_code = await self._analyze_js_ts_code(repo_path)
        elif primary_language == 'java':
            extracted_code = await self._analyze_java_code(repo_path)
        elif primary_language == 'go':
            extracted_code = await self._analyze_go_code(repo_path)
        else:
            # Generic analysis for other languages
            extracted_code = await self._analyze_generic_code(repo_path, primary_language)
        
        # Extract API endpoints
        api_endpoints = await self._extract_api_endpoints(repo_path, primary_language)
        
        # Extract configuration and environment variables
        config_data = await self._extract_configuration(repo_path)
        
        # Analyze business logic patterns
        business_patterns = await self._analyze_business_patterns(extracted_code, api_endpoints)
        
        return {
            'primary_language': primary_language,
            'extracted_functions': extracted_code.get('functions', []),
            'extracted_classes': extracted_code.get('classes', []),
            'api_endpoints': api_endpoints,
            'configuration': config_data,
            'business_patterns': business_patterns,
            'file_structure': self._analyze_file_structure(repo_path),
            'dependencies': await self._extract_dependencies(repo_path, primary_language),
            'entry_points': await self._find_entry_points(repo_path, primary_language)
        }
    
    def _detect_primary_language(self, repo_path: str) -> str:
        """Detect the primary programming language of the repository"""
        
        language_counts = {}
        
        for root, dirs, files in os.walk(repo_path):
            # Skip common non-source directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'target', 'build', 'dist']]
            
            for file in files:
                file_path = Path(file)
                extension = file_path.suffix.lower()
                
                for language, extensions in self.supported_languages.items():
                    if extension in extensions:
                        language_counts[language] = language_counts.get(language, 0) + 1
                        break
        
        if not language_counts:
            return 'unknown'
        
        # Return the language with the most files
        primary_language = max(language_counts, key=language_counts.get)
        logger.info(f"Detected primary language: {primary_language} (counts: {language_counts})")
        
        return primary_language
    
    async def _analyze_python_code(self, repo_path: str) -> Dict[str, Any]:
        """Analyze Python code to extract functions and classes"""
        
        functions = []
        classes = []
        
        for root, dirs, files in os.walk(repo_path):
            # Skip common non-source directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv', 'env', '.venv']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, repo_path)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Parse Python AST
                        tree = ast.parse(content)
                        
                        # Extract functions and classes
                        file_functions, file_classes = self._extract_python_ast_elements(tree, relative_path, content)
                        functions.extend(file_functions)
                        classes.extend(file_classes)
                        
                    except Exception as e:
                        logger.warning(f"Failed to parse Python file {relative_path}: {str(e)}")
                        continue
        
        return {
            'functions': [self._function_to_dict(f) for f in functions],
            'classes': [self._class_to_dict(c) for c in classes]
        }
    
    def _extract_python_ast_elements(self, tree: ast.AST, file_path: str, content: str) -> Tuple[List[ExtractedFunction], List[ExtractedClass]]:
        """Extract functions and classes from Python AST with proper scope analysis"""

        functions = []
        classes = []
        lines = content.split('\n')

        # Use a visitor pattern to properly track scope
        visitor = PythonScopeVisitor(file_path, lines)
        visitor.visit(tree)

        # Get top-level functions only (not nested functions)
        for func_info in visitor.top_level_functions:
            func = self._extract_python_function_from_info(func_info, file_path, lines)
            if func:
                functions.append(func)

        # Get classes with their methods
        for class_info in visitor.classes:
            cls = self._extract_python_class_from_info(class_info, file_path, lines, content)
            if cls:
                classes.append(cls)

        return functions, classes

    def _extract_python_function_from_info(self, func_info: Dict, file_path: str, lines: List[str]) -> Optional[ExtractedFunction]:
        """Extract function details from visitor info"""
        node = func_info['node']
        is_async = func_info['is_async']
        scope = func_info['scope']

        return self._extract_python_function(node, file_path, lines, scope=scope, is_async_override=is_async)

    def _extract_python_class_from_info(self, class_info: Dict, file_path: str, lines: List[str], content: str) -> Optional[ExtractedClass]:
        """Extract class details from visitor info"""
        node = class_info['node']
        methods_info = class_info['methods']

        # Extract methods
        methods = []
        for method_info in methods_info:
            method = self._extract_python_function_from_info(method_info, file_path, lines)
            if method:
                methods.append(method)

        # Get class docstring
        docstring = ast.get_docstring(node)

        # Get base classes
        base_classes = [ast.unparse(base) for base in node.bases]

        return ExtractedClass(
            name=node.name,
            docstring=docstring,
            methods=methods,
            file_path=file_path,
            line_number=node.lineno,
            base_classes=base_classes
        )

    def _extract_python_function(self, node: ast.FunctionDef, file_path: str, lines: List[str], scope: str = 'unknown', is_async_override: Optional[bool] = None) -> Optional[ExtractedFunction]:
        """Extract details from a Python function node"""
        
        try:
            # Get function signature
            signature = f"def {node.name}("
            args = []
            
            for arg in node.args.args:
                arg_str = arg.arg
                if arg.annotation:
                    arg_str += f": {ast.unparse(arg.annotation)}"
                args.append(arg_str)
            
            signature += ", ".join(args) + ")"
            
            if node.returns:
                signature += f" -> {ast.unparse(node.returns)}"
            
            # Get docstring
            docstring = ast.get_docstring(node)
            
            # Get function body
            start_line = node.lineno - 1
            end_line = node.end_lineno if hasattr(node, 'end_lineno') else start_line + 10
            body = '\n'.join(lines[start_line:end_line])
            
            # Extract parameters
            parameters = []
            for arg in node.args.args:
                param = {
                    'name': arg.arg,
                    'type': ast.unparse(arg.annotation) if arg.annotation else None,
                    'required': True
                }
                parameters.append(param)
            
            # Get decorators
            decorators = [ast.unparse(decorator) for decorator in node.decorator_list]
            
            # Determine if function is async
            is_async = is_async_override if is_async_override is not None else isinstance(node, ast.AsyncFunctionDef)

            return ExtractedFunction(
                name=node.name,
                signature=signature,
                docstring=docstring,
                body=body,
                file_path=file_path,
                line_number=node.lineno,
                parameters=parameters,
                return_type=ast.unparse(node.returns) if node.returns else None,
                is_async=is_async,
                decorators=decorators
            )
            
        except Exception as e:
            logger.warning(f"Failed to extract function {node.name}: {str(e)}")
            return None
    
    def _extract_python_class(self, node: ast.ClassDef, file_path: str, lines: List[str], content: str) -> Optional[ExtractedClass]:
        """Extract details from a Python class node"""
        
        try:
            # Get class docstring
            docstring = ast.get_docstring(node)
            
            # Extract methods
            methods = []
            for item in node.body:
                if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    method = self._extract_python_function(item, file_path, lines)
                    if method:
                        methods.append(method)
            
            # Get base classes
            base_classes = [ast.unparse(base) for base in node.bases]
            
            return ExtractedClass(
                name=node.name,
                docstring=docstring,
                methods=methods,
                file_path=file_path,
                line_number=node.lineno,
                base_classes=base_classes
            )
            
        except Exception as e:
            logger.warning(f"Failed to extract class {node.name}: {str(e)}")
            return None
    
    def _function_to_dict(self, func: ExtractedFunction) -> Dict[str, Any]:
        """Convert ExtractedFunction to dictionary"""
        return {
            'name': func.name,
            'signature': func.signature,
            'docstring': func.docstring,
            'body': func.body,
            'file_path': func.file_path,
            'line_number': func.line_number,
            'parameters': func.parameters,
            'return_type': func.return_type,
            'is_async': func.is_async,
            'decorators': func.decorators
        }
    
    def _class_to_dict(self, cls: ExtractedClass) -> Dict[str, Any]:
        """Convert ExtractedClass to dictionary"""
        return {
            'name': cls.name,
            'docstring': cls.docstring,
            'methods': [self._function_to_dict(method) for method in cls.methods],
            'file_path': cls.file_path,
            'line_number': cls.line_number,
            'base_classes': cls.base_classes
        }

    async def _analyze_js_ts_code(self, repo_path: str) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript code to extract functions and classes"""

        functions = []
        classes = []

        for root, dirs, files in os.walk(repo_path):
            # Skip common non-source directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', 'dist', 'build']]

            for file in files:
                if file.endswith(('.js', '.ts', '.mjs')):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, repo_path)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        # Extract JavaScript/TypeScript functions and classes
                        file_functions, file_classes = self._extract_js_ts_elements(content, relative_path)
                        functions.extend(file_functions)
                        classes.extend(file_classes)

                    except Exception as e:
                        logger.warning(f"Failed to parse JS/TS file {relative_path}: {str(e)}")
                        continue

        return {
            'functions': functions,
            'classes': classes
        }

    def _extract_js_ts_elements(self, content: str, file_path: str) -> Tuple[List[Dict], List[Dict]]:
        """Extract functions and classes from JavaScript/TypeScript code using regex"""

        functions = []
        classes = []
        lines = content.split('\n')

        # Regex patterns for JavaScript/TypeScript
        function_patterns = [
            r'(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\([^)]*\)',
            r'(?:export\s+)?const\s+(\w+)\s*=\s*(?:async\s+)?\([^)]*\)\s*=>',
            r'(\w+)\s*:\s*(?:async\s+)?function\s*\([^)]*\)',
            r'(\w+)\s*\([^)]*\)\s*{',  # Method definitions
        ]

        class_pattern = r'(?:export\s+)?class\s+(\w+)(?:\s+extends\s+\w+)?'

        # Find functions
        for i, line in enumerate(lines):
            for pattern in function_patterns:
                matches = re.finditer(pattern, line)
                for match in matches:
                    func_name = match.group(1)

                    # Extract function body (simplified)
                    body_lines = []
                    brace_count = 0
                    start_found = False

                    for j in range(i, min(i + 50, len(lines))):  # Look ahead max 50 lines
                        current_line = lines[j]
                        body_lines.append(current_line)

                        for char in current_line:
                            if char == '{':
                                brace_count += 1
                                start_found = True
                            elif char == '}':
                                brace_count -= 1

                        if start_found and brace_count == 0:
                            break

                    functions.append({
                        'name': func_name,
                        'signature': line.strip(),
                        'body': '\n'.join(body_lines),
                        'file_path': file_path,
                        'line_number': i + 1,
                        'is_async': 'async' in line,
                        'is_export': 'export' in line
                    })

        # Find classes
        for i, line in enumerate(lines):
            match = re.search(class_pattern, line)
            if match:
                class_name = match.group(1)

                # Extract class body (simplified)
                body_lines = []
                brace_count = 0
                start_found = False

                for j in range(i, min(i + 200, len(lines))):  # Look ahead max 200 lines for classes
                    current_line = lines[j]
                    body_lines.append(current_line)

                    for char in current_line:
                        if char == '{':
                            brace_count += 1
                            start_found = True
                        elif char == '}':
                            brace_count -= 1

                    if start_found and brace_count == 0:
                        break

                classes.append({
                    'name': class_name,
                    'body': '\n'.join(body_lines),
                    'file_path': file_path,
                    'line_number': i + 1,
                    'is_export': 'export' in line
                })

        return functions, classes

    async def _analyze_generic_code(self, repo_path: str, language: str) -> Dict[str, Any]:
        """Generic code analysis for unsupported languages"""

        functions = []
        classes = []

        # Basic pattern matching for common function/class patterns
        patterns = {
            'java': {
                'function': r'(?:public|private|protected)?\s*(?:static\s+)?[\w<>\[\]]+\s+(\w+)\s*\([^)]*\)',
                'class': r'(?:public\s+)?class\s+(\w+)'
            },
            'go': {
                'function': r'func\s+(?:\(\w+\s+\*?\w+\)\s+)?(\w+)\s*\([^)]*\)',
                'class': r'type\s+(\w+)\s+struct'
            },
            'rust': {
                'function': r'(?:pub\s+)?fn\s+(\w+)\s*\([^)]*\)',
                'class': r'(?:pub\s+)?struct\s+(\w+)'
            }
        }

        if language not in patterns:
            return {'functions': [], 'classes': []}

        lang_patterns = patterns[language]
        extensions = self.supported_languages.get(language, [])

        for root, dirs, files in os.walk(repo_path):
            dirs[:] = [d for d in dirs if not d.startswith('.')]

            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, repo_path)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        lines = content.split('\n')

                        # Find functions
                        for i, line in enumerate(lines):
                            if 'function' in lang_patterns:
                                matches = re.finditer(lang_patterns['function'], line)
                                for match in matches:
                                    functions.append({
                                        'name': match.group(1),
                                        'signature': line.strip(),
                                        'file_path': relative_path,
                                        'line_number': i + 1,
                                        'language': language
                                    })

                        # Find classes/structs
                        for i, line in enumerate(lines):
                            if 'class' in lang_patterns:
                                matches = re.finditer(lang_patterns['class'], line)
                                for match in matches:
                                    classes.append({
                                        'name': match.group(1),
                                        'signature': line.strip(),
                                        'file_path': relative_path,
                                        'line_number': i + 1,
                                        'language': language
                                    })

                    except Exception as e:
                        logger.warning(f"Failed to parse {language} file {relative_path}: {str(e)}")
                        continue

        return {
            'functions': functions,
            'classes': classes
        }

    async def _extract_api_endpoints(self, repo_path: str, language: str) -> List[Dict[str, Any]]:
        """Extract API endpoints from the repository"""

        endpoints = []

        # Universal patterns that work across ALL languages and frameworks
        universal_patterns = [
            # HTTP method decorators and functions
            r'@\w*\.(get|post|put|delete|patch)\([\'"]([^\'"]+)[\'"]',
            r'\.(get|post|put|delete|patch)\([\'"]([^\'"]+)[\'"]',

            # Route definitions
            r'route\([\'"]([^\'"]+)[\'"]',
            r'path\([\'"]([^\'"]+)[\'"]',

            # HTTP method mappings
            r'@(Get|Post|Put|Delete|Patch)Mapping\([\'"]([^\'"]+)[\'"]',
            r'@RequestMapping\([^)]*value\s*=\s*[\'"]([^\'"]+)[\'"]',

            # Generic handler patterns
            r'HandleFunc\([\'"]([^\'"]+)[\'"]',
            r'handle\([\'"]([^\'"]+)[\'"]',

            # URL patterns with methods
            r'[\'"]([^\'"/]+(?:/[^\'"/]*)*)[\'"].*(?:GET|POST|PUT|DELETE|PATCH)',
            r'(?:GET|POST|PUT|DELETE|PATCH).*[\'"]([^\'"/]+(?:/[^\'"/]*)*)[\'"]'
        ]

        for root, dirs, files in os.walk(repo_path):
            dirs[:] = [d for d in dirs if not d.startswith('.')]

            for file in files:
                if any(file.endswith(ext) for ext in self.supported_languages.get(language, [])):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, repo_path)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        # Apply universal patterns to detect endpoints
                        for pattern in universal_patterns:
                            matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
                            for match in matches:
                                try:
                                    groups = match.groups()
                                    if len(groups) >= 2:
                                        # Extract method and path from match groups
                                        if groups[0].upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                                            method = groups[0].upper()
                                            path = groups[1]
                                        else:
                                            method = 'GET'  # Default method
                                            path = groups[0]

                                        # Validate path format
                                        if path and (path.startswith('/') or path.startswith('{') or '/' in path):
                                            endpoints.append({
                                                'path': path,
                                                'method': method,
                                                'file_path': relative_path,
                                                'framework': 'detected',  # Generic framework
                                                'language': language
                                            })
                                except (IndexError, AttributeError):
                                    continue

                    except Exception as e:
                        logger.warning(f"Failed to extract endpoints from {relative_path}: {str(e)}")
                        continue

        return endpoints

    async def _extract_configuration(self, repo_path: str) -> Dict[str, Any]:
        """Extract configuration files and environment variables"""

        config_files = {}
        env_vars = set()

        # Common config file patterns
        config_patterns = {
            'package.json': 'json',
            'requirements.txt': 'text',
            'Cargo.toml': 'toml',
            'go.mod': 'text',
            'pom.xml': 'xml',
            'build.gradle': 'gradle',
            'Dockerfile': 'dockerfile',
            'docker-compose.yml': 'yaml',
            'docker-compose.yaml': 'yaml',
            '.env': 'env',
            '.env.example': 'env',
            'config.py': 'python',
            'config.js': 'javascript',
            'settings.py': 'python'
        }

        for root, dirs, files in os.walk(repo_path):
            dirs[:] = [d for d in dirs if not d.startswith('.')]

            for file in files:
                if file in config_patterns or file.endswith(('.json', '.yml', '.yaml', '.toml', '.ini')):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, repo_path)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        config_files[relative_path] = {
                            'type': config_patterns.get(file, 'unknown'),
                            'content': content[:1000],  # Limit content size
                            'size': len(content)
                        }

                        # Extract environment variables
                        env_matches = re.findall(r'(?:process\.env\.|os\.environ\.|getenv\(|ENV\[)[\'"]?([A-Z_][A-Z0-9_]*)', content)
                        env_vars.update(env_matches)

                    except Exception as e:
                        logger.warning(f"Failed to read config file {relative_path}: {str(e)}")
                        continue

        return {
            'config_files': config_files,
            'environment_variables': list(env_vars)
        }

    async def _analyze_business_patterns(self, extracted_code: Dict[str, Any], api_endpoints: List[Dict]) -> Dict[str, Any]:
        """Analyze business logic patterns from extracted code"""

        patterns = {
            'crud_operations': [],
            'authentication': [],
            'data_processing': [],
            'external_integrations': [],
            'business_rules': []
        }

        functions = extracted_code.get('functions', [])

        # Analyze function names and bodies for business patterns
        for func in functions:
            func_name = func.get('name', '').lower()
            func_body = func.get('body', '').lower()

            # CRUD operations
            if any(keyword in func_name for keyword in ['create', 'insert', 'add', 'save', 'store']):
                patterns['crud_operations'].append({
                    'type': 'create',
                    'function': func['name'],
                    'file': func.get('file_path', ''),
                    'signature': func.get('signature', '')
                })
            elif any(keyword in func_name for keyword in ['get', 'find', 'fetch', 'retrieve', 'read', 'list']):
                patterns['crud_operations'].append({
                    'type': 'read',
                    'function': func['name'],
                    'file': func.get('file_path', ''),
                    'signature': func.get('signature', '')
                })
            elif any(keyword in func_name for keyword in ['update', 'modify', 'edit', 'change']):
                patterns['crud_operations'].append({
                    'type': 'update',
                    'function': func['name'],
                    'file': func.get('file_path', ''),
                    'signature': func.get('signature', '')
                })
            elif any(keyword in func_name for keyword in ['delete', 'remove', 'destroy']):
                patterns['crud_operations'].append({
                    'type': 'delete',
                    'function': func['name'],
                    'file': func.get('file_path', ''),
                    'signature': func.get('signature', '')
                })

            # Authentication patterns
            if any(keyword in func_name for keyword in ['auth', 'login', 'logout', 'token', 'verify', 'validate']):
                patterns['authentication'].append({
                    'function': func['name'],
                    'file': func.get('file_path', ''),
                    'type': 'authentication'
                })

            # Data processing patterns
            if any(keyword in func_name for keyword in ['process', 'transform', 'convert', 'parse', 'format']):
                patterns['data_processing'].append({
                    'function': func['name'],
                    'file': func.get('file_path', ''),
                    'type': 'data_processing'
                })

            # External integrations
            if any(keyword in func_body for keyword in ['requests.', 'fetch(', 'axios.', 'http', 'api']):
                patterns['external_integrations'].append({
                    'function': func['name'],
                    'file': func.get('file_path', ''),
                    'type': 'external_api'
                })

        return patterns

    def _analyze_file_structure(self, repo_path: str) -> Dict[str, Any]:
        """Analyze repository file structure"""

        structure = {
            'total_files': 0,
            'directories': [],
            'file_types': {},
            'important_files': []
        }

        important_patterns = [
            'README', 'LICENSE', 'CHANGELOG', 'CONTRIBUTING',
            'Dockerfile', 'docker-compose', 'Makefile',
            'package.json', 'requirements.txt', 'Cargo.toml', 'go.mod'
        ]

        for root, dirs, files in os.walk(repo_path):
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]

            relative_root = os.path.relpath(root, repo_path)
            if relative_root != '.':
                structure['directories'].append(relative_root)

            for file in files:
                structure['total_files'] += 1

                # Count file types
                ext = Path(file).suffix.lower()
                structure['file_types'][ext] = structure['file_types'].get(ext, 0) + 1

                # Check for important files
                if any(pattern.lower() in file.lower() for pattern in important_patterns):
                    file_path = os.path.join(relative_root, file) if relative_root != '.' else file
                    structure['important_files'].append(file_path)

        return structure

    async def _extract_dependencies(self, repo_path: str, language: str) -> List[Dict[str, Any]]:
        """Extract dependencies from package files"""

        dependencies = []

        dependency_files = {
            'python': ['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'],
            'javascript': ['package.json'],
            'typescript': ['package.json'],
            'java': ['pom.xml', 'build.gradle'],
            'go': ['go.mod'],
            'rust': ['Cargo.toml'],
            'php': ['composer.json'],
            'ruby': ['Gemfile']
        }

        files_to_check = dependency_files.get(language, [])

        for root, dirs, files in os.walk(repo_path):
            for file in files:
                if file in files_to_check:
                    file_path = os.path.join(root, file)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        if file == 'package.json':
                            # Parse package.json
                            data = json.loads(content)
                            deps = data.get('dependencies', {})
                            dev_deps = data.get('devDependencies', {})

                            for name, version in deps.items():
                                dependencies.append({
                                    'name': name,
                                    'version': version,
                                    'type': 'production',
                                    'file': file
                                })

                            for name, version in dev_deps.items():
                                dependencies.append({
                                    'name': name,
                                    'version': version,
                                    'type': 'development',
                                    'file': file
                                })

                        elif file == 'requirements.txt':
                            # Parse requirements.txt
                            lines = content.split('\n')
                            for line in lines:
                                line = line.strip()
                                if line and not line.startswith('#'):
                                    # Simple parsing - could be improved
                                    name = re.split(r'[>=<]', line)[0].strip()
                                    dependencies.append({
                                        'name': name,
                                        'version': line.replace(name, '').strip(),
                                        'type': 'production',
                                        'file': file
                                    })

                    except Exception as e:
                        logger.warning(f"Failed to parse dependency file {file}: {str(e)}")
                        continue

        return dependencies

    async def _find_entry_points(self, repo_path: str, language: str) -> List[Dict[str, Any]]:
        """Find main entry points of the application"""

        entry_points = []

        entry_patterns = {
            'python': ['main.py', 'app.py', 'run.py', '__main__.py', 'manage.py'],
            'javascript': ['index.js', 'app.js', 'server.js', 'main.js'],
            'typescript': ['index.ts', 'app.ts', 'server.ts', 'main.ts'],
            'java': ['Main.java', 'Application.java'],
            'go': ['main.go'],
            'rust': ['main.rs']
        }

        patterns = entry_patterns.get(language, [])

        for root, dirs, files in os.walk(repo_path):
            for file in files:
                if file in patterns:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, repo_path)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        entry_points.append({
                            'file': relative_path,
                            'type': 'main_entry',
                            'size': len(content),
                            'has_main': 'if __name__ == "__main__"' in content or 'main(' in content
                        })

                    except Exception as e:
                        logger.warning(f"Failed to read entry point {relative_path}: {str(e)}")
                        continue

        return entry_points
