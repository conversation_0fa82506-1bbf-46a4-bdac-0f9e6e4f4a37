"""
Dynamic MCP Server Code Generation Service
Generates repository-specific MCP servers based on actual repository analysis
"""
import json
import logging
import zipfile
import tempfile
import os
from typing import Dict, List, Any, Optional
from pathlib import Path

# Optional imports with fallbacks
try:
    import anthropic
    HAS_ANTHROPIC = True
except ImportError:
    anthropic = None
    HAS_ANTHROPIC = False

try:
    from ..config import settings
except ImportError:
    # Fallback settings for testing
    class MockSettings:
        anthropic_api_key = None
        openai_api_key = None
    settings = MockSettings()

logger = logging.getLogger(__name__)


class DynamicMCPGenerator:
    """Dynamic MCP server generator that adapts to any repository type"""
    
    def __init__(self):
        # Initialize Anthropic client if available
        if HAS_ANTHROPIC and settings.anthropic_api_key:
            self.anthropic_client = anthropic.AsyncAnthropic(api_key=settings.anthropic_api_key)
        else:
            self.anthropic_client = None
            logger.warning("Anthropic client not available - some features may be limited")

        self.supported_languages = ["python", "typescript", "javascript", "java", "csharp", "go", "rust"]
        self.recommended_languages = ["python", "typescript"]

    async def generate_server(
        self, 
        selected_tools: List[Dict[str, Any]], 
        language: str,
        repo_context: Dict[str, Any],
        business_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate a dynamic MCP server based on repository analysis"""
        
        if not self.anthropic_client:
            return {"error": "AI service not available"}
        
        # Extract repository information
        repo_info = repo_context.get("repository_info", {})
        repo_name = repo_info.get("name", "unknown-repo")
        repo_description = repo_info.get("description", f"Repository: {repo_name}")
        repo_language = repo_info.get("language", "Unknown")
        repo_technologies = repo_context.get("technologies", [])
        
        # Extract business context
        domain = business_analysis.get("domain", "general")
        capabilities = business_analysis.get("capabilities", [])
        api_endpoints = business_analysis.get("api_endpoints", [])
        
        # Generate repository-specific prompt
        generation_prompt = self._create_dynamic_prompt(
            selected_tools, language, repo_name, repo_description, 
            repo_language, repo_technologies, domain, capabilities, api_endpoints
        )
        
        try:
            # Generate code using AI
            response = await self.anthropic_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=8000,
                temperature=0.1,
                messages=[{"role": "user", "content": generation_prompt}]
            )
            
            # Parse the response
            generated_files = self._parse_ai_response(response.content[0].text, language, repo_name)
            
            # Create project structure
            project_files = self._create_project_structure(
                generated_files, language, repo_name, repo_description, selected_tools
            )
            
            # Create ZIP file
            zip_path = await self._create_project_zip(project_files, language, repo_context)
            
            return {
                "success": True,
                "files": project_files,
                "zip_path": zip_path,
                "language": language,
                "repository": repo_name,
                "tools_count": len(selected_tools)
            }
            
        except Exception as e:
            logger.error(f"Error generating MCP server: {str(e)}")
            return {"error": f"Generation failed: {str(e)}"}

    def _create_dynamic_prompt(
        self, 
        selected_tools: List[Dict[str, Any]], 
        language: str,
        repo_name: str,
        repo_description: str,
        repo_language: str,
        repo_technologies: List[str],
        domain: str,
        capabilities: List[str],
        api_endpoints: List[Dict[str, Any]]
    ) -> str:
        """Create a dynamic prompt based on actual repository analysis"""
        
        # Format tools information
        tools_info = []
        for tool in selected_tools:
            tool_name = tool.get("tool_name", tool.get("name", "unknown_tool"))
            tool_desc = tool.get("description", "")
            tools_info.append(f"- {tool_name}: {tool_desc}")
        
        tools_list = "\n".join(tools_info)
        
        # Format repository technologies
        tech_list = ", ".join(repo_technologies) if repo_technologies else "Not specified"
        
        # Format API endpoints if available
        endpoints_info = ""
        if api_endpoints:
            endpoints_info = "\nRepository API Endpoints:\n"
            for endpoint in api_endpoints[:5]:  # Limit to first 5
                method = endpoint.get("method", "GET")
                path = endpoint.get("path", "")
                endpoints_info += f"- {method} {path}\n"
        
        # Format capabilities
        capabilities_list = ", ".join(capabilities) if capabilities else "General functionality"
        
        return f"""
Generate a complete, functional MCP server in {language} for the repository: {repo_name}

REPOSITORY CONTEXT:
- Name: {repo_name}
- Description: {repo_description}
- Primary Language: {repo_language}
- Technologies: {tech_list}
- Domain: {domain}
- Capabilities: {capabilities_list}{endpoints_info}

REQUIRED MCP TOOLS:
{tools_list}

CRITICAL REQUIREMENTS:
1. Generate a COMPLETE, FUNCTIONAL MCP server (not templates or stubs)
2. Use the @modelcontextprotocol/sdk package for {language}
3. Implement ALL tools with real functionality based on repository context
4. NO placeholder code, TODO comments, or "pass" statements
5. Make the server specific to {repo_name} and its actual capabilities
6. Use repository-appropriate naming and terminology
7. Include proper error handling and logging
8. Generate production-ready code

GENERATE PROJECT FILES:
- main.{self._get_file_extension(language)} (MCP server entry point)
- package.json/requirements.txt (dependencies)
- README.md (setup instructions specific to {repo_name})
- config.{self._get_file_extension(language)} (configuration)
- .env.example (environment variables for {repo_name})

Return as JSON: {{"filename": "content", ...}}

Make this MCP server specifically designed for {repo_name} ({domain} domain) with {repo_language} technology stack.
"""

    def _get_file_extension(self, language: str) -> str:
        """Get appropriate file extension for language"""
        extensions = {
            "python": "py",
            "typescript": "ts", 
            "javascript": "js",
            "java": "java",
            "csharp": "cs",
            "go": "go",
            "rust": "rs"
        }
        return extensions.get(language, "txt")

    def _parse_ai_response(self, response_text: str, language: str, repo_name: str) -> Dict[str, str]:
        """Parse AI response to extract generated files"""
        try:
            # Try to extract JSON from response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # Fallback: create basic structure
                return self._create_fallback_structure(language, repo_name)
                
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback")
            return self._create_fallback_structure(language, repo_name)

    def _create_fallback_structure(self, language: str, repo_name: str) -> Dict[str, str]:
        """NO FALLBACKS - Fail fast if real analysis fails"""
        raise Exception(f"Cannot create fallback structure for {repo_name}. MCP generation requires real repository analysis and cannot proceed with placeholder/mock data.")

    def _create_project_structure(
        self, 
        generated_files: Dict[str, str], 
        language: str, 
        repo_name: str,
        repo_description: str,
        selected_tools: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """Create complete project structure with additional files"""
        
        project_files = generated_files.copy()
        
        # Ensure we have a README
        if "README.md" not in project_files:
            project_files["README.md"] = self._generate_readme(repo_name, repo_description, selected_tools, language)
        
        # Ensure we have dependencies file
        if language == "python" and "requirements.txt" not in project_files:
            project_files["requirements.txt"] = "mcp>=1.0.0\naiohttp>=3.8.0\npython-dotenv>=0.19.0"
        elif language in ["typescript", "javascript"] and "package.json" not in project_files:
            project_files["package.json"] = self._generate_package_json(repo_name, language)
        
        # Add .env.example if not present
        if ".env.example" not in project_files:
            project_files[".env.example"] = self._generate_env_example(repo_name)
        
        return project_files

    def _generate_readme(self, repo_name: str, repo_description: str, selected_tools: List[Dict], language: str) -> str:
        """Generate repository-specific README"""
        tools_list = "\n".join([f"- {tool.get('tool_name', tool.get('name', 'Unknown'))}" for tool in selected_tools])
        
        return f"""# {repo_name} MCP Server

{repo_description}

Generated MCP server with {len(selected_tools)} tools for {repo_name}.

## Tools

{tools_list}

## Installation

```bash
{'pip install -r requirements.txt' if language == 'python' else 'npm install'}
```

## Usage

```bash
{'python main.py' if language == 'python' else 'npm start'}
```

## Configuration

Copy `.env.example` to `.env` and configure your settings.
"""

    def _generate_package_json(self, repo_name: str, language: str) -> str:
        """Generate package.json for Node.js projects"""
        return json.dumps({
            "name": f"{repo_name.lower().replace(' ', '-')}-mcp",
            "version": "1.0.0",
            "description": f"MCP server for {repo_name}",
            "main": f"main.{'ts' if language == 'typescript' else 'js'}",
            "type": "module",
            "scripts": {
                "start": f"node main.{'js' if language == 'javascript' else 'ts'}",
                "build": "tsc" if language == "typescript" else "echo 'No build needed'"
            },
            "dependencies": {
                "@modelcontextprotocol/sdk": "^0.5.0",
                "dotenv": "^16.0.0"
            }
        }, indent=2)

    def _generate_env_example(self, repo_name: str) -> str:
        """Generate environment variables template"""
        safe_name = repo_name.upper().replace(" ", "_").replace("-", "_")
        return f"""# {repo_name} MCP Server Configuration
{safe_name}_URL=http://localhost:3000
{safe_name}_API_KEY=your_api_key_here

# Optional: Logging Configuration
LOG_LEVEL=INFO

# Optional: Server Configuration
MCP_SERVER_NAME={repo_name.lower().replace(" ", "-")}-mcp-server
"""

    async def _create_project_zip(self, project_files: Dict[str, str], language: str, repo_context: Dict[str, Any]) -> str:
        """Create ZIP file containing the generated project"""
        
        repo_name = repo_context.get("repository_info", {}).get("name", "unknown-repo")
        clean_repo_name = repo_name.lower().replace(" ", "-").replace("_", "-")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            project_dir = Path(temp_dir) / f"mcp-{clean_repo_name}"
            project_dir.mkdir(exist_ok=True)
            
            # Write all project files
            for file_path, content in project_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            # Create ZIP file
            zip_path = Path(temp_dir) / f"mcp-{clean_repo_name}.zip"
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(project_dir):
                    for file in files:
                        file_path = Path(root) / file
                        arc_path = file_path.relative_to(project_dir)
                        zipf.write(file_path, arc_path)
            
            # Move to permanent location
            permanent_zip_path = f"/tmp/mcp-{clean_repo_name}.zip"
            import shutil
            shutil.move(str(zip_path), permanent_zip_path)
            
            return permanent_zip_path

    def get_recommended_language(self, repo_context: Dict[str, Any], analysis_results: Dict[str, Any]) -> str:
        """Get recommended language for MCP server based on repository analysis - NO HARDCODED ASSUMPTIONS"""

        # Get primary language from repository
        primary_language = repo_context.get('language', '').lower()

        # Universal language support - support ANY language the repository uses
        universal_language_mapping = {
            'python': 'python',
            'javascript': 'javascript',
            'typescript': 'typescript',
            'java': 'java',
            'c#': 'csharp',
            'csharp': 'csharp',
            'c++': 'cpp',
            'cpp': 'cpp',
            'go': 'go',
            'rust': 'rust',
            'php': 'php',
            'ruby': 'ruby',
            'kotlin': 'kotlin',
            'swift': 'swift',
            'dart': 'dart',
            'scala': 'scala',
            'clojure': 'clojure',
            'haskell': 'haskell',
            'elixir': 'elixir',
            'erlang': 'erlang',
            'unknown': 'python'  # Safe fallback
        }

        # If primary language is supported, use it
        if primary_language in universal_language_mapping:
            return universal_language_mapping[primary_language]

        # Check language distribution for supported languages
        code_structure = analysis_results.get('code_structure', {})
        languages = code_structure.get('languages', {})

        for lang, percentage in languages.items():
            lang_lower = lang.lower()
            if lang_lower in universal_language_mapping and percentage > 10:  # At least 10% of codebase
                return universal_language_mapping[lang_lower]

        # Dynamic framework detection without hardcoded assumptions
        dependencies = analysis_results.get('dependencies', [])

        if dependencies:
            # Convert all dependencies to lowercase for analysis
            dep_text = ' '.join(str(dep).lower() for dep in dependencies)

            # Dynamic language inference based on dependency patterns
            if any(pattern in dep_text for pattern in ['npm', 'node', 'react', 'vue', 'angular', 'express', 'next']):
                return 'typescript'  # Prefer TypeScript for modern JS projects
            elif any(pattern in dep_text for pattern in ['pip', 'django', 'flask', 'fastapi', 'pandas', 'numpy']):
                return 'python'
            elif any(pattern in dep_text for pattern in ['maven', 'gradle', 'spring', 'junit']):
                return 'java'
            elif any(pattern in dep_text for pattern in ['cargo', 'crates']):
                return 'rust'
            elif any(pattern in dep_text for pattern in ['go.mod', 'golang']):
                return 'go'

        # Fallback to Python as it has the best MCP ecosystem
        return 'python'
