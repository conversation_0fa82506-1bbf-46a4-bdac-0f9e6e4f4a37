"""
Enhanced MCP Marketplace Intelligence
Comprehensive MCP discovery with strategic Tavily integration and domain-specific recommendations
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

from .tavily_client import TavilyClient
from .intelligent_analysis_service import IntelligentAnalysisService

logger = logging.getLogger(__name__)

@dataclass
class MCPServerInfo:
    name: str
    description: str
    url: str
    category: str
    domain_relevance: float
    capabilities: List[str]
    installation_method: str
    author: str
    tags: List[str]
    use_cases: List[str]
    integration_examples: List[str]

@dataclass
class WorkflowIntegration:
    workflow_name: str
    mcp_servers: List[str]
    integration_pattern: str
    implementation_steps: List[str]
    benefits: List[str]

class EnhancedMCPMarketplace:
    """Advanced MCP marketplace intelligence with domain-specific recommendations"""
    
    def __init__(self):
        self.tavily_client = TavilyClient()
        self.ai_service = IntelligentAnalysisService()
        
        # Comprehensive MCP database
        self.known_mcps = self._initialize_mcp_database()
        
        # Domain-specific search strategies
        self.domain_search_strategies = self._initialize_search_strategies()
    
    async def discover_domain_mcps(
        self, 
        domain: str, 
        user_goals: str,
        repository_context: Dict[str, Any]
    ) -> List[MCPServerInfo]:
        """Discover MCPs relevant to specific domain and user goals"""
        
        logger.info(f"Discovering MCPs for domain: {domain}, goals: {user_goals}")
        
        # Get domain-specific search queries
        search_queries = self._build_domain_search_queries(domain, user_goals, repository_context)
        
        # Search for MCPs using multiple strategies
        discovered_mcps = []
        
        # Strategy 1: Known MCP database lookup
        known_mcps = self._search_known_mcps(domain, user_goals)
        discovered_mcps.extend(known_mcps)
        
        # Strategy 2: Tavily web search
        web_mcps = await self._search_web_mcps(search_queries, domain)
        discovered_mcps.extend(web_mcps)
        
        # Strategy 3: GitHub repository search
        github_mcps = await self._search_github_mcps(domain, user_goals)
        discovered_mcps.extend(github_mcps)
        
        # Deduplicate and score
        unique_mcps = self._deduplicate_mcps(discovered_mcps)
        scored_mcps = await self._score_domain_relevance(unique_mcps, domain, user_goals, repository_context)
        
        # Return top recommendations
        return sorted(scored_mcps, key=lambda x: x.domain_relevance, reverse=True)[:10]
    
    async def suggest_workflow_integrations(
        self, 
        domain: str,
        user_goals: str,
        available_mcps: List[MCPServerInfo]
    ) -> List[WorkflowIntegration]:
        """Suggest how multiple MCPs can work together in workflows"""
        
        workflow_prompt = f"""Based on these available MCP servers for a {domain} project with goals: "{user_goals}"

Available MCPs:
{self._format_mcps_for_prompt(available_mcps)}

Suggest 3-5 workflow integrations that combine multiple MCP servers to achieve the user's goals.

For each workflow, provide:
1. Workflow name and description
2. Which MCP servers to use together
3. Integration pattern (sequential, parallel, conditional)
4. Step-by-step implementation
5. Benefits and value proposition

Format as JSON array of workflow objects."""

        try:
            response = await self.ai_service._call_ai_service(workflow_prompt, max_tokens=1500)
            
            # Parse workflow suggestions
            workflows = self._parse_workflow_suggestions(response)
            return workflows
            
        except Exception as e:
            logger.error(f"Failed to suggest workflow integrations: {str(e)}")
            return []
    
    def _initialize_mcp_database(self) -> Dict[str, List[MCPServerInfo]]:
        """Initialize comprehensive MCP database with known servers"""
        
        return {
            'official': [
                MCPServerInfo(
                    name="GitHub MCP Server",
                    description="Official GitHub integration for repository management, issues, and CI/CD",
                    url="https://github.com/github/github-mcp-server",
                    category="version_control",
                    domain_relevance=0.9,
                    capabilities=["Repository management", "Issue tracking", "PR automation", "CI/CD insights"],
                    installation_method="npm install @github/mcp-server",
                    author="GitHub",
                    tags=["git", "repository", "ci-cd", "issues"],
                    use_cases=["Code management", "Issue tracking", "Automated workflows"],
                    integration_examples=["Automated PR creation", "Issue analysis", "Repository insights"]
                ),
                MCPServerInfo(
                    name="Filesystem MCP Server",
                    description="Secure file operations with access controls",
                    url="https://github.com/modelcontextprotocol/servers",
                    category="file_management",
                    domain_relevance=0.8,
                    capabilities=["File CRUD", "Directory management", "Permission handling"],
                    installation_method="npm install @modelcontextprotocol/server-filesystem",
                    author="ModelContextProtocol",
                    tags=["files", "filesystem", "storage"],
                    use_cases=["File management", "Content storage", "Asset handling"],
                    integration_examples=["Content file management", "Asset organization", "Backup operations"]
                )
            ],
            'database': [
                MCPServerInfo(
                    name="PostgreSQL MCP Server",
                    description="PostgreSQL database operations and management",
                    url="https://github.com/modelcontextprotocol/servers",
                    category="database",
                    domain_relevance=0.9,
                    capabilities=["SQL queries", "Schema management", "Data migration"],
                    installation_method="npm install @modelcontextprotocol/server-postgres",
                    author="ModelContextProtocol",
                    tags=["postgresql", "database", "sql"],
                    use_cases=["Database management", "Data queries", "Schema operations"],
                    integration_examples=["Content storage", "User management", "Analytics data"]
                )
            ],
            'testing': [
                MCPServerInfo(
                    name="Playwright MCP Server",
                    description="Browser automation and testing capabilities",
                    url="https://github.com/microsoft/playwright-mcp",
                    category="testing",
                    domain_relevance=0.95,
                    capabilities=["Browser automation", "Screenshot capture", "Test execution"],
                    installation_method="npx @playwright/mcp@latest",
                    author="Microsoft",
                    tags=["playwright", "testing", "automation", "browser"],
                    use_cases=["E2E testing", "Web scraping", "UI automation"],
                    integration_examples=["Automated testing", "Visual regression", "Performance monitoring"]
                )
            ],
            'ai_workflow': [
                MCPServerInfo(
                    name="OpenAI MCP Server",
                    description="OpenAI API integration for LLM capabilities",
                    url="https://github.com/openai/openai-mcp-server",
                    category="ai_integration",
                    domain_relevance=0.9,
                    capabilities=["Text generation", "Embeddings", "Chat completion"],
                    installation_method="npm install @openai/mcp-server",
                    author="OpenAI",
                    tags=["openai", "llm", "ai", "gpt"],
                    use_cases=["AI integration", "Text processing", "Intelligent automation"],
                    integration_examples=["Content generation", "Smart analysis", "Automated responses"]
                )
            ]
        }
    
    def _initialize_search_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize domain-specific search strategies"""
        
        return {
            'cms': {
                'keywords': ['content management', 'headless cms', 'strapi', 'wordpress', 'drupal'],
                'integrations': ['database', 'media', 'cdn', 'analytics', 'seo'],
                'workflows': ['publishing', 'content creation', 'media management']
            },
            'testing': {
                'keywords': ['testing', 'automation', 'playwright', 'selenium', 'cypress'],
                'integrations': ['ci-cd', 'reporting', 'monitoring', 'browser'],
                'workflows': ['test execution', 'performance testing', 'visual regression']
            },
            'ai_workflow': {
                'keywords': ['ai workflow', 'agent', 'llm', 'automation', 'orchestration'],
                'integrations': ['llm providers', 'vector database', 'apis', 'tools'],
                'workflows': ['agent coordination', 'task automation', 'memory management']
            }
        }
    
    def _build_domain_search_queries(
        self, 
        domain: str, 
        user_goals: str, 
        repository_context: Dict[str, Any]
    ) -> List[str]:
        """Build strategic search queries for domain"""
        
        strategy = self.domain_search_strategies.get(domain, {})
        keywords = strategy.get('keywords', [])
        integrations = strategy.get('integrations', [])
        
        repo_name = repository_context.get('name', '')
        technologies = repository_context.get('technologies', [])
        
        queries = []
        
        # Domain-specific queries
        for keyword in keywords[:3]:
            queries.append(f"{keyword} mcp server")
            queries.append(f"{keyword} model context protocol")
        
        # Integration-focused queries
        for integration in integrations[:2]:
            queries.append(f"{integration} mcp {domain}")
        
        # Repository-specific queries
        if repo_name:
            queries.append(f"{repo_name} mcp integration")
        
        # Technology-specific queries
        for tech in technologies[:2]:
            queries.append(f"{tech} mcp server")
        
        # Goal-specific queries
        goal_keywords = user_goals.lower().split()[:3]
        for keyword in goal_keywords:
            if len(keyword) > 3:  # Skip short words
                queries.append(f"{keyword} mcp automation")
        
        return queries[:8]  # Limit to 8 queries
    
    def _search_known_mcps(self, domain: str, user_goals: str) -> List[MCPServerInfo]:
        """Search known MCP database"""
        
        relevant_mcps = []
        
        # Add official MCPs (always relevant)
        relevant_mcps.extend(self.known_mcps.get('official', []))
        
        # Add domain-specific MCPs
        if domain in self.known_mcps:
            relevant_mcps.extend(self.known_mcps[domain])
        
        # Add related domain MCPs
        related_domains = {
            'cms': ['database', 'file_management'],
            'testing': ['ci_cd', 'monitoring'],
            'ai_workflow': ['database', 'api_integration']
        }
        
        for related_domain in related_domains.get(domain, []):
            if related_domain in self.known_mcps:
                relevant_mcps.extend(self.known_mcps[related_domain])
        
        return relevant_mcps
    
    async def _search_web_mcps(self, queries: List[str], domain: str) -> List[MCPServerInfo]:
        """Search web for MCPs using Tavily"""
        
        discovered_mcps = []
        
        for query in queries[:4]:  # Limit web searches
            try:
                results = await self.tavily_client.search(query, max_results=3)
                
                for result in results:
                    mcp_info = await self._extract_mcp_from_result(result, domain)
                    if mcp_info:
                        discovered_mcps.append(mcp_info)
                        
            except Exception as e:
                logger.error(f"Web search failed for query '{query}': {str(e)}")
        
        return discovered_mcps
    
    async def _search_github_mcps(self, domain: str, user_goals: str) -> List[MCPServerInfo]:
        """Search GitHub for MCP repositories"""
        
        github_queries = [
            f"{domain} mcp server",
            "model context protocol server",
            f"{domain} mcp integration"
        ]
        
        discovered_mcps = []
        
        for query in github_queries:
            try:
                # Use Tavily to search GitHub specifically
                github_query = f"site:github.com {query}"
                results = await self.tavily_client.search(github_query, max_results=2)
                
                for result in results:
                    if 'github.com' in result.get('url', ''):
                        mcp_info = await self._extract_github_mcp(result, domain)
                        if mcp_info:
                            discovered_mcps.append(mcp_info)
                            
            except Exception as e:
                logger.error(f"GitHub search failed for query '{query}': {str(e)}")
        
        return discovered_mcps

    async def _extract_mcp_from_result(self, result: Dict[str, Any], domain: str) -> Optional[MCPServerInfo]:
        """Extract MCP information from search result"""

        url = result.get('url', '')
        title = result.get('title', '')
        content = result.get('content', '')

        # Check if it's actually MCP-related
        mcp_indicators = ['mcp', 'model context protocol', 'mcp-server', 'mcp server']
        if not any(indicator in content.lower() for indicator in mcp_indicators):
            return None

        # Use AI to extract structured information
        extraction_prompt = f"""Extract MCP server information from this search result:

Title: {title}
URL: {url}
Content: {content[:800]}

Extract and return JSON:
{{
  "name": "MCP server name",
  "description": "Brief description of capabilities",
  "category": "category (database, testing, file_management, etc.)",
  "capabilities": ["capability1", "capability2"],
  "installation_method": "installation command or method",
  "author": "author or organization",
  "tags": ["tag1", "tag2"],
  "use_cases": ["use_case1", "use_case2"]
}}

If this is not a valid MCP server, return {{"valid": false}}"""

        try:
            response = await self.ai_service._call_ai_service(extraction_prompt, max_tokens=400)

            import json
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                data = json.loads(json_content)
            else:
                data = json.loads(response)

            if data.get('valid') == False:
                return None

            return MCPServerInfo(
                name=data.get('name', title),
                description=data.get('description', content[:200]),
                url=url,
                category=data.get('category', 'general'),
                domain_relevance=0.5,  # Will be scored later
                capabilities=data.get('capabilities', []),
                installation_method=data.get('installation_method', 'npm install'),
                author=data.get('author', 'Unknown'),
                tags=data.get('tags', []),
                use_cases=data.get('use_cases', []),
                integration_examples=[]
            )

        except Exception as e:
            logger.error(f"Failed to extract MCP info: {str(e)}")
            return None

    async def _extract_github_mcp(self, result: Dict[str, Any], domain: str) -> Optional[MCPServerInfo]:
        """Extract MCP information from GitHub repository"""

        url = result.get('url', '')
        title = result.get('title', '')
        content = result.get('content', '')

        # Enhanced GitHub-specific extraction
        github_prompt = f"""Extract MCP server information from this GitHub repository:

Title: {title}
URL: {url}
Content: {content[:800]}

This appears to be a GitHub repository. Extract:
{{
  "name": "Repository/MCP name",
  "description": "What this MCP server does",
  "category": "primary category",
  "capabilities": ["specific capabilities"],
  "installation_method": "npm/pip/docker command",
  "author": "GitHub username or organization",
  "tags": ["relevant tags"],
  "use_cases": ["practical use cases"]
}}

Focus on actual MCP server functionality."""

        try:
            response = await self.ai_service._call_ai_service(github_prompt, max_tokens=400)

            import json
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                data = json.loads(json_content)
            else:
                data = json.loads(response)

            return MCPServerInfo(
                name=data.get('name', title),
                description=data.get('description', content[:200]),
                url=url,
                category=data.get('category', 'general'),
                domain_relevance=0.6,  # GitHub repos get slight boost
                capabilities=data.get('capabilities', []),
                installation_method=data.get('installation_method', 'npm install'),
                author=data.get('author', 'Unknown'),
                tags=data.get('tags', []),
                use_cases=data.get('use_cases', []),
                integration_examples=[]
            )

        except Exception as e:
            logger.error(f"Failed to extract GitHub MCP info: {str(e)}")
            return None

    def _deduplicate_mcps(self, mcps: List[MCPServerInfo]) -> List[MCPServerInfo]:
        """Remove duplicate MCPs based on URL and name similarity"""

        seen_urls = set()
        seen_names = set()
        unique_mcps = []

        for mcp in mcps:
            # Skip if URL already seen
            if mcp.url in seen_urls:
                continue

            # Skip if very similar name already seen
            name_lower = mcp.name.lower()
            if any(abs(len(name_lower) - len(seen_name)) < 3 and
                   (name_lower in seen_name or seen_name in name_lower)
                   for seen_name in seen_names):
                continue

            seen_urls.add(mcp.url)
            seen_names.add(name_lower)
            unique_mcps.append(mcp)

        return unique_mcps

    async def _score_domain_relevance(
        self,
        mcps: List[MCPServerInfo],
        domain: str,
        user_goals: str,
        repository_context: Dict[str, Any]
    ) -> List[MCPServerInfo]:
        """Score MCPs based on domain relevance and user goals"""

        for mcp in mcps:
            score = await self._calculate_relevance_score(mcp, domain, user_goals, repository_context)
            mcp.domain_relevance = score

        return mcps

    async def _calculate_relevance_score(
        self,
        mcp: MCPServerInfo,
        domain: str,
        user_goals: str,
        repository_context: Dict[str, Any]
    ) -> float:
        """Calculate relevance score for an MCP"""

        score = 0.0

        # Domain category match (30%)
        if mcp.category == domain:
            score += 0.3
        elif mcp.category in self._get_related_categories(domain):
            score += 0.15

        # Goal relevance (25%)
        goal_keywords = user_goals.lower().split()
        mcp_text = f"{mcp.name} {mcp.description} {' '.join(mcp.capabilities)}".lower()
        goal_matches = sum(1 for keyword in goal_keywords if keyword in mcp_text)
        score += (goal_matches / max(len(goal_keywords), 1)) * 0.25

        # Technology compatibility (20%)
        repo_technologies = [tech.lower() for tech in repository_context.get('technologies', [])]
        tech_matches = sum(1 for tech in repo_technologies if tech in mcp_text)
        score += (tech_matches / max(len(repo_technologies), 1)) * 0.2

        # Capability relevance (15%)
        if mcp.capabilities:
            capability_score = len(mcp.capabilities) / 10  # Normalize
            score += min(capability_score, 0.15)

        # Author credibility (10%)
        trusted_authors = ['github', 'microsoft', 'openai', 'modelcontextprotocol']
        if any(author in mcp.author.lower() for author in trusted_authors):
            score += 0.1

        return min(score, 1.0)  # Cap at 1.0

    def _get_related_categories(self, domain: str) -> List[str]:
        """Get categories related to a domain"""

        related = {
            'cms': ['database', 'file_management', 'api_integration', 'media'],
            'testing': ['ci_cd', 'monitoring', 'browser_automation', 'reporting'],
            'ai_workflow': ['database', 'api_integration', 'llm_integration', 'automation'],
            'web_framework': ['database', 'authentication', 'api_integration', 'caching']
        }

        return related.get(domain, [])

    def _format_mcps_for_prompt(self, mcps: List[MCPServerInfo]) -> str:
        """Format MCPs for AI prompt"""

        formatted = []
        for mcp in mcps:
            formatted.append(f"""
- {mcp.name}: {mcp.description}
  Category: {mcp.category}
  Capabilities: {', '.join(mcp.capabilities)}
  Use Cases: {', '.join(mcp.use_cases)}
""")

        return '\n'.join(formatted)

    def _parse_workflow_suggestions(self, response: str) -> List[WorkflowIntegration]:
        """Parse workflow suggestions from AI response"""

        try:
            import json
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                workflows_data = json.loads(json_content)
            else:
                workflows_data = json.loads(response)

            workflows = []
            for workflow_data in workflows_data:
                workflow = WorkflowIntegration(
                    workflow_name=workflow_data.get('workflow_name', ''),
                    mcp_servers=workflow_data.get('mcp_servers', []),
                    integration_pattern=workflow_data.get('integration_pattern', ''),
                    implementation_steps=workflow_data.get('implementation_steps', []),
                    benefits=workflow_data.get('benefits', [])
                )
                workflows.append(workflow)

            return workflows

        except Exception as e:
            logger.error(f"Failed to parse workflow suggestions: {str(e)}")
            return []
