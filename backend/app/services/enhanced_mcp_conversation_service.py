"""
Enhanced MCP Conversation Service
Provides context-aware MCP conversations with requirement validation and workflow integration
"""

import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .unified_context_service import UnifiedContextService, UnifiedRepositoryContext
from .requirements_driven_mcp_generator import RequirementsDrivenMCPGenerator
from .intelligent_analysis_service import IntelligentAnalysisService

logger = logging.getLogger(__name__)

@dataclass
class ConversationResponse:
    """Response from MCP conversation"""
    message: str
    suggestions: List[Dict[str, Any]]
    validation_result: Optional[Dict[str, Any]]
    next_actions: List[Dict[str, Any]]
    workflow_stage: str
    context_used: Dict[str, Any]

@dataclass
class WorkflowAction:
    """Represents a workflow action that can be taken"""
    action_type: str  # 'validate_requirements', 'generate_mcp', 'explore_capabilities'
    title: str
    description: str
    parameters: Dict[str, Any]
    enabled: bool
    confidence: float

class EnhancedMCPConversationService:
    """Enhanced conversation service with full context awareness and validation"""
    
    def __init__(self):
        self.context_service = UnifiedContextService()
        self.requirements_generator = RequirementsDrivenMCPGenerator()
        self.ai_service = IntelligentAnalysisService()
    
    async def chat_with_full_context(
        self,
        analysis_id: int,
        user_message: str,
        conversation_history: List[Dict[str, Any]] = None
    ) -> ConversationResponse:
        """Enhanced chat with full repository context and validation"""
        
        logger.info(f"Enhanced MCP chat for analysis {analysis_id}")
        
        # Get complete repository context
        context = await self.context_service.get_complete_repository_context(
            analysis_id, include_conversation=True, include_validation=True
        )
        
        # Analyze user intent and requirements
        intent_analysis = await self._analyze_user_intent_with_context(
            user_message, conversation_history, context
        )
        
        # Validate requirements if user is asking for MCP generation
        validation_result = None
        if intent_analysis['requires_validation']:
            validation_result = await self._validate_user_requirements(
                analysis_id, user_message, context
            )
        
        # Generate contextual response
        response_message = await self._generate_contextual_response(
            user_message, context, intent_analysis, validation_result
        )
        
        # Get relevant suggestions
        suggestions = await self._get_contextual_suggestions(
            user_message, context, intent_analysis, analysis_id
        )
        
        # Determine next actions
        next_actions = await self._determine_next_actions(
            intent_analysis, validation_result, context
        )
        
        # Update conversation history
        await self._update_conversation_history(
            analysis_id, user_message, response_message, validation_result
        )
        
        return ConversationResponse(
            message=response_message,
            suggestions=suggestions,
            validation_result=validation_result,
            next_actions=next_actions,
            workflow_stage=context.workflow_stage,
            context_used={
                'capabilities_count': len(context.indexed_capabilities),
                'mcp_suggestions_count': len(context.mcp_suggestions),
                'tech_stack': context.tech_stack.get('primary_language'),
                'validation_performed': validation_result is not None
            }
        )
    
    async def validate_and_prepare_for_playground(
        self,
        analysis_id: int,
        user_requirements: str
    ) -> Dict[str, Any]:
        """Validate requirements and prepare context for playground"""
        
        # Get complete context
        context = await self.context_service.get_complete_repository_context(analysis_id)
        
        # Validate requirements
        validation_result = await self.context_service.validate_user_requirements_against_repository(
            analysis_id, user_requirements, context
        )
        
        if not validation_result['is_feasible']:
            return {
                'success': False,
                'validation_result': validation_result,
                'message': 'Requirements cannot be fulfilled with current repository capabilities'
            }
        
        # Prepare playground context
        playground_context = await self.context_service.prepare_playground_context(
            analysis_id, validation_result
        )
        
        return {
            'success': True,
            'validation_result': validation_result,
            'playground_context': playground_context,
            'message': 'Requirements validated successfully. Ready for MCP generation.'
        }
    
    async def _analyze_user_intent_with_context(
        self,
        user_message: str,
        conversation_history: List[Dict[str, Any]],
        context: UnifiedRepositoryContext
    ) -> Dict[str, Any]:
        """Analyze user intent with full repository context"""
        
        message_lower = user_message.lower()
        
        # Detect if user wants to generate MCP
        generation_keywords = ['generate', 'create', 'build', 'make mcp', 'mcp server']
        wants_generation = any(keyword in message_lower for keyword in generation_keywords)
        
        # Detect if user is asking about capabilities
        capability_keywords = ['what can', 'capabilities', 'functions', 'features', 'what does']
        asking_capabilities = any(keyword in message_lower for keyword in capability_keywords)
        
        # Detect if user is asking questions about repository
        question_keywords = ['how', 'what', 'why', 'when', 'where', 'explain']
        asking_questions = any(keyword in message_lower for keyword in question_keywords)
        
        # Detect scope - is user asking about repository-specific functionality?
        repo_keywords = ['this repo', 'current code', 'existing', 'repository', 'codebase']
        repository_focused = any(keyword in message_lower for keyword in repo_keywords)
        
        # Detect if user is asking for suggestions
        suggestion_keywords = ['suggest', 'recommend', 'ideas', 'options', 'possibilities']
        wants_suggestions = any(keyword in message_lower for keyword in suggestion_keywords)
        
        # Determine primary intent
        primary_intent = 'general'
        if wants_generation:
            primary_intent = 'generate_mcp'
        elif asking_capabilities:
            primary_intent = 'explore_capabilities'
        elif asking_questions:
            primary_intent = 'ask_question'
        elif wants_suggestions:
            primary_intent = 'get_suggestions'
        
        # Determine if validation is required
        requires_validation = wants_generation or (wants_suggestions and repository_focused)
        
        return {
            'primary_intent': primary_intent,
            'wants_generation': wants_generation,
            'asking_capabilities': asking_capabilities,
            'asking_questions': asking_questions,
            'repository_focused': repository_focused,
            'wants_suggestions': wants_suggestions,
            'requires_validation': requires_validation,
            'message_length': len(user_message),
            'conversation_context': len(conversation_history) if conversation_history else 0
        }
    
    async def _validate_user_requirements(
        self,
        analysis_id: int,
        user_message: str,
        context: UnifiedRepositoryContext
    ) -> Dict[str, Any]:
        """Validate user requirements against repository capabilities"""
        
        return await self.context_service.validate_user_requirements_against_repository(
            analysis_id, user_message, context
        )
    
    async def _generate_contextual_response(
        self,
        user_message: str,
        context: UnifiedRepositoryContext,
        intent_analysis: Dict[str, Any],
        validation_result: Optional[Dict[str, Any]]
    ) -> str:
        """Generate contextual response based on intent and validation"""
        
        primary_intent = intent_analysis['primary_intent']
        
        if primary_intent == 'generate_mcp':
            return await self._generate_mcp_generation_response(
                user_message, context, validation_result
            )
        elif primary_intent == 'explore_capabilities':
            return await self._generate_capabilities_response(context)
        elif primary_intent == 'ask_question':
            return await self._generate_question_response(user_message, context)
        elif primary_intent == 'get_suggestions':
            return await self._generate_suggestions_response(context, validation_result)
        else:
            return await self._generate_general_response(user_message, context)
    
    async def _generate_mcp_generation_response(
        self,
        user_message: str,
        context: UnifiedRepositoryContext,
        validation_result: Optional[Dict[str, Any]]
    ) -> str:
        """Generate response for MCP generation requests"""
        
        if not validation_result:
            return "I'd be happy to help you generate an MCP server! Let me analyze your requirements against the repository capabilities first."
        
        if not validation_result['is_feasible']:
            missing_caps = validation_result.get('missing_capabilities', [])
            suggestions = validation_result.get('suggestions', [])
            
            response = f"I've analyzed your requirements, but there are some challenges:\n\n"
            response += f"**Feasibility Score:** {validation_result['feasibility_score']:.1%}\n\n"
            
            if missing_caps:
                response += "**Missing Capabilities:**\n"
                for cap in missing_caps:
                    response += f"• {cap}\n"
                response += "\n"
            
            if suggestions:
                response += "**Suggestions:**\n"
                for suggestion in suggestions:
                    response += f"• {suggestion}\n"
                response += "\n"
            
            response += "Would you like to refine your requirements or explore what's possible with the current repository?"
            
            return response
        
        else:
            capability_matches = validation_result.get('capability_matches', [])
            
            response = f"Great! Your requirements look feasible with this repository.\n\n"
            response += f"**Feasibility Score:** {validation_result['feasibility_score']:.1%}\n\n"
            
            if capability_matches:
                response += "**Matching Repository Capabilities:**\n"
                for match in capability_matches[:3]:  # Top 3 matches
                    cap = match['capability']
                    score = match['match_score']
                    response += f"• **{cap.name}** ({score:.1%} match) - {cap.description}\n"
                response += "\n"
            
            response += "I can generate an MCP server based on these capabilities. Would you like me to proceed with the generation?"
            
            return response
    
    async def _generate_capabilities_response(self, context: UnifiedRepositoryContext) -> str:
        """Generate response about repository capabilities"""
        
        response = f"Here's what I found in the **{context.repo_info['name']}** repository:\n\n"
        
        # Tech stack summary
        tech_stack = context.tech_stack
        response += f"**Tech Stack:**\n"
        response += f"• Primary Language: {tech_stack['primary_language']}\n"
        
        if tech_stack.get('language_percentages'):
            response += "• Language Distribution:\n"
            for lang, pct in list(tech_stack['language_percentages'].items())[:3]:
                response += f"  - {lang}: {pct}%\n"
        
        response += f"• Dependencies: {tech_stack['dependencies_count']} detected\n\n"
        
        # Indexed capabilities
        if context.indexed_capabilities:
            response += f"**Key Capabilities ({len(context.indexed_capabilities)} found):**\n"
            for cap in context.indexed_capabilities[:5]:  # Top 5
                response += f"• **{cap.name}** - {cap.description}\n"
            
            if len(context.indexed_capabilities) > 5:
                response += f"• ... and {len(context.indexed_capabilities) - 5} more capabilities\n"
            response += "\n"
        
        # MCP suggestions
        if context.mcp_suggestions:
            response += f"**MCP Opportunities ({len(context.mcp_suggestions)} identified):**\n"
            for suggestion in context.mcp_suggestions[:3]:  # Top 3
                response += f"• {suggestion.get('name', 'Unnamed')} - {suggestion.get('description', 'No description')}\n"
            response += "\n"
        
        response += "What specific functionality would you like to explore or turn into an MCP server?"
        
        return response
    
    async def _generate_question_response(
        self,
        user_message: str,
        context: UnifiedRepositoryContext
    ) -> str:
        """Generate response for questions about the repository"""
        
        # Use AI service to answer based on context
        context_summary = self._build_context_summary(context)
        
        prompt = f"""Answer the user's question about this repository based on the analysis:

Repository: {context.repo_info['name']}
Question: {user_message}

Repository Context:
{context_summary}

Provide a helpful, specific answer based on the repository analysis."""
        
        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=800)
            return response
        except Exception as e:
            logger.error(f"Failed to generate AI response: {str(e)}")
            return "I'd be happy to help answer your question! Could you be more specific about what you'd like to know about this repository?"
    
    async def _generate_suggestions_response(
        self,
        context: UnifiedRepositoryContext,
        validation_result: Optional[Dict[str, Any]]
    ) -> str:
        """Generate response with MCP suggestions"""
        
        response = f"Based on the **{context.repo_info['name']}** repository analysis, here are some MCP possibilities:\n\n"
        
        if context.mcp_suggestions:
            response += "**Repository-Specific MCP Ideas:**\n"
            for i, suggestion in enumerate(context.mcp_suggestions[:4], 1):
                response += f"{i}. **{suggestion.get('name', 'Unnamed')}**\n"
                response += f"   {suggestion.get('description', 'No description')}\n\n"
        
        if validation_result and validation_result.get('capability_matches'):
            response += "**Based on Your Requirements:**\n"
            for match in validation_result['capability_matches'][:3]:
                cap = match['capability']
                response += f"• Use **{cap.name}** for {cap.category} functionality\n"
            response += "\n"
        
        response += "Which of these interests you, or do you have a specific use case in mind?"
        
        return response
    
    async def _generate_general_response(
        self,
        user_message: str,
        context: UnifiedRepositoryContext
    ) -> str:
        """Generate general response"""
        
        return f"I'm here to help you explore MCP possibilities for the **{context.repo_info['name']}** repository. I can:\n\n" \
               "• **Analyze capabilities** - Show what the repository can do\n" \
               "• **Suggest MCP servers** - Recommend specific MCP tools\n" \
               "• **Validate ideas** - Check if your MCP ideas are feasible\n" \
               "• **Generate servers** - Create functional MCP code\n\n" \
               "What would you like to explore?"
    
    def _build_context_summary(self, context: UnifiedRepositoryContext) -> str:
        """Build a summary of repository context for AI"""
        
        summary = f"Repository: {context.repo_info['name']}\n"
        summary += f"Language: {context.tech_stack['primary_language']}\n"
        summary += f"Description: {context.repo_info.get('description', 'No description')}\n"
        
        if context.indexed_capabilities:
            summary += f"\nKey Capabilities:\n"
            for cap in context.indexed_capabilities[:5]:
                summary += f"- {cap.name}: {cap.description}\n"
        
        if context.tech_stack.get('frameworks'):
            summary += f"\nFrameworks: {', '.join(context.tech_stack['frameworks'])}\n"
        
        return summary
    
    async def _get_contextual_suggestions(
        self,
        user_message: str,
        context: UnifiedRepositoryContext,
        intent_analysis: Dict[str, Any],
        analysis_id: int = None
    ) -> List[Dict[str, Any]]:
        """Get contextual MCP suggestions"""

        # Use the passed analysis_id or try to get it from context
        target_analysis_id = analysis_id or context.repo_info.get('analysis_id', 0)

        return await self.context_service.get_contextual_mcp_suggestions(
            target_analysis_id,
            user_message,
            context.conversation_context
        )
    
    async def _determine_next_actions(
        self,
        intent_analysis: Dict[str, Any],
        validation_result: Optional[Dict[str, Any]],
        context: UnifiedRepositoryContext
    ) -> List[WorkflowAction]:
        """Determine next possible actions for the user"""
        
        actions = []
        
        # Always offer capability exploration
        actions.append(WorkflowAction(
            action_type='explore_capabilities',
            title='Explore Repository Capabilities',
            description='See what functions and features are available',
            parameters={'analysis_id': context.repo_info.get('analysis_id', 0)},
            enabled=True,
            confidence=1.0
        ))
        
        # Offer MCP generation if requirements are feasible
        if validation_result and validation_result['is_feasible']:
            actions.append(WorkflowAction(
                action_type='generate_mcp',
                title='Generate MCP Server',
                description='Create a functional MCP server based on validated requirements',
                parameters={
                    'analysis_id': context.repo_info.get('analysis_id', 0),
                    'validation_result': validation_result
                },
                enabled=True,
                confidence=validation_result['feasibility_score']
            ))
        
        # Offer requirement validation if user seems to want generation
        elif intent_analysis['wants_generation']:
            actions.append(WorkflowAction(
                action_type='validate_requirements',
                title='Validate Requirements',
                description='Check if your requirements can be met with this repository',
                parameters={'analysis_id': context.repo_info.get('analysis_id', 0)},
                enabled=True,
                confidence=0.8
            ))
        
        # Offer suggestions if user seems interested
        if intent_analysis['wants_suggestions'] or intent_analysis['primary_intent'] == 'general':
            actions.append(WorkflowAction(
                action_type='get_suggestions',
                title='Get MCP Suggestions',
                description='See recommended MCP servers for this repository',
                parameters={'analysis_id': context.repo_info.get('analysis_id', 0)},
                enabled=True,
                confidence=0.9
            ))
        
        return actions
    
    async def _update_conversation_history(
        self,
        analysis_id: int,
        user_message: str,
        response_message: str,
        validation_result: Optional[Dict[str, Any]]
    ):
        """Update conversation history in database or cache"""
        
        # This would store conversation history
        # For now, just log it
        logger.info(f"Conversation update for analysis {analysis_id}: user_message_length={len(user_message)}, validation_performed={validation_result is not None}")
