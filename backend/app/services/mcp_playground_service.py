"""
MCP Playground Service - Generate, test, and package MCP servers
"""
import os
import json
import tempfile
import subprocess
import zipfile
import asyncio
import io
import re
from typing import Dict, List, Any, Optional
import anthropic
from app.database import <PERSON>Local
from app.models import RepoAnalysis
from app.tasks.mcp_analysis_task import get_mcp_analysis_result
from .real_mcp_generator import RealMCPGenerator
from .repository_code_extractor import RepositoryCodeExtractor


class MCPPlaygroundService:
    def __init__(self):
        self.anthropic_client = None
        api_key = os.getenv('ANTHROPIC_API_KEY')
        if api_key:
            self.anthropic_client = anthropic.Anthropic(api_key=api_key)

        # Initialize real MCP generator for functional code generation
        self.real_generator = RealMCPGenerator()
        self.code_extractor = RepositoryCodeExtractor()

    async def generate_mcp_server(self, analysis_id: int, prompt: str) -> Dict[str, Any]:
        """Generate a functional MCP server using existing analysis data and user requirements."""

        db = SessionLocal()
        try:
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            if not analysis:
                raise Exception("Analysis not found")

            print(f"🎯 Generating MCP server for {analysis.repo_name} based on user prompt")
            print(f"📝 User requirements: {prompt}")

            # Use existing analysis data instead of re-analyzing
            existing_analysis = {
                "analysis_results": analysis.analysis_results or {},
                "integration_analysis": analysis.integration_analysis or {},
                "mcp_suggestions": analysis.mcp_suggestions or {},
                "specific_tools": analysis.specific_tools or {},
                "repo_info": {
                    "url": analysis.repo_url,
                    "name": analysis.repo_name,
                    "owner": analysis.repo_owner
                }
            }

            print(f"📊 Using existing analysis data:")
            print(f"   - Analysis results: {'✅' if analysis.analysis_results else '❌'}")
            print(f"   - Integration analysis: {'✅' if analysis.integration_analysis else '❌'}")
            print(f"   - MCP suggestions: {'✅' if analysis.mcp_suggestions else '❌'}")
            print(f"   - Specific tools: {'✅' if analysis.specific_tools else '❌'}")

            # Generate functional MCP server using existing data + user prompt
            server_result = await self._generate_functional_mcp_from_existing_data(
                existing_analysis, prompt, analysis_id
            )

            return server_result

        finally:
            db.close()

    async def _generate_functional_mcp_from_existing_data(
        self, existing_analysis: Dict[str, Any], user_prompt: str, analysis_id: int
    ) -> Dict[str, Any]:
        """Generate functional MCP server using existing analysis data and user requirements."""

        if not self.anthropic_client:
            raise Exception("Anthropic API key not configured")

        # Extract relevant information from existing analysis
        repo_info = existing_analysis.get("repo_info", {})
        analysis_results = existing_analysis.get("analysis_results", {})
        integration_analysis = existing_analysis.get("integration_analysis", {})
        mcp_suggestions = existing_analysis.get("mcp_suggestions", {})
        specific_tools = existing_analysis.get("specific_tools", {})

        # Get actual repository information and analyze backend code
        repo_details = await self._get_real_repository_details(
            repo_info.get("owner"), repo_info.get("name")
        )

        # Analyze actual backend code to understand what MCP server should do
        backend_analysis = await self._analyze_backend_code(
            repo_info.get("owner"), repo_info.get("name"), repo_details
        )

        # Determine the appropriate language and MCP SDK based on repository
        primary_language = repo_details.get('language', 'JavaScript')
        mcp_sdk_info = self._get_mcp_sdk_for_language(primary_language)

        # Build comprehensive context using existing data AND real repository details
        context = f"""
GENERATE FUNCTIONAL MCP SERVER - LANGUAGE AGNOSTIC

USER REQUIREMENTS:
{user_prompt}

REPOSITORY ANALYSIS:
- Name: {repo_details.get('name', 'Unknown')}
- Primary Language: {primary_language}
- Technologies: {', '.join(repo_details.get('technologies', []))}
- Dependencies: {', '.join(repo_details.get('dependencies', [])[:10])}

BACKEND CODE ANALYSIS:
- API Endpoints: {len(backend_analysis.get('api_endpoints', []))} found
- Functions: {len(backend_analysis.get('functions', []))} found
- Classes: {len(backend_analysis.get('classes', []))} found
- Services: {len(backend_analysis.get('services', []))} found

ACTUAL BACKEND CAPABILITIES:
{json.dumps(backend_analysis, indent=2)[:1500]}

MCP SDK INFORMATION FOR {primary_language}:
{json.dumps(mcp_sdk_info, indent=2)}

EXISTING ANALYSIS DATA:
{json.dumps(analysis_results, indent=2)[:1000]}

CRITICAL REQUIREMENTS:
1. Generate code in {primary_language} using the appropriate MCP SDK
2. Create MCP tools that EXPOSE the backend's actual capabilities:
   - Wrap existing API endpoints as MCP tools
   - Expose backend functions as callable tools
   - Provide access to backend services and utilities
   - Enable interaction with database models
3. Use ACTUAL dependencies and patterns from the repository
4. Implement ALL required features from user requirements:
   - Rate limiting and security constraints
   - Session management and cleanup
   - Comprehensive logging
   - Input validation and sanitization
   - Proper error handling
   - Memory management
5. Follow MCP protocol specifications for {primary_language}
6. Make it production-ready and immediately functional
7. Include comprehensive testing and validation

RESPONSE FORMAT:
SERVER_NAME: [descriptive name]
DESCRIPTION: [what the server does]

CODE:
```{primary_language.lower()}
[complete functional MCP server with ALL required features]
```

DEPENDENCIES:
```{mcp_sdk_info.get('package_file', 'json')}
[complete dependency file for {primary_language}]
```

README:
```markdown
[comprehensive README with installation and usage]
```
"""

        try:
            print("🤖 Generating functional MCP server with AI using existing analysis...")

            response = await asyncio.to_thread(
                self.anthropic_client.messages.create,
                model='claude-3-5-sonnet-20241022',
                max_tokens=4000,
                temperature=0.2,
                system=self._get_functional_system_prompt(),
                messages=[{
                    "role": "user",
                    "content": context
                }]
            )

            ai_response = response.content[0].text if response.content else ""

            if not ai_response:
                raise Exception("Empty response from AI")

            print(f"📝 AI response length: {len(ai_response)} characters")
            print(f"🔍 AI response preview: {ai_response[:500]}...")

            # Parse the response
            server_data = self._parse_generated_server(ai_response)

            print(f"🔍 Parsed code length: {len(server_data.get('code', ''))}")
            print(f"🔍 Parsed package.json length: {len(server_data.get('packageJson', ''))}")

            # Enhance with existing analysis data
            server_data["analysis_id"] = analysis_id
            server_data["based_on_existing_analysis"] = True
            server_data["user_prompt"] = user_prompt
            server_data["repository"] = repo_info

            print(f"✅ Generated MCP server: {server_data.get('name', 'Unknown')}")

            return server_data

        except Exception as e:
            print(f"❌ Failed to generate MCP server: {e}")
            raise Exception(f"MCP server generation failed: {e}")

    def _get_functional_system_prompt(self) -> str:
        """System prompt for generating functional MCP servers."""
        return """You are an expert MCP server developer who creates PRODUCTION-READY, FUNCTIONAL MCP servers for any programming language.

CRITICAL REQUIREMENTS:
1. Generate code in the SPECIFIED language using the appropriate MCP SDK
2. Include ALL required features from user requirements:
   - Rate limiting and security constraints
   - Session management and cleanup
   - Comprehensive logging system
   - Input validation and sanitization
   - Proper error handling and recovery
   - Memory management and resource cleanup
   - Authentication and authorization if needed
3. Use ONLY dependencies that exist in the repository or are standard for the language
4. Follow the repository's existing code patterns and structure
5. Generate REAL, FUNCTIONAL code - NO placeholders, TODOs, or mock implementations
6. Make the server immediately usable and production-ready

LANGUAGE-SPECIFIC MCP COMPLIANCE:
- JavaScript/TypeScript: Use @modelcontextprotocol/sdk with proper Server, setRequestHandler patterns
- Python: Use mcp package with proper decorators and async handlers
- Go: Use go-sdk with proper handler registration
- Rust: Use mcp-sdk with proper trait implementations

PRODUCTION REQUIREMENTS:
- Comprehensive error handling with proper logging
- Input validation using language-appropriate libraries (Zod, Pydantic, etc.)
- Resource management and cleanup
- Security best practices
- Performance optimization
- Proper testing structure
- Documentation and examples

Your generated MCP server must:
1. Pass ALL validation tests
2. Work without any modifications
3. Be immediately deployable to production
4. Handle all edge cases and errors gracefully
5. Include comprehensive logging and monitoring"""

    def _get_mcp_sdk_for_language(self, language: str) -> Dict[str, Any]:
        """Get MCP SDK information for different programming languages."""
        language_lower = language.lower()

        if language_lower in ['javascript', 'typescript']:
            return {
                "sdk_package": "@modelcontextprotocol/sdk",
                "import_pattern": "import { Server } from '@modelcontextprotocol/sdk/server/index.js';",
                "server_creation": "const server = new Server();",
                "handler_pattern": "server.setRequestHandler('tools/list', async () => { ... });",
                "package_file": "json",
                "package_manager": "npm",
                "test_command": "npm test",
                "start_command": "npm start",
                "validation_tools": ["node", "npm"],
                "additional_deps": ["zod", "winston"]
            }
        elif language_lower == 'python':
            return {
                "sdk_package": "mcp",
                "import_pattern": "from mcp import Server",
                "server_creation": "server = Server()",
                "handler_pattern": "@server.list_tools()\nasync def list_tools() -> list[Tool]: ...",
                "package_file": "txt",
                "package_manager": "pip",
                "test_command": "python -m pytest",
                "start_command": "python main.py",
                "validation_tools": ["python", "pip"],
                "additional_deps": ["pydantic", "loguru"]
            }
        elif language_lower in ['go', 'golang']:
            return {
                "sdk_package": "github.com/modelcontextprotocol/go-sdk",
                "import_pattern": "import \"github.com/modelcontextprotocol/go-sdk/server\"",
                "server_creation": "server := server.New()",
                "handler_pattern": "server.HandleListTools(func() []Tool { ... })",
                "package_file": "mod",
                "package_manager": "go",
                "test_command": "go test",
                "start_command": "go run main.go",
                "validation_tools": ["go"],
                "additional_deps": ["github.com/sirupsen/logrus"]
            }
        elif language_lower == 'rust':
            return {
                "sdk_package": "mcp-sdk",
                "import_pattern": "use mcp_sdk::Server;",
                "server_creation": "let server = Server::new();",
                "handler_pattern": "server.handle_list_tools(|| { ... });",
                "package_file": "toml",
                "package_manager": "cargo",
                "test_command": "cargo test",
                "start_command": "cargo run",
                "validation_tools": ["cargo"],
                "additional_deps": ["serde", "tokio", "tracing"]
            }
        else:
            # Default to JavaScript for unknown languages
            return {
                "sdk_package": "@modelcontextprotocol/sdk",
                "import_pattern": "import { Server } from '@modelcontextprotocol/sdk/server/index.js';",
                "server_creation": "const server = new Server();",
                "handler_pattern": "server.setRequestHandler('tools/list', async () => { ... });",
                "package_file": "json",
                "package_manager": "npm",
                "test_command": "npm test",
                "start_command": "npm start",
                "validation_tools": ["node", "npm"],
                "additional_deps": ["zod", "winston"]
            }

    async def _get_real_repository_details(self, owner: str, repo_name: str) -> Dict[str, Any]:
        """Get real repository details from GitHub API."""
        try:
            import requests

            # Get repository information
            repo_url = f"https://api.github.com/repos/{owner}/{repo_name}"
            response = requests.get(repo_url, timeout=10)

            if response.status_code != 200:
                print(f"⚠️ Could not fetch repository details: {response.status_code}")
                return {"language": "JavaScript", "technologies": [], "dependencies": []}

            repo_data = response.json()

            # Get languages used in the repository
            languages_url = f"https://api.github.com/repos/{owner}/{repo_name}/languages"
            languages_response = requests.get(languages_url, timeout=10)
            languages = languages_response.json() if languages_response.status_code == 200 else {}

            # Determine primary language and technologies
            primary_language = repo_data.get('language', 'JavaScript')
            technologies = list(languages.keys())

            # Get package files to understand dependencies
            dependencies = []
            code_patterns = {}

            # Check for package.json (Node.js/TypeScript)
            if 'JavaScript' in technologies or 'TypeScript' in technologies:
                package_url = f"https://api.github.com/repos/{owner}/{repo_name}/contents/package.json"
                package_response = requests.get(package_url, timeout=10)
                if package_response.status_code == 200:
                    try:
                        import base64
                        package_content = base64.b64decode(package_response.json()['content']).decode('utf-8')
                        package_data = json.loads(package_content)
                        deps = {**package_data.get('dependencies', {}), **package_data.get('devDependencies', {})}
                        dependencies = list(deps.keys())
                        code_patterns['package_json'] = package_data
                    except Exception as e:
                        print(f"⚠️ Could not parse package.json: {e}")

            # Check for requirements.txt (Python)
            elif 'Python' in technologies:
                requirements_url = f"https://api.github.com/repos/{owner}/{repo_name}/contents/requirements.txt"
                req_response = requests.get(requirements_url, timeout=10)
                if req_response.status_code == 200:
                    try:
                        import base64
                        req_content = base64.b64decode(req_response.json()['content']).decode('utf-8')
                        dependencies = [line.split('==')[0].split('>=')[0].strip() for line in req_content.split('\n') if line.strip()]
                        code_patterns['requirements'] = dependencies
                    except Exception as e:
                        print(f"⚠️ Could not parse requirements.txt: {e}")

            return {
                "name": repo_data.get('name'),
                "owner": repo_data.get('owner', {}).get('login'),
                "description": repo_data.get('description'),
                "language": primary_language,
                "technologies": technologies,
                "dependencies": dependencies[:20],  # Limit to 20
                "code_patterns": code_patterns,
                "stars": repo_data.get('stargazers_count', 0),
                "size": repo_data.get('size', 0)
            }

        except Exception as e:
            print(f"⚠️ Error fetching repository details: {e}")
            return {
                "language": "JavaScript",
                "technologies": [],
                "dependencies": [],
                "code_patterns": {}
            }

    async def _analyze_backend_code(self, owner: str, repo_name: str, repo_details: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze actual backend code to understand what the MCP server should expose."""
        try:
            import requests

            backend_analysis = {
                "api_endpoints": [],
                "functions": [],
                "classes": [],
                "database_models": [],
                "services": [],
                "utilities": []
            }

            # Common backend directories to analyze
            backend_dirs = ['backend', 'server', 'api', 'src', 'app', 'lib']

            for dir_name in backend_dirs:
                try:
                    # Get directory contents
                    dir_url = f"https://api.github.com/repos/{owner}/{repo_name}/contents/{dir_name}"
                    response = requests.get(dir_url, timeout=10)

                    if response.status_code == 200:
                        contents = response.json()

                        # Analyze files in the directory
                        for item in contents[:10]:  # Limit to 10 files
                            if item['type'] == 'file' and item['name'].endswith(('.py', '.js', '.ts', '.go', '.rs')):
                                file_analysis = await self._analyze_code_file(
                                    owner, repo_name, item['path']
                                )

                                # Merge analysis results
                                for key in backend_analysis:
                                    if key in file_analysis:
                                        backend_analysis[key].extend(file_analysis[key])

                except Exception as e:
                    print(f"⚠️ Could not analyze directory {dir_name}: {e}")
                    continue

            return backend_analysis

        except Exception as e:
            print(f"⚠️ Backend code analysis failed: {e}")
            return {
                "api_endpoints": [],
                "functions": [],
                "classes": [],
                "database_models": [],
                "services": [],
                "utilities": []
            }

    async def _analyze_code_file(self, owner: str, repo_name: str, file_path: str) -> Dict[str, Any]:
        """Analyze a specific code file to extract functions, classes, and API endpoints."""
        try:
            import requests
            import base64
            import re

            # Get file content
            file_url = f"https://api.github.com/repos/{owner}/{repo_name}/contents/{file_path}"
            response = requests.get(file_url, timeout=10)

            if response.status_code != 200:
                return {}

            file_data = response.json()
            content = base64.b64decode(file_data['content']).decode('utf-8')

            analysis = {
                "api_endpoints": [],
                "functions": [],
                "classes": [],
                "database_models": [],
                "services": [],
                "utilities": []
            }

            # Extract API endpoints
            api_patterns = [
                r'@app\.(get|post|put|delete|patch)\s*\([\'"]([^\'"]+)[\'"]',  # FastAPI
                r'app\.(get|post|put|delete|patch)\s*\([\'"]([^\'"]+)[\'"]',   # Express
                r'@router\.(get|post|put|delete|patch)\s*\([\'"]([^\'"]+)[\'"]', # Router
                r'def\s+(\w+).*@.*route',  # Flask
            ]

            for pattern in api_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple) and len(match) >= 2:
                        method, endpoint = match[0], match[1]
                        analysis["api_endpoints"].append({
                            "method": method.upper(),
                            "endpoint": endpoint,
                            "file": file_path
                        })

            # Extract functions
            function_patterns = [
                r'def\s+(\w+)\s*\(',  # Python
                r'function\s+(\w+)\s*\(',  # JavaScript
                r'const\s+(\w+)\s*=\s*(?:async\s+)?\(',  # JavaScript arrow functions
                r'func\s+(\w+)\s*\(',  # Go
                r'fn\s+(\w+)\s*\(',  # Rust
            ]

            for pattern in function_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if not match.startswith('_') and len(match) > 2:  # Skip private functions
                        analysis["functions"].append({
                            "name": match,
                            "file": file_path
                        })

            # Extract classes
            class_patterns = [
                r'class\s+(\w+)',  # Python, JavaScript
                r'type\s+(\w+)\s+struct',  # Go
                r'struct\s+(\w+)',  # Rust
            ]

            for pattern in class_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    analysis["classes"].append({
                        "name": match,
                        "file": file_path
                    })

            return analysis

        except Exception as e:
            print(f"⚠️ Could not analyze file {file_path}: {e}")
            return {}

    async def create_download_package(self, server_data: Dict[str, Any]) -> bytes:
        """Create a ZIP package of the generated MCP server."""
        try:
            import zipfile
            import io

            files = server_data.get('files', {})
            if not files:
                raise Exception("No files to package")

            # Create ZIP file in memory
            zip_buffer = io.BytesIO()

            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for file_path, content in files.items():
                    if content and isinstance(content, str):  # Only add non-empty string files
                        zip_file.writestr(file_path, content)

            return zip_buffer.getvalue()

        except Exception as e:
            print(f"❌ Failed to create download package: {e}")
            raise Exception(f"Download package creation failed: {e}")

    async def _analyze_real_repository(self, repo_url: str, repo_owner: str, repo_name: str) -> Dict[str, Any]:
        """Analyze the actual repository to understand its capabilities."""
        try:
            print(f"🔍 Analyzing repository: {repo_name}")

            # Use GitHub service to get repository information
            github_token = os.getenv('GITHUB_TOKEN', '')

            # Get repository files and structure
            from .github_service import GitHubService
            github_service = GitHubService()

            # Get repository contents
            repo_contents = github_service.get_repo_contents(
                github_token, repo_owner, repo_name
            )

            # Analyze the repository to detect technologies and capabilities
            technologies = []
            functions = []
            apis = []

            # Check for common files and technologies
            file_names = [item.get('name', '') for item in repo_contents if item.get('type') == 'file']

            # Detect technologies from files
            if 'package.json' in file_names:
                technologies.append('nodejs')
            if 'requirements.txt' in file_names or any(f.endswith('.py') for f in file_names):
                technologies.append('python')
            if any('playwright' in f.lower() for f in file_names):
                technologies.append('playwright')
            if any('react' in f.lower() for f in file_names):
                technologies.append('react')

            # Get package.json to understand dependencies
            if 'package.json' in file_names:
                try:
                    package_content = github_service.get_file_content(
                        github_token, repo_owner, repo_name, 'package.json'
                    )
                    if package_content:
                        package_data = json.loads(package_content)
                        deps = {**package_data.get('dependencies', {}), **package_data.get('devDependencies', {})}

                        if 'playwright' in deps:
                            technologies.append('playwright')
                        if 'puppeteer' in deps:
                            technologies.append('puppeteer')
                        if 'express' in deps:
                            technologies.append('express')
                        if 'react' in deps:
                            technologies.append('react')
                        if 'next' in deps:
                            technologies.append('nextjs')
                except Exception as e:
                    print(f"⚠️ Could not parse package.json: {e}")

            # Analyze key JavaScript/TypeScript files for functions
            js_files = [f for f in file_names if f.endswith(('.js', '.ts', '.jsx', '.tsx'))][:5]  # Limit to 5 files

            for file_name in js_files:
                try:
                    file_content = github_service.get_file_content(
                        github_token, repo_owner, repo_name, file_name
                    )
                    if file_content:
                        # Extract function names using simple regex
                        import re
                        func_patterns = [
                            r'function\s+(\w+)',
                            r'const\s+(\w+)\s*=\s*(?:async\s+)?(?:\([^)]*\)\s*=>|\([^)]*\)\s*{)',
                            r'export\s+(?:async\s+)?function\s+(\w+)'
                        ]

                        for pattern in func_patterns:
                            matches = re.findall(pattern, file_content)
                            functions.extend(matches)

                        # Look for API patterns
                        if 'app.get' in file_content or 'app.post' in file_content:
                            apis.append(f"Express API in {file_name}")
                        if 'router.' in file_content:
                            apis.append(f"Router in {file_name}")

                except Exception as e:
                    print(f"⚠️ Could not analyze {file_name}: {e}")

            return {
                "repo_url": repo_url,
                "repo_name": repo_name,
                "technologies": list(set(technologies)),
                "functions": list(set(functions))[:20],  # Limit to 20 functions
                "apis": apis,
                "files": file_names[:10]  # Top 10 files
            }

        except Exception as e:
            print(f"⚠️ Repository analysis failed: {e}")
            return {
                "repo_url": repo_url,
                "repo_name": repo_name,
                "technologies": [],
                "functions": [],
                "apis": [],
                "files": []
            }

    async def _generate_tools_from_requirements(self, prompt: str, repo_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate tool specifications based on user requirements and repository capabilities."""
        if not self.anthropic_client:
            return []

        try:
            # Build context for tool generation
            context = f"""
ANALYZE USER REQUIREMENTS AND REPOSITORY CAPABILITIES TO SUGGEST MCP TOOLS

USER REQUIREMENTS:
{prompt}

REPOSITORY ANALYSIS:
- Repository: {repo_analysis.get('repo_name', 'Unknown')}
- Technologies: {', '.join(repo_analysis.get('technologies', []))}
- Available Functions: {', '.join(repo_analysis.get('functions', [])[:10])}
- API Endpoints: {len(repo_analysis.get('apis', []))} detected

TASK:
Based on the user requirements and repository capabilities, suggest 3-5 specific MCP tools that:
1. Address the user's needs
2. Leverage the repository's actual capabilities
3. Can be implemented using the detected functions/APIs

Respond in this format:
TOOL_1:
name: tool_name
description: what it does
implementation_hint: which repository function/API to use

TOOL_2:
name: tool_name
description: what it does
implementation_hint: which repository function/API to use

Continue for each tool...
"""

            response = await asyncio.to_thread(
                self.anthropic_client.messages.create,
                model='claude-3-5-sonnet-20241022',
                max_tokens=1500,
                temperature=0.2,
                messages=[{
                    "role": "user",
                    "content": context
                }]
            )

            ai_response = response.content[0].text if response.content else ""

            # Parse tools from AI response
            tools = self._parse_tools_from_ai_response(ai_response)

            print(f"🛠️ Generated {len(tools)} tools from requirements")
            return tools

        except Exception as e:
            print(f"⚠️ Tool generation failed: {e}")
            return []

    def _parse_tools_from_ai_response(self, ai_response: str) -> List[Dict[str, Any]]:
        """Parse tool specifications from AI response."""
        tools = []

        # Split by TOOL_ markers
        tool_sections = re.split(r'TOOL_\d+:', ai_response)

        for section in tool_sections[1:]:  # Skip first empty section
            lines = section.strip().split('\n')
            tool = {}

            for line in lines:
                if line.startswith('name:'):
                    tool['name'] = line.replace('name:', '').strip()
                elif line.startswith('description:'):
                    tool['description'] = line.replace('description:', '').strip()
                elif line.startswith('implementation_hint:'):
                    tool['implementation_hint'] = line.replace('implementation_hint:', '').strip()

            if tool.get('name') and tool.get('description'):
                tools.append(tool)

        return tools

    async def _convert_real_server_to_playground_format(self, real_server: Dict[str, Any], repo_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Convert real generator output to playground format."""
        try:
            # Read the generated files from the real generator
            zip_path = real_server.get('zip_file_path')
            if not zip_path or not os.path.exists(zip_path):
                raise Exception("Generated zip file not found")

            # Extract files from zip
            files = {}
            with zipfile.ZipFile(zip_path, 'r') as zip_file:
                for file_name in zip_file.namelist():
                    try:
                        content = zip_file.read(file_name).decode('utf-8')
                        files[file_name] = content
                    except UnicodeDecodeError:
                        # Skip binary files
                        continue

            # Get main server file content
            main_file = files.get('main.py', files.get('index.js', ''))
            package_file = files.get('requirements.txt', files.get('package.json', ''))
            readme_file = files.get('README.md', '')

            return {
                "name": real_server.get('server_name', 'Generated MCP Server'),
                "description": f"Functional MCP server generated from {repo_analysis.get('repo_name', 'repository')} code",
                "tools": [],  # Tools are embedded in the code
                "code": main_file,
                "packageJson": package_file,
                "readme": readme_file,
                "files": files,
                "is_functional": True,
                "technologies": repo_analysis.get('technologies', []),
                "tools_count": real_server.get('tools_count', 0)
            }

        except Exception as e:
            print(f"⚠️ Conversion failed: {e}")
            raise Exception(f"Failed to convert real server: {e}")

    async def _generate_ai_fallback_server(self, analysis: RepoAnalysis, prompt: str, repo_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """NO AI FALLBACKS - Fail fast when real generator fails"""
        raise Exception("AI fallback generation not allowed. MCP generation must use real repository code extraction and cannot proceed with AI-generated placeholder code.")

        # Build enhanced context with repository analysis
        context = f"""
GENERATE FUNCTIONAL MCP SERVER

USER REQUIREMENTS:
{prompt}

REPOSITORY CONTEXT:
- Repository: {analysis.repo_url}
- Name: {analysis.repo_name}
- Owner: {analysis.repo_owner}
- Technologies: {', '.join(repo_analysis.get('technologies', []))}
- Functions: {', '.join(repo_analysis.get('functions', [])[:10])}

REQUIREMENTS:
1. Generate a FUNCTIONAL MCP server (no placeholders)
2. Use technologies detected in the repository
3. Implement real functionality based on user requirements
4. Include proper error handling and logging
5. Make it production-ready

Generate complete server code, package.json, and README.
"""

        try:
            response = await asyncio.to_thread(
                self.anthropic_client.messages.create,
                model='claude-3-5-sonnet-20241022',
                max_tokens=8000,
                temperature=0.3,
                system=self._get_functional_generation_system_prompt(),
                messages=[{
                    "role": "user",
                    "content": context
                }]
            )

            ai_response = response.content[0].text if response.content else ""

            # Parse the AI response
            server = self._parse_generated_server(ai_response)
            server["is_functional"] = False  # Mark as AI-generated
            server["technologies"] = repo_analysis.get('technologies', [])

            return server

        except Exception as e:
            raise Exception(f"AI fallback generation failed: {e}")

    def _get_functional_generation_system_prompt(self) -> str:
        """Get system prompt for functional MCP generation."""
        return """You are an expert MCP server developer who creates FUNCTIONAL, WORKING MCP servers.

CRITICAL REQUIREMENTS:
1. Generate COMPLETE, FUNCTIONAL code - NO placeholders, TODO comments, or incomplete methods
2. Use ACTUAL technologies and patterns from the repository analysis
3. Implement WORKING functionality that addresses user requirements
4. Include comprehensive error handling and logging
5. Follow MCP protocol specifications exactly
6. Make the server production-ready and immediately usable
7. ALWAYS complete all methods and handlers - never leave them unfinished
8. Ensure all code blocks are properly closed and complete

IMPORTANT: Your response must include COMPLETE code. Do not truncate or abbreviate any part of the implementation. Every method, function, and handler must be fully implemented."""

    async def test_mcp_server(self, server_code: str, package_json: str, analysis_id: int = None, user_prompt: str = None) -> List[Dict[str, Any]]:
        """Comprehensively test the generated MCP server code."""
        test_results = []

        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                # Write files
                server_file = os.path.join(temp_dir, 'index.js')
                package_file = os.path.join(temp_dir, 'package.json')

                with open(server_file, 'w') as f:
                    f.write(server_code)

                with open(package_file, 'w') as f:
                    f.write(package_json)

                # Detect language from file extension or content
                detected_language = self._detect_language_from_file(server_file, server_code)

                # Test 1: Syntax validation
                syntax_result = await self._test_syntax(server_file, detected_language)
                test_results.append(syntax_result)

                # Test 2: Package.json validation
                package_result = await self._test_package_json(package_file)
                test_results.append(package_result)

                # Test 3: MCP protocol compliance
                mcp_result = await self._test_mcp_compliance(server_code)
                test_results.append(mcp_result)

                # Test 4: Dependencies check
                deps_result = await self._test_dependencies(temp_dir)
                test_results.append(deps_result)

                # Test 5: Functional validation against requirements
                if user_prompt:
                    functional_result = await self._test_functional_requirements(server_code, user_prompt)
                    test_results.append(functional_result)

                # Test 6: Repository context validation
                if analysis_id:
                    context_result = await self._test_repository_context(server_code, analysis_id)
                    test_results.append(context_result)

                # Test 7: Security validation
                security_result = await self._test_security_practices(server_code)
                test_results.append(security_result)

                # Test 8: Performance and best practices
                performance_result = await self._test_performance_practices(server_code)
                test_results.append(performance_result)

                # Test 9: Tool functionality validation
                tools_result = await self._test_tool_functionality(server_code)
                test_results.append(tools_result)

            except Exception as e:
                test_results.append({
                    "success": False,
                    "message": "Testing setup failed",
                    "details": str(e)
                })

        return test_results

    async def create_download_package(self, server: Dict[str, Any]) -> bytes:
        """Create a downloadable zip package of the MCP server."""
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Add main server file
            zip_file.writestr('index.js', server['code'])
            
            # Add package.json
            zip_file.writestr('package.json', server['packageJson'])
            
            # Add README.md
            zip_file.writestr('README.md', server['readme'])
            
            # Add example usage
            example_usage = self._generate_example_usage(server)
            zip_file.writestr('example-usage.md', example_usage)
            
            # Add installation script
            install_script = self._generate_install_script(server)
            zip_file.writestr('install.sh', install_script)
            
            # Add test file
            test_file = self._generate_test_file(server)
            zip_file.writestr('test.js', test_file)
        
        zip_buffer.seek(0)
        return zip_buffer.getvalue()

    async def _build_generation_context(self, analysis: RepoAnalysis, mcp_analysis: Dict, prompt: str) -> str:
        """Build context for MCP server generation."""
        return f"""Generate a complete, functional MCP server based on this request:

USER REQUEST: {prompt}

REPOSITORY CONTEXT:
- Repository: {analysis.repo_url}
- Repository Name: {analysis.repo_name}
- Owner: {analysis.repo_owner}
- Analysis Results: {json.dumps(analysis.analysis_results, indent=2) if analysis.analysis_results else "No analysis results available"}

MCP ANALYSIS:
{json.dumps(mcp_analysis, indent=2) if mcp_analysis else "No specific MCP analysis available"}

REQUIREMENTS:
1. Generate a COMPLETE, working MCP server with ALL methods fully implemented
2. Include proper error handling and validation
3. Follow MCP protocol specifications exactly
4. Include comprehensive documentation
5. Make it production-ready with proper logging
6. Include all necessary dependencies in package.json
7. Provide clear installation and usage instructions
8. NEVER leave methods incomplete or use placeholder code
9. Ensure all handlers and functions are fully implemented

CRITICAL: Your response must contain COMPLETE code. Do not truncate any part of the implementation.

Please provide the response in this EXACT format:

SERVER_NAME: [Name of the MCP server]
DESCRIPTION: [Brief description]

TOOLS:
[List each tool with name, description, and input schema]

CODE:
```javascript
[Complete server code here]
```

PACKAGE_JSON:
```json
[Complete package.json here]
```

README:
```markdown
[Complete README.md here]
```"""

    def _get_generation_system_prompt(self) -> str:
        """Get the system prompt for MCP server generation."""
        return """You are an expert MCP (Model Context Protocol) server developer. You create production-ready, fully functional MCP servers that follow the official MCP specification.

Key requirements:
1. Always use the @modelcontextprotocol/sdk package
2. Implement proper error handling and validation
3. Follow MCP protocol exactly - no deviations
4. Include comprehensive logging
5. Make servers robust and production-ready
6. Include proper TypeScript types when applicable
7. Provide clear documentation and examples

Your generated servers must be immediately usable without any modifications."""

    def _parse_generated_server(self, ai_response: str) -> Dict[str, Any]:
        """Parse the AI response to extract server components."""
        import re

        # Extract server name
        name_match = re.search(r'SERVER_NAME:\s*(.+)', ai_response)
        name = name_match.group(1).strip() if name_match else "Generated MCP Server"

        # Extract description
        desc_match = re.search(r'DESCRIPTION:\s*(.+)', ai_response)
        description = desc_match.group(1).strip() if desc_match else "AI-generated MCP server"

        # Extract tools section
        tools_match = re.search(r'TOOLS:\s*\n(.*?)\n\nCODE:', ai_response, re.DOTALL)
        tools_text = tools_match.group(1).strip() if tools_match else ""
        tools = self._parse_tools(tools_text)

        # Extract code (support multiple languages)
        code_match = re.search(r'CODE:\s*\n```(?:javascript|js|typescript|ts|python|py)?\n(.*?)\n```', ai_response, re.DOTALL)
        code = code_match.group(1).strip() if code_match else ""

        # Extract package.json or dependencies file
        package_match = re.search(r'(?:PACKAGE_JSON|DEPENDENCIES):\s*\n```(?:json|txt|toml|mod)?\n(.*?)\n```', ai_response, re.DOTALL)
        package_json = package_match.group(1).strip() if package_match else ""

        # Generate fallback package.json if not provided or invalid
        if not package_json:
            package_json = self._generate_fallback_package_json(name, description)
        else:
            # Validate and fix existing package.json
            package_json = self._validate_and_fix_package_json(package_json, name, description)

        # Extract README
        readme_match = re.search(r'README:\s*\n```(?:markdown|md)?\n(.*?)\n```', ai_response, re.DOTALL)
        readme = readme_match.group(1).strip() if readme_match else ""

        # Generate additional files for a complete project
        files = {
            "index.js": code,
            "package.json": package_json,
            "README.md": readme,
            ".gitignore": self._generate_gitignore(),
            "test/server.test.js": self._generate_test_file(name, tools),
            "examples/usage.js": self._generate_usage_example(name, tools),
            "install.sh": self._generate_install_script(name),
            "docker/Dockerfile": self._generate_dockerfile(),
            "docker/docker-compose.yml": self._generate_docker_compose(name)
        }

        return {
            "name": name,
            "description": description,
            "tools": tools,
            "code": code,
            "packageJson": package_json,
            "readme": readme,
            "files": files
        }

    def _generate_fallback_package_json(self, name: str, description: str) -> str:
        """NO FALLBACK PACKAGE.JSON - Fail fast when real data isn't available"""
        raise Exception("Fallback package.json generation not allowed. MCP generation must extract real dependencies from repository analysis.")

    def _validate_and_fix_package_json(self, package_json: str, name: str, description: str) -> str:
        """Validate and fix package.json to ensure it has all required fields."""
        import json

        try:
            package_data = json.loads(package_json)
        except json.JSONDecodeError:
            # If invalid JSON, generate fallback
            return self._generate_fallback_package_json(name, description)

        # Ensure required fields are present
        required_fields = {
            "name": name.lower().replace(' ', '-'),
            "version": "1.0.0",
            "main": "index.js",
            "type": "module"
        }

        for field, default_value in required_fields.items():
            if field not in package_data:
                package_data[field] = default_value

        # Ensure MCP SDK dependency
        if "dependencies" not in package_data:
            package_data["dependencies"] = {}

        if "@modelcontextprotocol/sdk" not in package_data["dependencies"]:
            package_data["dependencies"]["@modelcontextprotocol/sdk"] = "^1.0.0"

        # Ensure proper scripts
        if "scripts" not in package_data:
            package_data["scripts"] = {}

        package_data["scripts"]["start"] = "node index.js"

        # Ensure Node.js version requirement
        if "engines" not in package_data:
            package_data["engines"] = {}

        package_data["engines"]["node"] = ">=18.0.0"

        return json.dumps(package_data, indent=2)

    def _parse_tools(self, tools_text: str) -> List[Dict[str, Any]]:
        """Parse tools from the tools section."""
        tools = []
        # Simple parsing - in a real implementation, this would be more sophisticated
        lines = tools_text.split('\n')
        current_tool = None
        
        for line in lines:
            line = line.strip()
            if line.startswith('- ') or line.startswith('* '):
                if current_tool:
                    tools.append(current_tool)
                current_tool = {
                    "name": line[2:].split(':')[0].strip(),
                    "description": line[2:].split(':', 1)[1].strip() if ':' in line else line[2:],
                    "inputSchema": {"type": "object", "properties": {}}
                }
        
        if current_tool:
            tools.append(current_tool)
        
        return tools

    def _detect_language_from_file(self, file_path: str, content: str) -> str:
        """Detect programming language from file extension and content."""
        # Check file extension
        if file_path.endswith('.py'):
            return 'Python'
        elif file_path.endswith('.js'):
            return 'JavaScript'
        elif file_path.endswith('.ts'):
            return 'TypeScript'
        elif file_path.endswith('.go'):
            return 'Go'
        elif file_path.endswith('.rs'):
            return 'Rust'

        # Check content patterns
        if 'import {' in content and 'from ' in content:
            if 'typescript' in content.lower() or ': string' in content or 'interface ' in content:
                return 'TypeScript'
            else:
                return 'JavaScript'
        elif 'from ' in content and 'import ' in content and 'def ' in content:
            return 'Python'
        elif 'package main' in content and 'func ' in content:
            return 'Go'
        elif 'fn main()' in content and 'use ' in content:
            return 'Rust'

        # Default to JavaScript
        return 'JavaScript'

    async def _test_syntax(self, server_file: str, language: str = "JavaScript") -> Dict[str, Any]:
        """Test syntax for different programming languages."""
        try:
            language_lower = language.lower()

            if language_lower in ['javascript', 'typescript']:
                # Test with Node.js
                result = subprocess.run(
                    ['node', '--check', server_file],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
            elif language_lower == 'python':
                # Test with Python
                result = subprocess.run(
                    ['python', '-m', 'py_compile', server_file],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
            else:
                # For other languages, just check if file exists and is readable
                with open(server_file, 'r') as f:
                    content = f.read()
                if len(content.strip()) > 0:
                    return {
                        "success": True,
                        "message": f"{language} syntax validation skipped",
                        "details": f"File exists and is readable. Language-specific validation not available for {language}"
                    }
                else:
                    return {
                        "success": False,
                        "message": f"{language} file is empty",
                        "details": "Generated file has no content"
                    }

            if result.returncode == 0:
                return {
                    "success": True,
                    "message": f"{language} syntax validation passed",
                    "details": f"{language} syntax is valid"
                }
            else:
                return {
                    "success": False,
                    "message": f"{language} syntax validation failed",
                    "details": result.stderr
                }
        except FileNotFoundError as e:
            return {
                "success": False,
                "message": f"{language} validator not found",
                "details": f"Required tool not available: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"{language} syntax test error",
                "details": str(e)
            }

    async def _test_package_json(self, package_file: str) -> Dict[str, Any]:
        """Test package.json validity."""
        try:
            with open(package_file, 'r') as f:
                package_data = json.load(f)
            
            # Check required fields
            required_fields = ['name', 'version', 'main']
            missing_fields = [field for field in required_fields if field not in package_data]
            
            if missing_fields:
                return {
                    "success": False,
                    "message": "Package.json validation failed",
                    "details": f"Missing required fields: {', '.join(missing_fields)}"
                }
            
            # Check for MCP dependencies
            deps = package_data.get('dependencies', {})
            if '@modelcontextprotocol/sdk' not in deps:
                return {
                    "success": False,
                    "message": "Package.json validation failed",
                    "details": "Missing required MCP SDK dependency"
                }
            
            return {
                "success": True,
                "message": "Package.json validation passed",
                "details": "All required fields and dependencies present"
            }
            
        except json.JSONDecodeError as e:
            return {
                "success": False,
                "message": "Package.json validation failed",
                "details": f"Invalid JSON: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "message": "Package.json test error",
                "details": str(e)
            }

    async def _test_mcp_compliance(self, server_code: str) -> Dict[str, Any]:
        """Test MCP protocol compliance."""
        try:
            # Check for required MCP patterns (support both ES6 imports and CommonJS)
            required_patterns = [
                r'(?:from\s+[\'"]@modelcontextprotocol/sdk[\'"]|import\s+.*from\s+[\'"]@modelcontextprotocol/sdk|require\s*\(\s*[\'"]@modelcontextprotocol/sdk)',
                r'Server\s*\(',
                r'(?:\.setRequestHandler\s*\(|server\.setRequestHandler)',
                r'ListToolsRequestSchema',
                r'CallToolRequestSchema'
            ]
            
            missing_patterns = []
            for pattern in required_patterns:
                if not re.search(pattern, server_code):
                    missing_patterns.append(pattern)
            
            if missing_patterns:
                return {
                    "success": False,
                    "message": "MCP compliance check failed",
                    "details": f"Missing required MCP patterns: {', '.join(missing_patterns)}"
                }
            
            return {
                "success": True,
                "message": "MCP compliance check passed",
                "details": "All required MCP patterns found"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": "MCP compliance test error",
                "details": str(e)
            }

    async def _test_dependencies(self, temp_dir: str) -> Dict[str, Any]:
        """Test if dependencies can be installed."""
        try:
            # Try to install dependencies
            result = subprocess.run(
                ['npm', 'install', '--dry-run'],
                cwd=temp_dir,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "message": "Dependencies check passed",
                    "details": "All dependencies can be installed"
                }
            else:
                return {
                    "success": False,
                    "message": "Dependencies check failed",
                    "details": result.stderr
                }

        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "message": "Dependencies check timeout",
                "details": "Dependency installation took too long"
            }
        except Exception as e:
            return {
                "success": False,
                "message": "Dependencies test error",
                "details": str(e)
            }

    async def _test_functional_requirements(self, server_code: str, user_prompt: str) -> Dict[str, Any]:
        """Test if the generated code meets the functional requirements from user prompt."""
        try:
            # Use AI to analyze if the code meets the requirements
            if not self.anthropic_client:
                return {
                    "success": False,
                    "message": "Functional validation skipped",
                    "details": "AI client not available"
                }

            analysis_prompt = f"""
Analyze this MCP server code against the user requirements and determine if it meets the functional specifications.

USER REQUIREMENTS:
{user_prompt}

GENERATED CODE:
{server_code[:3000]}...

Please evaluate:
1. Does the code implement the requested functionality?
2. Are all required tools/methods present?
3. Does it handle the specified use cases?
4. Are there any missing critical features?

Respond with:
MEETS_REQUIREMENTS: [YES/NO]
MISSING_FEATURES: [List any missing features]
IMPLEMENTATION_QUALITY: [GOOD/FAIR/POOR]
DETAILS: [Specific analysis]
"""

            response = await asyncio.to_thread(
                self.anthropic_client.messages.create,
                model='claude-3-5-sonnet-20241022',
                max_tokens=1000,
                temperature=0.1,
                messages=[{"role": "user", "content": analysis_prompt}]
            )

            ai_response = response.content[0].text if response.content else ""

            # Parse AI response
            meets_requirements = "YES" in ai_response.split("MEETS_REQUIREMENTS:")[1].split("\n")[0] if "MEETS_REQUIREMENTS:" in ai_response else False

            return {
                "success": meets_requirements,
                "message": "Functional requirements validation",
                "details": ai_response
            }

        except Exception as e:
            return {
                "success": False,
                "message": "Functional validation error",
                "details": str(e)
            }

    async def _test_repository_context(self, server_code: str, analysis_id: int) -> Dict[str, Any]:
        """Test if the generated code is relevant to the repository context."""
        try:
            # Get repository analysis
            db = SessionLocal()
            try:
                analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
                if not analysis:
                    return {
                        "success": False,
                        "message": "Repository context validation failed",
                        "details": "Analysis not found"
                    }

                repo_context = {
                    "repo_url": analysis.repo_url,
                    "repo_name": analysis.repo_name,
                    "analysis_results": analysis.analysis_results
                }

                # Check if code uses relevant technologies from the repo
                relevant_techs = []
                if analysis.analysis_results:
                    # Extract technologies from analysis
                    analysis_str = json.dumps(analysis.analysis_results)
                    if "playwright" in analysis_str.lower():
                        relevant_techs.append("playwright")
                    if "react" in analysis_str.lower():
                        relevant_techs.append("react")
                    if "node" in analysis_str.lower():
                        relevant_techs.append("nodejs")
                    if "python" in analysis_str.lower():
                        relevant_techs.append("python")

                # Check if generated code mentions relevant technologies
                code_lower = server_code.lower()
                used_techs = [tech for tech in relevant_techs if tech in code_lower]

                if used_techs:
                    return {
                        "success": True,
                        "message": "Repository context validation passed",
                        "details": f"Code uses relevant technologies: {', '.join(used_techs)}"
                    }
                else:
                    return {
                        "success": True,  # Not a failure, just no specific tech alignment
                        "message": "Repository context validation completed",
                        "details": "Code is generic and doesn't use repository-specific technologies"
                    }

            finally:
                db.close()

        except Exception as e:
            return {
                "success": False,
                "message": "Repository context validation error",
                "details": str(e)
            }

    async def _test_security_practices(self, server_code: str) -> Dict[str, Any]:
        """Test security practices in the generated code."""
        try:
            security_issues = []
            security_good_practices = []

            # Check for common security issues
            if "eval(" in server_code:
                security_issues.append("Uses eval() which is dangerous")

            if "process.env" not in server_code and "config" not in server_code.lower():
                security_issues.append("No environment variable usage detected")

            if "try {" in server_code and "catch" in server_code:
                security_good_practices.append("Proper error handling with try-catch")

            if "validate" in server_code.lower() or "sanitize" in server_code.lower():
                security_good_practices.append("Input validation/sanitization detected")

            if "rate" in server_code.lower() and "limit" in server_code.lower():
                security_good_practices.append("Rate limiting implementation detected")

            if "log" in server_code.lower():
                security_good_practices.append("Logging implementation detected")

            # Determine overall security score
            security_score = len(security_good_practices) - len(security_issues)

            return {
                "success": security_score >= 0,
                "message": f"Security validation ({'PASS' if security_score >= 0 else 'NEEDS_IMPROVEMENT'})",
                "details": f"Good practices: {security_good_practices}. Issues: {security_issues}"
            }

        except Exception as e:
            return {
                "success": False,
                "message": "Security validation error",
                "details": str(e)
            }

    async def _test_performance_practices(self, server_code: str) -> Dict[str, Any]:
        """Test performance and best practices in the generated code."""
        try:
            performance_issues = []
            performance_good_practices = []

            # Check for performance best practices
            if "async" in server_code and "await" in server_code:
                performance_good_practices.append("Uses async/await for non-blocking operations")

            if "pool" in server_code.lower() or "connection" in server_code.lower():
                performance_good_practices.append("Connection pooling or management detected")

            if "cache" in server_code.lower():
                performance_good_practices.append("Caching implementation detected")

            if "timeout" in server_code.lower():
                performance_good_practices.append("Timeout handling detected")

            # Check for potential performance issues
            if server_code.count("new ") > 10:
                performance_issues.append("Many object instantiations detected")

            if "setInterval" in server_code and "clearInterval" not in server_code:
                performance_issues.append("setInterval without clearInterval detected")

            # Check code structure
            if "class " in server_code or "function " in server_code:
                performance_good_practices.append("Well-structured code with functions/classes")

            performance_score = len(performance_good_practices) - len(performance_issues)

            return {
                "success": performance_score >= 1,
                "message": f"Performance validation ({'GOOD' if performance_score >= 2 else 'ACCEPTABLE' if performance_score >= 1 else 'NEEDS_IMPROVEMENT'})",
                "details": f"Good practices: {performance_good_practices}. Issues: {performance_issues}"
            }

        except Exception as e:
            return {
                "success": False,
                "message": "Performance validation error",
                "details": str(e)
            }

    async def _test_tool_functionality(self, server_code: str) -> Dict[str, Any]:
        """Test if the MCP tools are properly implemented."""
        try:
            tool_issues = []
            tool_features = []

            # Check for proper tool implementation
            if "addTool" in server_code or "setRequestHandler" in server_code:
                tool_features.append("Proper MCP tool registration")
            else:
                tool_issues.append("No MCP tool registration found")

            # Check for input schema validation
            if "inputSchema" in server_code:
                tool_features.append("Input schema validation")
            else:
                tool_issues.append("No input schema validation")

            # Check for proper error handling in tools
            if "throw" in server_code or "Error" in server_code:
                tool_features.append("Error handling in tools")

            # Check for tool descriptions
            if "description" in server_code:
                tool_features.append("Tool descriptions provided")

            # Count number of tools
            tool_count = server_code.count("addTool") + server_code.count("name:")
            if tool_count >= 3:
                tool_features.append(f"Multiple tools implemented ({tool_count} detected)")
            elif tool_count >= 1:
                tool_features.append(f"Basic tools implemented ({tool_count} detected)")
            else:
                tool_issues.append("No tools detected")

            tool_score = len(tool_features) - len(tool_issues)

            return {
                "success": tool_score >= 2,
                "message": f"Tool functionality validation ({'EXCELLENT' if tool_score >= 4 else 'GOOD' if tool_score >= 2 else 'NEEDS_IMPROVEMENT'})",
                "details": f"Features: {tool_features}. Issues: {tool_issues}"
            }

        except Exception as e:
            return {
                "success": False,
                "message": "Tool functionality validation error",
                "details": str(e)
            }

    def _generate_example_usage(self, server: Dict[str, Any]) -> str:
        """Generate example usage documentation."""
        return f"""# Example Usage for {server['name']}

## Installation

1. Extract the zip file
2. Run `npm install` to install dependencies
3. Start the server with `node index.js`

## Available Tools

{chr(10).join([f"- **{tool['name']}**: {tool['description']}" for tool in server.get('tools', [])])}

## Testing

Run the test file:
```bash
node test.js
```

## Integration

Add this server to your MCP client configuration:

```json
{{
  "mcpServers": {{
    "{server['name'].lower().replace(' ', '-')}": {{
      "command": "node",
      "args": ["path/to/index.js"]
    }}
  }}
}}
```
"""

    def _generate_install_script(self, server_name: str) -> str:
        """Generate installation script."""
        return f"""#!/bin/bash

# Installation script for {server_name}

echo "Installing {server_name}..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
npm install

# Install Playwright browsers if needed
if grep -q "playwright" package.json; then
    echo "Installing Playwright browsers..."
    npx playwright install
fi

if [ $? -eq 0 ]; then
    echo "✅ Installation completed successfully!"
    echo "Run 'npm start' to start the MCP server."
    echo "Run 'npm test' to run tests."
else
    echo "❌ Installation failed. Please check the error messages above."
    exit 1
fi
"""

    def _generate_dockerfile(self) -> str:
        """Generate Dockerfile."""
        return """FROM node:18-alpine

# Install dependencies for Playwright
RUN apk add --no-cache \\
    chromium \\
    nss \\
    freetype \\
    freetype-dev \\
    harfbuzz \\
    ca-certificates \\
    ttf-freefont

# Set Playwright to use installed Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \\
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S mcp -u 1001

# Change ownership
RUN chown -R mcp:nodejs /app
USER mcp

EXPOSE 3000

CMD ["npm", "start"]
"""

    def _generate_docker_compose(self, server_name: str) -> str:
        """Generate docker-compose.yml."""
        return f"""version: '3.8'

services:
  {server_name}:
    build: .
    container_name: {server_name}
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  logs:
"""

    def _generate_gitignore(self) -> str:
        """Generate .gitignore file."""
        return """# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Logs
logs
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Build outputs
dist/
build/

# Screenshots and downloads
screenshots/
downloads/
"""

    def _generate_test_file(self, server_name: str, tools: List[Dict[str, Any]]) -> str:
        """Generate comprehensive test file."""
        tool_tests = ""
        for tool in tools:
            tool_name = tool.get('name', 'unknown')
            tool_tests += f"""
    // Test {tool_name} tool
    test('{tool_name} tool should be available', async () => {{
        const tools = await server.listTools();
        expect(tools.some(t => t.name === '{tool_name}')).toBe(true);
    }});
"""

        return f"""const {{ Server }} = require('@modelcontextprotocol/sdk');
const {{ {server_name.replace('-', '_')} }} = require('../index');

describe('{server_name} MCP Server', () => {{
    let server;

    beforeAll(async () => {{
        server = new Server('{server_name}');
        await server.start();
    }});

    afterAll(async () => {{
        await server.stop();
    }});

    test('server should start successfully', () => {{
        expect(server).toBeDefined();
    }});

    test('server should list available tools', async () => {{
        const tools = await server.listTools();
        expect(Array.isArray(tools)).toBe(true);
        expect(tools.length).toBeGreaterThan(0);
    }});
{tool_tests}
}});
"""

    def _generate_usage_example(self, server_name: str, tools: List[Dict[str, Any]]) -> str:
        """Generate usage examples."""
        examples = ""
        for tool in tools[:3]:  # Show examples for first 3 tools
            tool_name = tool.get('name', 'unknown')
            examples += f"""
// Example: Using {tool_name}
async function example_{tool_name}() {{
    try {{
        const result = await server.callTool('{tool_name}', {{
            // Add appropriate parameters here
        }});
        console.log('Result:', result);
    }} catch (error) {{
        console.error('Error:', error);
    }}
}}
"""

        return f"""// Usage examples for {server_name} MCP Server

const {{ Server }} = require('@modelcontextprotocol/sdk');

async function main() {{
    const server = new Server('{server_name}');

    try {{
        await server.start();
        console.log('Server started successfully');

        // List available tools
        const tools = await server.listTools();
        console.log('Available tools:', tools.map(t => t.name));
{examples}
    }} catch (error) {{
        console.error('Error:', error);
    }} finally {{
        await server.stop();
    }}
}}

main();
"""
