"""
MCP Chat Service with full repository context and AI capabilities
"""
import os
import asyncio
from typing import List, Dict, Any, Optional
import anthropic
from app.services.github_service import GitHubService
from app.tasks.mcp_analysis_task import get_mcp_analysis_result
from app.database import SessionLocal
from app.models import RepoAnalysis

# Try to import Tavily<PERSON><PERSON>, but make it optional
try:
    from app.services.tavily_client import Tavily<PERSON>lient
    TAVILY_AVAILABLE = True
except ImportError:
    print("Warning: TavilyClient not available for MCP chat.")
    TavilyClient = None
    TAVILY_AVAILABLE = False


class MCPChatService:
    def __init__(self):
        # Initialize Anthropic client
        anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')
        if anthropic_api_key:
            self.anthropic_client = anthropic.Anthropic(api_key=anthropic_api_key)
        else:
            self.anthropic_client = None
            print("Warning: ANTHROPIC_API_KEY not set. MCP chat will be limited.")
        
        # Initialize Tavily client for web search
        tavily_api_key = os.getenv('TAVILY_API_KEY')
        if tavily_api_key and TAVILY_AVAILABLE:
            self.tavily_client = TavilyClient()
        else:
            self.tavily_client = None
        
        self.github_service = GitHubService()

    async def get_full_context(self, analysis_id: int) -> str:
        """Get comprehensive context about the repository and analysis."""
        try:
            db = SessionLocal()
            
            # Get repository analysis
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            if not analysis:
                return "Repository analysis not found."
            
            # Get MCP analysis results
            mcp_analysis = get_mcp_analysis_result(analysis_id)
            
            # Build comprehensive context
            context_parts = [
                f"Repository: {analysis.repo_url}",
                f"Name: {analysis.repo_name}",
                f"Owner: {analysis.repo_owner}",
                f"Status: {analysis.status}",
            ]
            
            # Add repository analysis results if available
            if analysis.analysis_results:
                context_parts.append("\n## Repository Analysis:")
                if isinstance(analysis.analysis_results, dict):
                    # Extract key information from analysis results
                    if 'languages' in analysis.analysis_results:
                        context_parts.append(f"Languages: {analysis.analysis_results['languages']}")
                    if 'dependencies' in analysis.analysis_results:
                        dep_count = len(analysis.analysis_results.get('dependencies', []))
                        context_parts.append(f"Dependencies: {dep_count} found")
                    if 'api_endpoints' in analysis.analysis_results:
                        endpoint_count = len(analysis.analysis_results.get('api_endpoints', []))
                        context_parts.append(f"API Endpoints: {endpoint_count} found")
            
            # Add MCP analysis results if available
            if mcp_analysis:
                context_parts.append("\n## MCP Analysis:")

                # Handle both object and dict formats
                if hasattr(mcp_analysis, 'executive_summary'):
                    context_parts.append(f"Executive Summary: {mcp_analysis.executive_summary}")
                    capability_matrix = getattr(mcp_analysis, 'capability_matrix', [])
                    existing_servers = getattr(mcp_analysis, 'existing_mcp_servers', [])
                    gap_analysis = getattr(mcp_analysis, 'gap_analysis', '')
                elif isinstance(mcp_analysis, dict):
                    context_parts.append(f"Executive Summary: {mcp_analysis.get('executive_summary', 'No summary available')}")
                    capability_matrix = mcp_analysis.get('capability_matrix', [])
                    existing_servers = mcp_analysis.get('existing_mcp_servers', [])
                    gap_analysis = mcp_analysis.get('gap_analysis', '')
                else:
                    context_parts.append("MCP Analysis available but format not recognized")
                    capability_matrix = []
                    existing_servers = []
                    gap_analysis = ''

                context_parts.append(f"Capabilities Found: {len(capability_matrix)}")
                context_parts.append(f"Existing MCP Servers: {len(existing_servers)}")
                context_parts.append(f"Gap Analysis: {gap_analysis}")

                if capability_matrix:
                    context_parts.append("\n### Capability Matrix:")
                    for cap in capability_matrix[:5]:  # Limit to top 5
                        if isinstance(cap, dict):
                            context_parts.append(f"- {cap.get('capability', 'Unknown')} ({cap.get('underlying_tech', 'Unknown')}) -> {cap.get('candidate_tool_name', 'Unknown')}")
                        else:
                            context_parts.append(f"- {getattr(cap, 'capability', 'Unknown')} ({getattr(cap, 'underlying_tech', 'Unknown')}) -> {getattr(cap, 'candidate_tool_name', 'Unknown')}")

                if existing_servers:
                    context_parts.append("\n### Existing MCP Servers:")
                    for server in existing_servers[:3]:  # Limit to top 3
                        if isinstance(server, dict):
                            context_parts.append(f"- {server.get('server_name', 'Unknown')}: {server.get('overlapping_tools', 'Unknown')}")
                        else:
                            context_parts.append(f"- {getattr(server, 'server_name', 'Unknown')}: {getattr(server, 'overlapping_tools', 'Unknown')}")
            
            db.close()
            return "\n".join(context_parts)
            
        except Exception as e:
            print(f"Error getting context: {e}")
            return f"Error retrieving context: {str(e)}"

    async def search_web_for_context(self, query: str) -> str:
        """Search the web for additional context using Tavily."""
        if not self.tavily_client:
            return "Web search not available (Tavily not configured)"
        
        try:
            results = await self.tavily_client.search(query, max_results=3)
            if results and 'results' in results:
                search_context = []
                for result in results['results'][:3]:
                    title = result.get('title', 'Unknown')
                    url = result.get('url', '')
                    content = result.get('content', '')[:200] + "..." if result.get('content') else ''
                    search_context.append(f"**{title}**\n{content}\nSource: {url}")
                return "\n\n".join(search_context)
            return "No relevant web results found"
        except Exception as e:
            print(f"Web search error: {e}")
            return f"Web search failed: {str(e)}"

    async def chat_with_context(self, analysis_id: int, user_message: str, conversation_history: List[Dict[str, str]] = None) -> str:
        """Chat with full repository and MCP context."""
        if not self.anthropic_client:
            return "Sorry, AI chat is not available (Anthropic API key not configured)."
        
        try:
            # Get full context
            context = await self.get_full_context(analysis_id)
            
            # Check if user is asking for web search
            web_search_keywords = ['search', 'find', 'look up', 'research', 'latest', 'current']
            needs_web_search = any(keyword in user_message.lower() for keyword in web_search_keywords)
            
            web_context = ""
            if needs_web_search and 'mcp' in user_message.lower():
                # Search for MCP-related information
                search_query = f"Model Context Protocol MCP {user_message}"
                web_context = await self.search_web_for_context(search_query)
            
            # Build conversation messages
            messages = []
            
            # Add conversation history if provided
            if conversation_history:
                for msg in conversation_history[-10:]:  # Keep last 10 messages for context
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
            
            # Add current user message
            messages.append({
                "role": "user",
                "content": user_message
            })
            
            # Create system prompt with full context
            system_prompt = f"""You are an expert MCP (Model Context Protocol) assistant with deep knowledge of repository analysis and MCP server development.

## Repository Context:
{context}

## Web Search Results (if applicable):
{web_context if web_context else "No web search performed"}

## IMPORTANT: You have FULL ACCESS to the repository context above, including:
- Repository analysis data with technologies, dependencies, and business logic
- MCP analysis results with capability matrix and server specifications
- Existing MCP server recommendations
- Gap analysis and implementation guidance

## Your Role:
- Have natural conversations about MCP opportunities and planning
- Provide strategic guidance and help users understand what's possible
- Explain how MCP servers would leverage the repository's existing capabilities
- Help users think through requirements, architecture, and implementation strategy
- Answer questions about MCP concepts, benefits, and best practices
- **FOCUS ON PLANNING AND GUIDANCE** rather than code generation
- **ALWAYS reference the specific repository context** when providing answers
- **NO CODE EXAMPLES** unless specifically requested for understanding concepts

## Simple Button Protocol:
**ONLY show the Generate MCP Server button when the user explicitly wants to build something.**

### **CRITICAL: When to Show Generate MCP Server Button:**
**IMMEDIATELY show the button when user says ANY of these - NO QUESTIONS, NO CLARIFICATIONS:**
- "build [server_name]"
- "create [server_name]"
- "generate [server_name]"
- "make [server_name]"
- "let's build [server_name]"

**REQUIRED Format (use EXACTLY this format):**
```
I'll help you build [SERVER_NAME] for your repository.

SHOW_MCP_BUTTONS: [SERVER_NAME]
REQUIREMENTS: [Brief description of what the server will do]
```

**MANDATORY EXAMPLES:**
- User: "Let's build a secure_sandbox MCP server" → IMMEDIATELY show button, NO questions
- User: "Generate a browser_automation MCP server" → IMMEDIATELY show button, NO questions
- User: "Create search_research MCP server" → IMMEDIATELY show button, NO questions

**ABSOLUTELY FORBIDDEN when user wants to build:**
- Asking clarifying questions
- Suggesting alternatives
- Questioning their choice
- Any conversation - just show the button immediately

### **For All Other Conversations:**
- Simply have a natural conversation
- Answer questions directly and clearly
- Provide planning guidance and strategic advice
- NO buttons at all - just conversation

## Guidelines:
- **ALWAYS use the repository context provided above** - you have full access to analysis data
- Have natural, helpful conversations about MCP opportunities and planning
- Reference the actual repository analysis and MCP analysis results in your responses
- **CONVERSATION ONLY** - NO code, pseudo-code, or technical implementation details
- Provide clear, strategic guidance about MCP server possibilities and benefits
- Help users understand the value and potential of different MCP approaches
- Explain concepts in plain language without technical jargon
- Ask clarifying questions to understand user needs better
- Use the web search results to provide current information about MCP ecosystem
- **ONLY show Generate MCP Server button when users explicitly want to build something**
- **ALWAYS be specific about what the MCP server will do for THEIR specific repository**
- **NEVER say you don't have access to repository context** - you have full access to all analysis data
- **KEEP CONVERSATIONS NATURAL**: No buttons unless user wants to build
- **ABSOLUTELY NO CODE EXAMPLES** - focus on concepts and planning only
- **NO TECHNICAL IMPLEMENTATION DETAILS** - save that for the playground
- **USE CLEAR, NON-TECHNICAL DESCRIPTIONS**: Explain functionality and benefits simply
- **FOCUS ON USER GOALS**: Understand what they want to achieve
- **PROVIDE STRATEGIC GUIDANCE**: Help with planning and decision-making

**CONVERSATION FOCUS:**
- Have natural, helpful conversations about MCP possibilities
- Focus on understanding user needs and explaining concepts
- Provide strategic guidance and planning advice
- Help users think through requirements and approach
- Explain benefits and value propositions clearly
- **NEVER include code, pseudo-code, or implementation details**
- **NO technical implementation discussions unless specifically asked**
- **NO class definitions, function signatures, or code examples**
- **FOCUS ON WHAT, WHY, and HOW (conceptually) - not implementation details**

Please provide a helpful, natural response focused on conversation and planning.

**SIMPLE BUTTON RULE:**

**ONLY when user explicitly wants to build/create/generate an MCP server**, use:
```
SHOW_MCP_BUTTONS: [SERVER_NAME]
REQUIREMENTS: [Brief description of what the server will do]
```

**For all other conversations**: Just have a natural conversation with NO buttons.

**ABSOLUTELY FORBIDDEN:**
- Any code examples, pseudo-code, or implementation details
- Function names, class names, or technical syntax
- Words like "implement", "function", "class", "method", "API endpoint"
- Technical architecture details or code structure discussions

**Focus on:**
- Understanding user needs and goals
- Explaining MCP concepts and possibilities in plain language
- Strategic guidance and planning advice
- Answering questions clearly without technical jargon
- Benefits and value propositions only"""

            # Make API call to Anthropic
            response = await asyncio.to_thread(
                self.anthropic_client.messages.create,
                model='claude-3-5-sonnet-20241022',
                max_tokens=2000,
                temperature=0.7,
                system=system_prompt,
                messages=messages
            )

            return response.content[0].text if response.content else "Sorry, I couldn't generate a response."

        except Exception as e:
            print(f"Chat error: {e}")
            return f"Sorry, there was an error processing your request: {str(e)}"

    async def get_mcp_suggestions(self, analysis_id: int, user_goal: str) -> str:
        """Get specific MCP suggestions based on user's goal."""
        context = await self.get_full_context(analysis_id)
        
        prompt = f"""Based on the repository analysis and the user's goal, provide specific MCP server suggestions.

Repository Context:
{context}

User's Goal: {user_goal}

Please provide:
1. Specific MCP tools that would help achieve this goal
2. Existing MCP servers that could be used
3. Custom MCP server recommendations
4. Implementation approach
5. Code examples if relevant

Be specific and actionable."""

        return await self.chat_with_context(analysis_id, prompt)
