"""
MCP Workflow Integration Service
Orchestrates the complete MCP workflow from analysis to playground
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from .unified_context_service import UnifiedContextService
from .enhanced_mcp_conversation_service import EnhancedMCPConversationService
from .requirements_driven_mcp_generator import RequirementsDrivenMCPGenerator
from .indexing_service import IndexingService

logger = logging.getLogger(__name__)

class WorkflowStage(Enum):
    REPOSITORY_ANALYSIS = "repository_analysis"
    CODE_INDEXING = "code_indexing"
    MCP_ANALYSIS = "mcp_analysis"
    MCP_CONVERSATION = "mcp_conversation"
    REQUIREMENT_VALIDATION = "requirement_validation"
    MCP_PLAYGROUND = "mcp_playground"

@dataclass
class WorkflowState:
    """Current state of the MCP workflow"""
    analysis_id: int
    current_stage: WorkflowStage
    completed_stages: List[WorkflowStage]
    available_actions: List[str]
    context_data: Dict[str, Any]
    validation_results: Optional[Dict[str, Any]]
    ready_for_generation: bool

@dataclass
class WorkflowTransition:
    """Represents a transition between workflow stages"""
    from_stage: WorkflowStage
    to_stage: WorkflowStage
    action: str
    requirements: List[str]
    validation_needed: bool

class MCPWorkflowIntegrationService:
    """Service that orchestrates the complete MCP workflow"""
    
    def __init__(self):
        self.context_service = UnifiedContextService()
        self.conversation_service = EnhancedMCPConversationService()
        self.requirements_generator = RequirementsDrivenMCPGenerator()
        self.indexing_service = IndexingService()
        
        # Define workflow transitions
        self.workflow_transitions = self._define_workflow_transitions()
    
    async def get_workflow_state(self, analysis_id: int) -> WorkflowState:
        """Get current workflow state for an analysis"""
        
        logger.info(f"Getting workflow state for analysis {analysis_id}")
        
        # Get repository context to determine current stage
        context = await self.context_service.get_complete_repository_context(analysis_id)
        
        # Determine current stage and completed stages
        current_stage, completed_stages = await self._determine_workflow_progress(analysis_id, context)
        
        # Get available actions for current stage
        available_actions = await self._get_available_actions(current_stage, context)
        
        # Check if ready for generation
        ready_for_generation = await self._check_generation_readiness(context)
        
        return WorkflowState(
            analysis_id=analysis_id,
            current_stage=current_stage,
            completed_stages=completed_stages,
            available_actions=available_actions,
            context_data=self._extract_context_summary(context),
            validation_results=context.validated_requirements,
            ready_for_generation=ready_for_generation
        )
    
    async def execute_workflow_action(
        self,
        analysis_id: int,
        action: str,
        parameters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a workflow action and transition to next stage if appropriate"""
        
        logger.info(f"Executing workflow action '{action}' for analysis {analysis_id}")
        
        # Get current workflow state
        workflow_state = await self.get_workflow_state(analysis_id)
        
        # Validate action is available
        if action not in workflow_state.available_actions:
            return {
                'success': False,
                'error': f"Action '{action}' not available in current stage '{workflow_state.current_stage.value}'",
                'available_actions': workflow_state.available_actions
            }
        
        # Execute the action
        result = await self._execute_action(action, analysis_id, parameters or {})
        
        # Check if we should transition to next stage
        if result.get('success'):
            transition_result = await self._check_and_execute_transition(
                workflow_state, action, result
            )
            result.update(transition_result)
        
        return result
    
    async def validate_requirements_in_workflow(
        self,
        analysis_id: int,
        user_requirements: str
    ) -> Dict[str, Any]:
        """Validate requirements within the workflow context"""
        
        # Get current workflow state
        workflow_state = await self.get_workflow_state(analysis_id)
        
        # Ensure we're at the right stage for validation
        if workflow_state.current_stage not in [WorkflowStage.MCP_CONVERSATION, WorkflowStage.REQUIREMENT_VALIDATION]:
            return {
                'success': False,
                'error': f"Requirement validation not available in stage '{workflow_state.current_stage.value}'",
                'current_stage': workflow_state.current_stage.value
            }
        
        # Perform validation using conversation service
        validation_result = await self.conversation_service.validate_and_prepare_for_playground(
            analysis_id, user_requirements
        )
        
        # Update workflow state if validation successful
        if validation_result['success']:
            await self._update_workflow_stage(analysis_id, WorkflowStage.REQUIREMENT_VALIDATION)
        
        return validation_result
    
    async def prepare_for_playground(
        self,
        analysis_id: int,
        validated_requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Prepare context for MCP playground generation"""
        
        # Get current workflow state
        workflow_state = await self.get_workflow_state(analysis_id)
        
        # Ensure requirements are validated
        if not workflow_state.validation_results and not validated_requirements:
            return {
                'success': False,
                'error': 'Requirements must be validated before proceeding to playground',
                'required_action': 'validate_requirements'
            }
        
        # Prepare playground context
        playground_context = await self.context_service.prepare_playground_context(
            analysis_id, validated_requirements or workflow_state.validation_results
        )
        
        # Update workflow stage
        await self._update_workflow_stage(analysis_id, WorkflowStage.MCP_PLAYGROUND)
        
        return {
            'success': True,
            'playground_context': playground_context,
            'workflow_stage': WorkflowStage.MCP_PLAYGROUND.value,
            'ready_for_generation': True
        }
    
    async def generate_mcp_from_workflow(
        self,
        analysis_id: int,
        target_language: str = "python"
    ) -> Dict[str, Any]:
        """Generate MCP server using workflow context"""
        
        # Get current workflow state
        workflow_state = await self.get_workflow_state(analysis_id)
        
        # Ensure we're ready for generation
        if not workflow_state.ready_for_generation:
            return {
                'success': False,
                'error': 'Workflow not ready for MCP generation',
                'current_stage': workflow_state.current_stage.value,
                'missing_requirements': self._get_missing_requirements_for_generation(workflow_state)
            }
        
        # Get validated requirements
        validated_requirements = workflow_state.validation_results
        if not validated_requirements:
            return {
                'success': False,
                'error': 'No validated requirements found',
                'required_action': 'validate_requirements'
            }
        
        # Generate MCP using requirements-driven generator
        generation_result = await self.requirements_generator.generate_requirements_based_mcp(
            analysis_id=analysis_id,
            user_requirements=validated_requirements['requirement_analysis']['original_text'],
            repository_context=workflow_state.context_data['repository_context'],
            analysis_results=workflow_state.context_data['analysis_results'],
            target_language=target_language
        )
        
        return generation_result
    
    def _define_workflow_transitions(self) -> List[WorkflowTransition]:
        """Define valid workflow transitions"""
        
        return [
            WorkflowTransition(
                from_stage=WorkflowStage.REPOSITORY_ANALYSIS,
                to_stage=WorkflowStage.CODE_INDEXING,
                action="start_indexing",
                requirements=["analysis_completed"],
                validation_needed=False
            ),
            WorkflowTransition(
                from_stage=WorkflowStage.CODE_INDEXING,
                to_stage=WorkflowStage.MCP_ANALYSIS,
                action="start_mcp_analysis",
                requirements=["indexing_completed"],
                validation_needed=False
            ),
            WorkflowTransition(
                from_stage=WorkflowStage.MCP_ANALYSIS,
                to_stage=WorkflowStage.MCP_CONVERSATION,
                action="start_conversation",
                requirements=["mcp_analysis_completed"],
                validation_needed=False
            ),
            WorkflowTransition(
                from_stage=WorkflowStage.MCP_CONVERSATION,
                to_stage=WorkflowStage.REQUIREMENT_VALIDATION,
                action="validate_requirements",
                requirements=["user_requirements_provided"],
                validation_needed=True
            ),
            WorkflowTransition(
                from_stage=WorkflowStage.REQUIREMENT_VALIDATION,
                to_stage=WorkflowStage.MCP_PLAYGROUND,
                action="prepare_playground",
                requirements=["requirements_validated", "feasibility_confirmed"],
                validation_needed=False
            )
        ]
    
    async def _determine_workflow_progress(
        self,
        analysis_id: int,
        context
    ) -> tuple[WorkflowStage, List[WorkflowStage]]:
        """Determine current stage and completed stages"""
        
        completed_stages = []
        
        # Check repository analysis
        if context.analysis_results:
            completed_stages.append(WorkflowStage.REPOSITORY_ANALYSIS)
        
        # Check code indexing
        if context.indexed_capabilities:
            completed_stages.append(WorkflowStage.CODE_INDEXING)
        
        # Check MCP analysis
        if context.mcp_suggestions:
            completed_stages.append(WorkflowStage.MCP_ANALYSIS)
        
        # Check conversation
        if context.conversation_context:
            completed_stages.append(WorkflowStage.MCP_CONVERSATION)
        
        # Check validation
        if context.validated_requirements:
            completed_stages.append(WorkflowStage.REQUIREMENT_VALIDATION)
            
            # Check if ready for playground
            if context.validated_requirements.get('is_feasible'):
                completed_stages.append(WorkflowStage.MCP_PLAYGROUND)
        
        # Determine current stage
        if not completed_stages:
            current_stage = WorkflowStage.REPOSITORY_ANALYSIS
        elif WorkflowStage.MCP_PLAYGROUND in completed_stages:
            current_stage = WorkflowStage.MCP_PLAYGROUND
        elif WorkflowStage.REQUIREMENT_VALIDATION in completed_stages:
            current_stage = WorkflowStage.MCP_PLAYGROUND
        elif WorkflowStage.MCP_CONVERSATION in completed_stages:
            current_stage = WorkflowStage.REQUIREMENT_VALIDATION
        elif WorkflowStage.MCP_ANALYSIS in completed_stages:
            current_stage = WorkflowStage.MCP_CONVERSATION
        elif WorkflowStage.CODE_INDEXING in completed_stages:
            current_stage = WorkflowStage.MCP_ANALYSIS
        else:
            current_stage = WorkflowStage.CODE_INDEXING
        
        return current_stage, completed_stages
    
    async def _get_available_actions(
        self,
        current_stage: WorkflowStage,
        context
    ) -> List[str]:
        """Get available actions for current stage"""
        
        actions = []
        
        if current_stage == WorkflowStage.REPOSITORY_ANALYSIS:
            actions = ["complete_analysis", "view_analysis_results"]
        
        elif current_stage == WorkflowStage.CODE_INDEXING:
            actions = ["start_indexing", "view_indexing_progress"]
        
        elif current_stage == WorkflowStage.MCP_ANALYSIS:
            actions = ["start_mcp_analysis", "view_mcp_suggestions"]
        
        elif current_stage == WorkflowStage.MCP_CONVERSATION:
            actions = ["chat_with_assistant", "validate_requirements", "explore_capabilities"]
        
        elif current_stage == WorkflowStage.REQUIREMENT_VALIDATION:
            actions = ["prepare_playground", "refine_requirements", "view_validation_results"]
        
        elif current_stage == WorkflowStage.MCP_PLAYGROUND:
            actions = ["generate_mcp_server", "test_mcp_server", "download_mcp_server"]
        
        return actions
    
    async def _check_generation_readiness(self, context) -> bool:
        """Check if workflow is ready for MCP generation"""
        
        # Need analysis results
        if not context.analysis_results:
            return False
        
        # Need indexed capabilities
        if not context.indexed_capabilities:
            return False
        
        # Need validated requirements
        if not context.validated_requirements:
            return False
        
        # Requirements must be feasible
        if not context.validated_requirements.get('is_feasible'):
            return False
        
        return True
    
    def _extract_context_summary(self, context) -> Dict[str, Any]:
        """Extract summary of context for workflow state"""
        
        return {
            'repository_context': {
                'name': context.repo_info.get('name'),
                'language': context.tech_stack.get('primary_language'),
                'capabilities_count': len(context.indexed_capabilities),
                'mcp_suggestions_count': len(context.mcp_suggestions)
            },
            'analysis_results': context.analysis_results,
            'workflow_stage': context.workflow_stage
        }
    
    async def _execute_action(
        self,
        action: str,
        analysis_id: int,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a specific workflow action"""
        
        if action == "chat_with_assistant":
            user_message = parameters.get('message', '')
            conversation_history = parameters.get('conversation_history', [])
            
            response = await self.conversation_service.chat_with_full_context(
                analysis_id, user_message, conversation_history
            )
            
            return {
                'success': True,
                'action': action,
                'response': response.__dict__
            }
        
        elif action == "validate_requirements":
            user_requirements = parameters.get('requirements', '')
            
            validation_result = await self.validate_requirements_in_workflow(
                analysis_id, user_requirements
            )
            
            return {
                'success': validation_result['success'],
                'action': action,
                'validation_result': validation_result
            }
        
        elif action == "prepare_playground":
            validated_requirements = parameters.get('validated_requirements', {})
            
            playground_result = await self.prepare_for_playground(
                analysis_id, validated_requirements
            )
            
            return {
                'success': playground_result['success'],
                'action': action,
                'playground_context': playground_result.get('playground_context')
            }
        
        elif action == "generate_mcp_server":
            target_language = parameters.get('target_language', 'python')
            
            generation_result = await self.generate_mcp_from_workflow(
                analysis_id, target_language
            )
            
            return {
                'success': generation_result['success'],
                'action': action,
                'generation_result': generation_result
            }
        
        else:
            return {
                'success': False,
                'error': f"Unknown action: {action}"
            }
    
    async def _check_and_execute_transition(
        self,
        workflow_state: WorkflowState,
        action: str,
        action_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Check if action should trigger a workflow transition"""
        
        # Find applicable transitions
        applicable_transitions = [
            t for t in self.workflow_transitions
            if t.from_stage == workflow_state.current_stage and t.action == action
        ]
        
        if not applicable_transitions:
            return {'transition': None}
        
        transition = applicable_transitions[0]
        
        # Check if requirements are met
        requirements_met = await self._check_transition_requirements(
            transition, workflow_state, action_result
        )
        
        if requirements_met:
            # Execute transition
            await self._update_workflow_stage(workflow_state.analysis_id, transition.to_stage)
            
            return {
                'transition': {
                    'from_stage': transition.from_stage.value,
                    'to_stage': transition.to_stage.value,
                    'action': action
                }
            }
        
        return {'transition': None}
    
    async def _check_transition_requirements(
        self,
        transition: WorkflowTransition,
        workflow_state: WorkflowState,
        action_result: Dict[str, Any]
    ) -> bool:
        """Check if transition requirements are met"""
        
        # Simple requirement checking for now
        # In production, this would be more sophisticated
        
        if "analysis_completed" in transition.requirements:
            return bool(workflow_state.context_data.get('analysis_results'))
        
        if "requirements_validated" in transition.requirements:
            return action_result.get('validation_result', {}).get('success', False)
        
        if "feasibility_confirmed" in transition.requirements:
            validation = action_result.get('validation_result', {})
            return validation.get('validation_result', {}).get('is_feasible', False)
        
        return True
    
    async def _update_workflow_stage(self, analysis_id: int, new_stage: WorkflowStage):
        """Update workflow stage in database or cache"""
        
        # This would update the workflow stage in persistent storage
        # For now, just log it
        logger.info(f"Workflow stage updated for analysis {analysis_id}: {new_stage.value}")
    
    def _get_missing_requirements_for_generation(self, workflow_state: WorkflowState) -> List[str]:
        """Get list of missing requirements for MCP generation"""
        
        missing = []
        
        if not workflow_state.context_data.get('analysis_results'):
            missing.append("Repository analysis not completed")
        
        if not workflow_state.context_data.get('repository_context', {}).get('capabilities_count'):
            missing.append("Code indexing not completed")
        
        if not workflow_state.validation_results:
            missing.append("Requirements not validated")
        
        if workflow_state.validation_results and not workflow_state.validation_results.get('is_feasible'):
            missing.append("Requirements not feasible with current repository")
        
        return missing
