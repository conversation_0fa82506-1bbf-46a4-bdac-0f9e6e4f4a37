"""
Unified Vector Service

Single vector storage service that uses either Weaviate OR Redis, not both.
Eliminates the data retrieval problems of hybrid approaches.
"""

import logging
from typing import Dict, List, Any, Optional
from ..config import settings
from .redis_vector_service import RedisVectorService
from .weaviate_vector_service import WeaviateVectorService

logger = logging.getLogger(__name__)


class UnifiedVectorService:
    """
    Unified vector service that uses ONE storage system consistently.
    Eliminates hybrid complexity and data retrieval issues.
    """
    
    def __init__(self):
        self.vector_service = None
        self.storage_type = None
        self.mode = settings.vector_storage_mode
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize_service()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.vector_service:
            await self.vector_service.__aexit__(exc_type, exc_val, exc_tb)
    
    async def initialize_service(self):
        """Initialize the appropriate vector service based on configuration"""
        try:
            if self.mode == "weaviate_only":
                await self._initialize_weaviate()
            elif self.mode == "redis_only":
                await self._initialize_redis()
            elif self.mode == "hybrid":
                # Try Weaviate first, fallback to Redis if it fails
                try:
                    await self._initialize_weaviate()
                except Exception as e:
                    logger.warning(f"Weaviate failed, falling back to Redis: {e}")
                    await self._initialize_redis()
            else:
                raise ValueError(f"Invalid vector storage mode: {self.mode}")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize vector service: {e}")
            raise
    
    async def _initialize_weaviate(self):
        """Initialize Weaviate vector service"""
        self.vector_service = WeaviateVectorService()
        await self.vector_service.__aenter__()
        self.storage_type = "weaviate"
        logger.info("✅ Unified vector service: Using Weaviate (unlimited capacity)")
    
    async def _initialize_redis(self):
        """Initialize Redis vector service"""
        self.vector_service = RedisVectorService()
        await self.vector_service.__aenter__()
        self.storage_type = "redis"
        logger.info("✅ Unified vector service: Using Redis (limited capacity)")
    
    async def index_repository_code(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Index repository code using the configured vector service
        """
        if not self.vector_service:
            raise Exception("Vector service not initialized")
        
        try:
            logger.info(f"Indexing analysis {analysis_id} with {self.storage_type}")
            result = await self.vector_service.index_repository_code(analysis_id, repo_content)
            result["storage_type"] = self.storage_type
            result["unified_service"] = True
            return result
            
        except Exception as e:
            logger.error(f"Vector indexing failed for analysis {analysis_id}: {e}")
            raise Exception(f"Vector indexing failed: {e}")
    
    async def search_similar_code(self, query: str, analysis_id: Optional[int] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search for similar code using the configured vector service
        """
        if not self.vector_service:
            return []
        
        try:
            results = await self.vector_service.search_similar_code(analysis_id, query, limit)
            logger.debug(f"Found {len(results)} results using {self.storage_type}")
            return results
            
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []
    
    async def get_repository_stats(self, analysis_id: int) -> Dict[str, Any]:
        """Get statistics for indexed repository"""
        if not self.vector_service:
            return {"error": "Vector service not initialized"}
        
        try:
            if hasattr(self.vector_service, 'get_repository_stats'):
                stats = await self.vector_service.get_repository_stats(analysis_id)
            else:
                # Fallback stats
                stats = {
                    "analysis_id": analysis_id,
                    "storage_type": self.storage_type,
                    "status": "available"
                }
            
            stats["unified_service"] = True
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get repository stats: {e}")
            return {"error": str(e)}
    
    def get_storage_info(self) -> Dict[str, Any]:
        """Get information about the current storage configuration"""
        return {
            "storage_type": self.storage_type,
            "mode": self.mode,
            "service_class": type(self.vector_service).__name__ if self.vector_service else None,
            "unlimited_capacity": self.storage_type == "weaviate",
            "unified_service": True
        }
