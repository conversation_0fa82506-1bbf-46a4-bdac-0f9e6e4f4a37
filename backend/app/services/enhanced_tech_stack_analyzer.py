"""
Enhanced Technology Stack Analyzer
Provides comprehensive and accurate tech stack detection with confidence scoring
"""
import os
import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class TechStackDetection:
    """Represents a detected technology with confidence and evidence"""
    name: str
    category: str  # 'language', 'framework', 'library', 'tool', 'database'
    confidence: float  # 0.0 to 1.0
    evidence: List[str]  # Sources of detection
    version: Optional[str] = None
    file_locations: List[str] = None

@dataclass
class TechStackAnalysis:
    """Complete tech stack analysis with accuracy metrics"""
    primary_language: str
    languages: Dict[str, float]  # language -> confidence score
    frameworks: List[TechStackDetection]
    libraries: List[TechStackDetection]
    tools: List[TechStackDetection]
    databases: List[TechStackDetection]
    overall_confidence: float
    detection_methods: List[str]
    validation_status: str  # 'pending', 'validated', 'needs_correction'

class EnhancedTechStackAnalyzer:
    """Advanced tech stack analyzer with multiple detection methods and confidence scoring"""
    
    def __init__(self):
        self.language_patterns = {
            'python': {
                'extensions': ['.py', '.pyw', '.pyx'],
                'files': ['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'],
                'patterns': [r'#!/usr/bin/env python', r'from\s+\w+\s+import', r'import\s+\w+'],
                'confidence_boost': 0.9
            },
            'javascript': {
                'extensions': ['.js', '.mjs', '.cjs'],
                'files': ['package.json', 'package-lock.json', 'yarn.lock'],
                'patterns': [r'require\(', r'module\.exports', r'import\s+.*\s+from'],
                'confidence_boost': 0.9
            },
            'typescript': {
                'extensions': ['.ts', '.tsx'],
                'files': ['tsconfig.json', 'package.json'],
                'patterns': [r'interface\s+\w+', r'type\s+\w+\s*=', r'import.*from.*\.ts'],
                'confidence_boost': 0.95
            },
            'java': {
                'extensions': ['.java'],
                'files': ['pom.xml', 'build.gradle', 'gradle.properties'],
                'patterns': [r'public\s+class\s+\w+', r'package\s+[\w\.]+', r'import\s+java\.'],
                'confidence_boost': 0.9
            },
            'go': {
                'extensions': ['.go'],
                'files': ['go.mod', 'go.sum'],
                'patterns': [r'package\s+main', r'func\s+main\(\)', r'import\s+\('],
                'confidence_boost': 0.95
            },
            'rust': {
                'extensions': ['.rs'],
                'files': ['Cargo.toml', 'Cargo.lock'],
                'patterns': [r'fn\s+main\(\)', r'use\s+std::', r'extern\s+crate'],
                'confidence_boost': 0.95
            },
            'php': {
                'extensions': ['.php', '.phtml'],
                'files': ['composer.json', 'composer.lock'],
                'patterns': [r'<\?php', r'namespace\s+\w+', r'use\s+\w+\\'],
                'confidence_boost': 0.9
            },
            'ruby': {
                'extensions': ['.rb', '.rake'],
                'files': ['Gemfile', 'Gemfile.lock', 'Rakefile'],
                'patterns': [r'class\s+\w+', r'def\s+\w+', r'require\s+[\'"]'],
                'confidence_boost': 0.9
            },
            'csharp': {
                'extensions': ['.cs', '.csx'],
                'files': ['*.csproj', '*.sln', 'packages.config'],
                'patterns': [r'using\s+System', r'namespace\s+\w+', r'public\s+class'],
                'confidence_boost': 0.9
            },
            'cpp': {
                'extensions': ['.cpp', '.cxx', '.cc', '.c++', '.hpp', '.hxx'],
                'files': ['CMakeLists.txt', 'Makefile'],
                'patterns': [r'#include\s*<', r'using\s+namespace', r'int\s+main\('],
                'confidence_boost': 0.85
            }
        }
        
        self.framework_patterns = {
            'react': {
                'indicators': ['react', '@types/react', 'react-dom'],
                'patterns': [r'import.*React', r'from\s+[\'"]react[\'"]', r'jsx?'],
                'category': 'frontend_framework'
            },
            'vue': {
                'indicators': ['vue', '@vue/cli', 'vuex'],
                'patterns': [r'<template>', r'Vue\.component', r'new Vue\('],
                'category': 'frontend_framework'
            },
            'angular': {
                'indicators': ['@angular/core', '@angular/cli', 'angular'],
                'patterns': [r'@Component', r'@Injectable', r'import.*@angular'],
                'category': 'frontend_framework'
            },
            'django': {
                'indicators': ['django', 'djangorestframework'],
                'patterns': [r'from\s+django', r'INSTALLED_APPS', r'urlpatterns'],
                'category': 'backend_framework'
            },
            'flask': {
                'indicators': ['flask', 'flask-sqlalchemy'],
                'patterns': [r'from\s+flask', r'@app\.route', r'Flask\(__name__\)'],
                'category': 'backend_framework'
            },
            'fastapi': {
                'indicators': ['fastapi', 'uvicorn'],
                'patterns': [r'from\s+fastapi', r'@app\.(get|post|put|delete)', r'FastAPI\('],
                'category': 'backend_framework'
            },
            'express': {
                'indicators': ['express', 'express-session'],
                'patterns': [r'require\([\'"]express[\'"]', r'app\.(get|post|put|delete)', r'express\(\)'],
                'category': 'backend_framework'
            },
            'spring': {
                'indicators': ['spring-boot', 'spring-core', 'springframework'],
                'patterns': [r'@SpringBootApplication', r'@RestController', r'@Service'],
                'category': 'backend_framework'
            },
            'gin': {
                'indicators': ['gin-gonic/gin'],
                'patterns': [r'gin\.Default\(\)', r'gin\.Engine', r'c\.JSON\('],
                'category': 'backend_framework'
            }
        }

    async def analyze_tech_stack(self, repo_path: str, repo_info: Dict[str, Any]) -> TechStackAnalysis:
        """Perform comprehensive tech stack analysis"""
        
        logger.info(f"Starting enhanced tech stack analysis for: {repo_info.get('name', 'unknown')}")
        
        # Multiple detection methods
        language_analysis = await self._analyze_languages(repo_path)
        framework_analysis = await self._analyze_frameworks(repo_path)
        dependency_analysis = await self._analyze_dependencies(repo_path)
        file_analysis = await self._analyze_file_structure(repo_path)
        
        # Combine and score results
        combined_analysis = self._combine_analyses(
            language_analysis, framework_analysis, dependency_analysis, file_analysis, repo_info
        )
        
        return combined_analysis

    async def _analyze_languages(self, repo_path: str) -> Dict[str, Any]:
        """Analyze programming languages with confidence scoring"""
        
        language_scores = defaultdict(float)
        language_evidence = defaultdict(list)
        file_counts = defaultdict(int)
        
        # Walk through repository files
        for root, dirs, files in os.walk(repo_path):
            # Skip hidden and build directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'target', 'build', 'dist']]
            
            for file in files:
                if file.startswith('.'):
                    continue
                    
                file_path = os.path.join(root, file)
                file_ext = Path(file).suffix.lower()
                
                # Check file extensions
                for lang, config in self.language_patterns.items():
                    if file_ext in config['extensions']:
                        file_counts[lang] += 1
                        language_scores[lang] += 0.3  # Base score for file extension
                        language_evidence[lang].append(f"File extension: {file_ext}")
                    
                    # Check specific files
                    if file in config['files'] or any(file.endswith(f) for f in config['files']):
                        language_scores[lang] += 0.5  # Higher score for specific files
                        language_evidence[lang].append(f"Config file: {file}")
                
                # Analyze file content for patterns
                if file_counts[lang] > 0:  # Only analyze if we already suspect this language
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read(1000)  # Read first 1KB
                            
                        for lang, config in self.language_patterns.items():
                            for pattern in config['patterns']:
                                if re.search(pattern, content):
                                    language_scores[lang] += 0.2
                                    language_evidence[lang].append(f"Code pattern: {pattern}")
                                    
                    except Exception:
                        continue
        
        # Normalize scores and apply confidence boosts
        max_files = max(file_counts.values()) if file_counts else 1
        for lang in language_scores:
            # File count contribution (0-0.4)
            file_score = (file_counts[lang] / max_files) * 0.4
            
            # Pattern score (already accumulated)
            pattern_score = min(language_scores[lang], 0.6)
            
            # Apply confidence boost
            confidence_boost = self.language_patterns[lang]['confidence_boost']
            
            language_scores[lang] = (file_score + pattern_score) * confidence_boost
        
        return {
            'scores': dict(language_scores),
            'evidence': dict(language_evidence),
            'file_counts': dict(file_counts)
        }

    async def _analyze_frameworks(self, repo_path: str) -> List[TechStackDetection]:
        """Detect frameworks and libraries"""
        
        detected_frameworks = []
        
        # Check dependency files
        dependency_files = ['package.json', 'requirements.txt', 'Cargo.toml', 'pom.xml', 'go.mod']
        
        for dep_file in dependency_files:
            file_path = os.path.join(repo_path, dep_file)
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for framework indicators
                    for framework, config in self.framework_patterns.items():
                        confidence = 0.0
                        evidence = []
                        
                        # Check dependency indicators
                        for indicator in config['indicators']:
                            if indicator in content:
                                confidence += 0.4
                                evidence.append(f"Dependency: {indicator} in {dep_file}")
                        
                        if confidence > 0:
                            detected_frameworks.append(TechStackDetection(
                                name=framework,
                                category=config['category'],
                                confidence=min(confidence, 1.0),
                                evidence=evidence,
                                file_locations=[dep_file]
                            ))
                            
                except Exception as e:
                    logger.warning(f"Failed to analyze {dep_file}: {e}")
        
        # Check code patterns
        for root, dirs, files in os.walk(repo_path):
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files[:10]:  # Limit to first 10 files for performance
                if any(file.endswith(ext) for ext in ['.py', '.js', '.ts', '.java', '.go']):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read(2000)  # Read first 2KB
                        
                        for framework, config in self.framework_patterns.items():
                            for pattern in config['patterns']:
                                if re.search(pattern, content):
                                    # Check if framework already detected
                                    existing = next((f for f in detected_frameworks if f.name == framework), None)
                                    if existing:
                                        existing.confidence = min(existing.confidence + 0.2, 1.0)
                                        existing.evidence.append(f"Code pattern: {pattern} in {file}")
                                        existing.file_locations.append(file)
                                    else:
                                        detected_frameworks.append(TechStackDetection(
                                            name=framework,
                                            category=config['category'],
                                            confidence=0.3,
                                            evidence=[f"Code pattern: {pattern} in {file}"],
                                            file_locations=[file]
                                        ))
                                    
                    except Exception:
                        continue
        
        return detected_frameworks

    async def _analyze_dependencies(self, repo_path: str) -> List[TechStackDetection]:
        """Analyze dependencies from various package files"""
        
        dependencies = []
        
        # Package.json (Node.js)
        package_json = os.path.join(repo_path, 'package.json')
        if os.path.exists(package_json):
            try:
                with open(package_json, 'r', encoding='utf-8') as f:
                    data = json.loads(f.read())
                
                all_deps = {**data.get('dependencies', {}), **data.get('devDependencies', {})}
                for name, version in all_deps.items():
                    dependencies.append(TechStackDetection(
                        name=name,
                        category='library',
                        confidence=0.9,
                        evidence=['package.json dependency'],
                        version=version,
                        file_locations=['package.json']
                    ))
                    
            except Exception as e:
                logger.warning(f"Failed to parse package.json: {e}")
        
        # Requirements.txt (Python)
        requirements_txt = os.path.join(repo_path, 'requirements.txt')
        if os.path.exists(requirements_txt):
            try:
                with open(requirements_txt, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # Parse package name and version
                        parts = re.split(r'[>=<!=]', line)
                        if parts:
                            name = parts[0].strip()
                            version = line.replace(name, '').strip() if len(parts) > 1 else None
                            
                            dependencies.append(TechStackDetection(
                                name=name,
                                category='library',
                                confidence=0.9,
                                evidence=['requirements.txt dependency'],
                                version=version,
                                file_locations=['requirements.txt']
                            ))
                            
            except Exception as e:
                logger.warning(f"Failed to parse requirements.txt: {e}")
        
        return dependencies

    async def _analyze_file_structure(self, repo_path: str) -> Dict[str, Any]:
        """Analyze file structure for additional insights"""
        
        structure_indicators = {
            'has_tests': False,
            'has_docs': False,
            'has_config': False,
            'has_docker': False,
            'has_ci': False
        }
        
        evidence = []
        
        for root, dirs, files in os.walk(repo_path):
            for file in files:
                file_lower = file.lower()
                
                # Test indicators
                if any(keyword in file_lower for keyword in ['test', 'spec', '__test__']):
                    structure_indicators['has_tests'] = True
                    evidence.append(f"Test file: {file}")
                
                # Documentation
                if any(keyword in file_lower for keyword in ['readme', 'doc', 'documentation']):
                    structure_indicators['has_docs'] = True
                    evidence.append(f"Documentation: {file}")
                
                # Configuration
                if any(keyword in file_lower for keyword in ['config', 'settings', '.env']):
                    structure_indicators['has_config'] = True
                    evidence.append(f"Config file: {file}")
                
                # Docker
                if 'dockerfile' in file_lower or 'docker-compose' in file_lower:
                    structure_indicators['has_docker'] = True
                    evidence.append(f"Docker file: {file}")
                
                # CI/CD
                if any(keyword in file_lower for keyword in ['.github', '.gitlab', 'jenkins', 'travis']):
                    structure_indicators['has_ci'] = True
                    evidence.append(f"CI/CD file: {file}")
        
        return {
            'indicators': structure_indicators,
            'evidence': evidence
        }

    def _combine_analyses(
        self, 
        language_analysis: Dict[str, Any],
        framework_analysis: List[TechStackDetection],
        dependency_analysis: List[TechStackDetection],
        file_analysis: Dict[str, Any],
        repo_info: Dict[str, Any]
    ) -> TechStackAnalysis:
        """Combine all analyses into final result"""
        
        # Determine primary language
        language_scores = language_analysis['scores']
        primary_language = max(language_scores, key=language_scores.get) if language_scores else 'Unknown'
        
        # Combine frameworks and libraries
        all_frameworks = framework_analysis
        all_libraries = dependency_analysis
        
        # Separate by category
        frameworks = [f for f in all_frameworks if 'framework' in f.category]
        libraries = [l for l in all_libraries if l.category == 'library']
        tools = []  # Can be expanded
        databases = []  # Can be expanded
        
        # Calculate overall confidence
        detection_methods = ['File Extension Analysis', 'Dependency Analysis', 'Code Pattern Analysis', 'File Structure Analysis']
        
        confidence_factors = []
        if language_scores:
            confidence_factors.append(max(language_scores.values()))
        if frameworks:
            confidence_factors.append(sum(f.confidence for f in frameworks) / len(frameworks))
        if libraries:
            confidence_factors.append(min(0.8, len(libraries) / 10))  # More libraries = higher confidence, capped at 0.8
        
        overall_confidence = sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.5
        
        return TechStackAnalysis(
            primary_language=primary_language,
            languages=language_scores,
            frameworks=frameworks,
            libraries=libraries,
            tools=tools,
            databases=databases,
            overall_confidence=overall_confidence,
            detection_methods=detection_methods,
            validation_status='pending'
        )
