"""
User-Driven MCP Suggestion Service
Generates MCP recommendations based on user prompts and indexed codebase
"""

import json
import logging
from typing import Dict, List, Any
import anthropic
from ..config import settings

logger = logging.getLogger(__name__)


class UserMCPSuggestionService:
    """Service for generating user-driven MCP suggestions"""
    
    def __init__(self):
        self.anthropic_client = anthropic.Anthropic(api_key=settings.ANTHROPIC_API_KEY) if settings.ANTHROPIC_API_KEY else None
        
    async def generate_user_driven_suggestions(
        self, 
        user_prompt: str, 
        analysis_data: Dict[str, Any], 
        focus_areas: List[str] = None
    ) -> Dict[str, Any]:
        """Generate MCP suggestions based on user prompt and codebase analysis"""
        
        logger.info(f"Generating user-driven suggestions for prompt: {user_prompt}")
        
        # Extract relevant data from analysis
        business_logic = analysis_data.get("ai_analysis", {}).get("comprehensive_analysis", {}).get("business_logic", {})
        code_implementations = business_logic.get("code_implementations", {})
        api_endpoints = analysis_data.get("api_endpoints", [])
        repo_info = analysis_data.get("repository_info", {})
        
        # Build context for AI
        context = self._build_context(repo_info, business_logic, code_implementations, api_endpoints, focus_areas)
        
        # Generate suggestions using AI
        if self.anthropic_client:
            suggestions = await self._generate_with_claude(user_prompt, context)
        else:
            # Fallback to basic suggestions if no AI available
            suggestions = self._generate_fallback_suggestions(user_prompt, context)
            
        return suggestions
    
    def _build_context(
        self, 
        repo_info: Dict[str, Any], 
        business_logic: Dict[str, Any], 
        code_implementations: Dict[str, Any], 
        api_endpoints: List[Dict[str, Any]], 
        focus_areas: List[str]
    ) -> Dict[str, Any]:
        """Build context for AI suggestion generation"""
        
        context = {
            "repository": {
                "name": repo_info.get("name", "Unknown"),
                "description": repo_info.get("description", ""),
                "language": repo_info.get("language", ""),
                "primary_domain": business_logic.get("primary_domain", ""),
                "business_purpose": business_logic.get("business_purpose", "")
            },
            "available_functions": [],
            "available_classes": [],
            "api_endpoints": [],
            "focus_areas": focus_areas or []
        }
        
        # Extract available functions
        for func in code_implementations.get("functions", []):
            context["available_functions"].append({
                "name": func.get("name", ""),
                "file": func.get("file", ""),
                "signature": func.get("signature", ""),
                "description": func.get("body_preview", "")[:200]  # First 200 chars
            })
        
        # Extract available classes
        for cls in code_implementations.get("classes", []):
            context["available_classes"].append({
                "name": cls.get("name", ""),
                "file": cls.get("file", ""),
                "methods": cls.get("methods", [])
            })
        
        # Extract API endpoints
        for endpoint in api_endpoints:
            context["api_endpoints"].append({
                "method": endpoint.get("method", ""),
                "path": endpoint.get("path", ""),
                "file": endpoint.get("file", "")
            })
        
        return context
    
    async def _generate_with_claude(self, user_prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate suggestions using Claude AI"""
        
        prompt = f"""
        You are an expert MCP (Model Context Protocol) consultant. A user wants specific MCP recommendations for their codebase.

        USER REQUEST: {user_prompt}

        CODEBASE CONTEXT:
        Repository: {context['repository']['name']}
        Description: {context['repository']['description']}
        Primary Language: {context['repository']['language']}
        Business Domain: {context['repository']['primary_domain']}
        Purpose: {context['repository']['business_purpose']}

        AVAILABLE CODE TO EXPOSE:
        Functions: {json.dumps(context['available_functions'][:10], indent=2)}
        Classes: {json.dumps(context['available_classes'][:5], indent=2)}
        API Endpoints: {json.dumps(context['api_endpoints'][:10], indent=2)}

        FOCUS AREAS: {', '.join(context['focus_areas']) if context['focus_areas'] else 'None specified'}

        TASK:
        Based on the user's request and the available codebase, recommend specific MCP servers that would address their needs.

        For each recommendation:
        1. Explain how it addresses the user's specific request
        2. Identify which existing code/functions it would expose
        3. Suggest whether to use existing MCP servers or build custom ones
        4. Provide implementation guidance

        Return as JSON:
        {{
            "understanding": "What the user is asking for",
            "recommendations": [
                {{
                    "server_name": "Descriptive name for the MCP server",
                    "addresses_request": "How this addresses the user's specific need",
                    "exposed_functionality": ["list", "of", "functions/endpoints", "to", "expose"],
                    "implementation_approach": "existing_server|custom_development|hybrid",
                    "existing_mcp_option": "Name of existing MCP server if applicable",
                    "marketplace_url": "URL if existing server available",
                    "custom_development_needed": "What needs to be built custom",
                    "estimated_effort": "1-week|2-3 weeks|4-6 weeks",
                    "business_value": "Specific value this provides",
                    "implementation_steps": ["step 1", "step 2", "step 3"],
                    "source_files": ["files", "to", "use"],
                    "source_functions": ["functions", "to", "expose"]
                }}
            ],
            "additional_considerations": ["other", "things", "to", "consider"],
            "next_steps": ["recommended", "next", "actions"]
        }}
        """
        
        try:
            response = await self.anthropic_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=4000,
                temperature=0.1,
                messages=[{"role": "user", "content": prompt}]
            )
            
            content = response.content[0].text.strip()
            
            # Extract JSON from response
            if '```json' in content:
                json_start = content.find('```json') + 7
                json_end = content.find('```', json_start)
                content = content[json_start:json_end].strip()
            elif '{' in content:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                content = content[json_start:json_end]
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Claude suggestion generation failed: {str(e)}")
            # NO FALLBACKS - Fail fast if AI analysis fails
            raise Exception(f"MCP suggestion generation failed: {str(e)}. Cannot generate suggestions without proper AI analysis.")
    
    def _generate_fallback_suggestions(self, user_prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """NO FALLBACKS - Fail fast if AI is not available"""
        raise Exception(f"Fallback suggestions not allowed. MCP generation requires proper AI analysis and cannot proceed with keyword-based fallbacks.")

        if any(word in prompt_lower for word in ['api', 'endpoint', 'rest', 'http']):
            recommendations.append({
                "server_name": "API Endpoint MCP Server",
                "addresses_request": "Exposes your API endpoints for AI interaction",
                "exposed_functionality": [ep["path"] for ep in context["api_endpoints"][:5]],
                "implementation_approach": "custom_development",
                "estimated_effort": "2-3 weeks",
                "business_value": "AI can interact with your API endpoints",
                "source_files": list(set([ep["file"] for ep in context["api_endpoints"]]))
            })

        # Only suggest database MCP if repository actually uses databases
        if any(word in prompt_lower for word in ['database', 'data', 'query', 'sql']):
            has_database = any(
                db_term in str(dep).lower() if isinstance(dep, str) else False
                for dep in dependencies
                for db_term in ["postgres", "mysql", "sqlite", "database", "db", "sql"]
            ) or any(
                db_term in description
                for db_term in ["database", "postgres", "mysql", "sqlite", "sql"]
            )

            if has_database:
                # Determine which database MCP to suggest based on actual usage
                if any("postgres" in str(dep).lower() for dep in dependencies) or "postgres" in description:
                    mcp_option = "postgres-mcp"
                    mcp_url = "https://github.com/modelcontextprotocol/servers/tree/main/src/postgres"
                elif any("mysql" in str(dep).lower() for dep in dependencies) or "mysql" in description:
                    mcp_option = "mysql-mcp"
                    mcp_url = "https://mcp.so/servers/mysql"
                elif any("sqlite" in str(dep).lower() for dep in dependencies) or "sqlite" in description:
                    mcp_option = "sqlite-mcp"
                    mcp_url = "https://github.com/modelcontextprotocol/servers/tree/main/src/sqlite"
                else:
                    # Generic database suggestion
                    mcp_option = "database-mcp"
                    mcp_url = "https://mcp.so/servers/database"

                recommendations.append({
                    "server_name": "Database Query MCP Server",
                    "addresses_request": "Provides AI access to your database operations",
                    "exposed_functionality": [f["name"] for f in context["available_functions"] if "query" in f["name"].lower() or "db" in f["name"].lower()][:5],
                    "implementation_approach": "hybrid",
                    "existing_mcp_option": mcp_option,
                    "marketplace_url": mcp_url,
                    "estimated_effort": "1-week",
                    "business_value": "AI can query and analyze your data"
                })

        # Only suggest file operations MCP if repository actually does file operations
        if any(word in prompt_lower for word in ['file', 'document', 'upload', 'download']):
            has_file_operations = any(
                file_term in str(dep).lower() if isinstance(dep, str) else False
                for dep in dependencies
                for file_term in ["file", "upload", "storage", "fs", "filesystem", "boto3", "s3"]
            ) or any(
                file_term in description
                for file_term in ["file", "upload", "storage", "document"]
            )

            if has_file_operations:
                recommendations.append({
                    "server_name": "File Operations MCP Server",
                    "addresses_request": "Enables AI to work with your file operations",
                    "exposed_functionality": [f["name"] for f in context["available_functions"] if any(word in f["name"].lower() for word in ["file", "upload", "download", "read", "write"])][:5],
                    "implementation_approach": "existing_server",
                    "existing_mcp_option": "filesystem-mcp",
                    "marketplace_url": "https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem",
                    "estimated_effort": "1-week",
                    "business_value": "AI can manage files and documents"
                })
        
        return {
            "understanding": f"User wants MCP recommendations related to: {user_prompt}",
            "recommendations": recommendations,
            "additional_considerations": ["Consider security implications", "Test with small datasets first"],
            "next_steps": ["Select preferred recommendations", "Review implementation approach", "Start with existing servers where possible"]
        }
