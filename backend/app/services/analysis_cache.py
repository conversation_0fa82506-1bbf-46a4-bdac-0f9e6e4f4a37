"""
Analysis caching service for improved performance
"""

import json
import hashlib
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import redis
import pickle
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    data: Any
    created_at: datetime
    expires_at: datetime
    cache_key: str
    size_bytes: int

class AnalysisCache:
    """High-performance caching service for repository analysis results"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        """Initialize cache with Redis backend"""
        try:
            self.redis_client = redis.from_url(redis_url, decode_responses=False)
            self.redis_client.ping()  # Test connection
            logger.info("✅ Redis cache initialized successfully")
        except Exception as e:
            logger.warning(f"⚠️ Redis not available, using in-memory cache: {str(e)}")
            self.redis_client = None
            self._memory_cache = {}
        
        # Cache configuration
        self.default_ttl = timedelta(hours=24)  # 24 hours default
        self.analysis_ttl = timedelta(hours=48)  # 48 hours for analysis results
        self.suggestions_ttl = timedelta(hours=12)  # 12 hours for suggestions
        self.code_extraction_ttl = timedelta(hours=6)  # 6 hours for code extraction
        
        # Cache key prefixes
        self.ANALYSIS_PREFIX = "analysis:"
        self.SUGGESTIONS_PREFIX = "suggestions:"
        self.CODE_EXTRACTION_PREFIX = "code_extract:"
        self.REPO_METADATA_PREFIX = "repo_meta:"
        
    def _generate_cache_key(self, prefix: str, identifier: str, **kwargs) -> str:
        """Generate a consistent cache key"""
        # Create a hash of the identifier and any additional parameters
        key_data = f"{identifier}:{json.dumps(kwargs, sort_keys=True)}"
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"{prefix}{key_hash}"
    
    def _serialize_data(self, data: Any) -> bytes:
        """Serialize data for storage"""
        return pickle.dumps(data)
    
    def _deserialize_data(self, data: bytes) -> Any:
        """Deserialize data from storage"""
        return pickle.loads(data)
    
    def set_cache(self, key: str, data: Any, ttl: Optional[timedelta] = None) -> bool:
        """Set cache entry with TTL"""
        try:
            ttl = ttl or self.default_ttl
            serialized_data = self._serialize_data(data)
            
            if self.redis_client:
                # Use Redis
                self.redis_client.setex(key, int(ttl.total_seconds()), serialized_data)
            else:
                # Use in-memory cache
                expires_at = datetime.utcnow() + ttl
                self._memory_cache[key] = CacheEntry(
                    data=data,
                    created_at=datetime.utcnow(),
                    expires_at=expires_at,
                    cache_key=key,
                    size_bytes=len(serialized_data)
                )
            
            logger.debug(f"✅ Cached data with key: {key}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to set cache for key {key}: {str(e)}")
            return False
    
    def get_cache(self, key: str) -> Optional[Any]:
        """Get cache entry"""
        try:
            if self.redis_client:
                # Use Redis
                data = self.redis_client.get(key)
                if data:
                    return self._deserialize_data(data)
            else:
                # Use in-memory cache
                entry = self._memory_cache.get(key)
                if entry and entry.expires_at > datetime.utcnow():
                    return entry.data
                elif entry:
                    # Remove expired entry
                    del self._memory_cache[key]
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get cache for key {key}: {str(e)}")
            return None
    
    def delete_cache(self, key: str) -> bool:
        """Delete cache entry"""
        try:
            if self.redis_client:
                self.redis_client.delete(key)
            else:
                self._memory_cache.pop(key, None)
            
            logger.debug(f"🗑️ Deleted cache key: {key}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to delete cache for key {key}: {str(e)}")
            return False
    
    def cache_analysis_result(self, repo_url: str, analysis_data: Dict[str, Any]) -> bool:
        """Cache repository analysis result"""
        key = self._generate_cache_key(self.ANALYSIS_PREFIX, repo_url)
        return self.set_cache(key, analysis_data, self.analysis_ttl)
    
    def get_cached_analysis(self, repo_url: str) -> Optional[Dict[str, Any]]:
        """Get cached repository analysis"""
        key = self._generate_cache_key(self.ANALYSIS_PREFIX, repo_url)
        return self.get_cache(key)
    
    def cache_mcp_suggestions(self, analysis_id: int, suggestions_data: Dict[str, Any]) -> bool:
        """Cache MCP suggestions"""
        key = self._generate_cache_key(self.SUGGESTIONS_PREFIX, str(analysis_id))
        return self.set_cache(key, suggestions_data, self.suggestions_ttl)
    
    def get_cached_suggestions(self, analysis_id: int) -> Optional[Dict[str, Any]]:
        """Get cached MCP suggestions"""
        key = self._generate_cache_key(self.SUGGESTIONS_PREFIX, str(analysis_id))
        return self.get_cache(key)
    
    def cache_code_extraction(self, repo_url: str, tool_names: List[str], extraction_data: Dict[str, Any]) -> bool:
        """Cache code extraction results"""
        key = self._generate_cache_key(
            self.CODE_EXTRACTION_PREFIX, 
            repo_url, 
            tools=sorted(tool_names)
        )
        return self.set_cache(key, extraction_data, self.code_extraction_ttl)
    
    def get_cached_code_extraction(self, repo_url: str, tool_names: List[str]) -> Optional[Dict[str, Any]]:
        """Get cached code extraction results"""
        key = self._generate_cache_key(
            self.CODE_EXTRACTION_PREFIX, 
            repo_url, 
            tools=sorted(tool_names)
        )
        return self.get_cache(key)
    
    def cache_repository_metadata(self, repo_url: str, metadata: Dict[str, Any]) -> bool:
        """Cache repository metadata (language, structure, etc.)"""
        key = self._generate_cache_key(self.REPO_METADATA_PREFIX, repo_url)
        return self.set_cache(key, metadata, self.analysis_ttl)
    
    def get_cached_repository_metadata(self, repo_url: str) -> Optional[Dict[str, Any]]:
        """Get cached repository metadata"""
        key = self._generate_cache_key(self.REPO_METADATA_PREFIX, repo_url)
        return self.get_cache(key)
    
    def invalidate_repository_cache(self, repo_url: str) -> bool:
        """Invalidate all cache entries for a repository"""
        try:
            # Generate all possible keys for this repository
            analysis_key = self._generate_cache_key(self.ANALYSIS_PREFIX, repo_url)
            metadata_key = self._generate_cache_key(self.REPO_METADATA_PREFIX, repo_url)
            
            # Delete the keys
            self.delete_cache(analysis_key)
            self.delete_cache(metadata_key)
            
            # For code extraction, we'd need to iterate through possible tool combinations
            # This is more complex, so for now we'll rely on TTL expiration
            
            logger.info(f"🗑️ Invalidated cache for repository: {repo_url}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to invalidate cache for repository {repo_url}: {str(e)}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            if self.redis_client:
                info = self.redis_client.info()
                return {
                    "backend": "redis",
                    "connected": True,
                    "used_memory": info.get("used_memory_human", "unknown"),
                    "total_keys": self.redis_client.dbsize(),
                    "hits": info.get("keyspace_hits", 0),
                    "misses": info.get("keyspace_misses", 0)
                }
            else:
                total_size = sum(entry.size_bytes for entry in self._memory_cache.values())
                active_entries = sum(1 for entry in self._memory_cache.values() 
                                   if entry.expires_at > datetime.utcnow())
                
                return {
                    "backend": "memory",
                    "connected": True,
                    "used_memory": f"{total_size / 1024 / 1024:.2f} MB",
                    "total_keys": len(self._memory_cache),
                    "active_keys": active_entries,
                    "expired_keys": len(self._memory_cache) - active_entries
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get cache stats: {str(e)}")
            return {"backend": "unknown", "connected": False, "error": str(e)}
    
    def cleanup_expired_entries(self) -> int:
        """Clean up expired entries (for in-memory cache)"""
        if self.redis_client:
            return 0  # Redis handles expiration automatically
        
        try:
            expired_keys = [
                key for key, entry in self._memory_cache.items()
                if entry.expires_at <= datetime.utcnow()
            ]
            
            for key in expired_keys:
                del self._memory_cache[key]
            
            logger.info(f"🧹 Cleaned up {len(expired_keys)} expired cache entries")
            return len(expired_keys)
            
        except Exception as e:
            logger.error(f"❌ Failed to cleanup expired entries: {str(e)}")
            return 0

# Global cache instance
analysis_cache = AnalysisCache()
