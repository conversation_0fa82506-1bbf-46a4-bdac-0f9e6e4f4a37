"""
Hybrid Vector Service

Manages both Redis and Weaviate vector storage with gradual migration support.
Provides unlimited indexing capabilities while maintaining backward compatibility.
"""

import logging
from typing import Dict, List, Any, Optional
from ..config import settings
from .redis_vector_service import RedisVectorService
from .weaviate_vector_service import WeaviateVectorService

logger = logging.getLogger(__name__)


class HybridVectorService:
    """
    Hybrid service that manages both Redis and Weaviate vector storage.
    Provides gradual migration path and feature flags for safe deployment.
    """
    
    def __init__(self):
        self.redis_service = None
        self.weaviate_service = None
        self.use_weaviate = settings.weaviate_enabled
        self.fallback_to_redis = True  # Always have Redis as fallback
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize_services()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.redis_service:
            await self.redis_service.__aexit__(exc_type, exc_val, exc_tb)
        if self.weaviate_service:
            await self.weaviate_service.__aexit__(exc_type, exc_val, exc_tb)
    
    async def initialize_services(self):
        """Initialize vector services based on configuration"""
        try:
            # Always initialize Redis (for backward compatibility and fallback)
            self.redis_service = RedisVectorService()
            await self.redis_service.__aenter__()
            logger.info("✅ Redis vector service initialized")
            
            # Initialize Weaviate if enabled
            if self.use_weaviate:
                try:
                    self.weaviate_service = WeaviateVectorService()
                    await self.weaviate_service.__aenter__()
                    logger.info("✅ Weaviate vector service initialized")
                except Exception as e:
                    logger.warning(f"⚠️ Weaviate initialization failed, falling back to Redis: {e}")
                    self.use_weaviate = False
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize vector services: {e}")
            raise
    
    async def index_repository_code(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Index repository code using the best available service
        
        Args:
            analysis_id: ID of the analysis
            repo_content: Repository content from analysis service
            
        Returns:
            Dict with indexing statistics
        """
        primary_result = None
        fallback_result = None
        
        # Try Weaviate first (unlimited capacity)
        if self.use_weaviate and self.weaviate_service:
            try:
                logger.info(f"Indexing analysis {analysis_id} with Weaviate (unlimited capacity)")
                primary_result = await self.weaviate_service.index_repository_code(analysis_id, repo_content)
                primary_result["primary_storage"] = "weaviate"
                
                # Skip Redis storage for large repositories (Weaviate handles unlimited capacity)
                if self.fallback_to_redis and self._is_suitable_for_redis(repo_content):
                    try:
                        # Only store metadata in Redis, not full vectors (save memory)
                        metadata_only = {
                            "analysis_id": analysis_id,
                            "files_count": repo_content.get("files_count", 0),
                            "storage_type": "weaviate_primary",
                            "indexed_at": primary_result.get("indexed_at")
                        }
                        await self.redis_service.store_analysis_metadata(analysis_id, metadata_only)
                        primary_result["fallback_storage"] = "redis_metadata_only"
                        logger.info(f"Stored metadata in Redis for analysis {analysis_id}")
                    except Exception as e:
                        logger.warning(f"Redis metadata storage failed: {e}")
                
                return primary_result
                
            except Exception as e:
                logger.error(f"Weaviate indexing failed for analysis {analysis_id}: {e}")
                # Fall through to Redis
        
        # Fallback to Redis
        if self.redis_service:
            try:
                logger.info(f"Indexing analysis {analysis_id} with Redis (fallback)")
                fallback_result = await self.redis_service.index_repository_code(analysis_id, repo_content)
                fallback_result["primary_storage"] = "redis"
                fallback_result["fallback_reason"] = "weaviate_unavailable" if self.use_weaviate else "weaviate_disabled"
                return fallback_result
                
            except Exception as e:
                logger.error(f"Redis indexing also failed for analysis {analysis_id}: {e}")
                raise Exception(f"All vector storage services failed: {e}")
        
        raise Exception("No vector storage services available")
    
    def _is_suitable_for_redis(self, repo_content: Dict[str, Any]) -> bool:
        """Check if repository content is suitable for Redis storage"""
        try:
            code_samples = repo_content.get("code_samples", {})
            
            # Check total size
            total_size = sum(len(str(content)) for content in code_samples.values())
            max_redis_size = 50 * 1024 * 1024  # 50MB limit for Redis
            
            # Check file count
            file_count = len(code_samples)
            max_redis_files = 500  # Reasonable limit for Redis
            
            return total_size < max_redis_size and file_count < max_redis_files
            
        except Exception:
            return False
    
    async def search_similar_code(self, query: str, analysis_id: Optional[int] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search for similar code using the best available service
        
        Args:
            query: Search query
            analysis_id: Optional analysis ID to filter results
            limit: Maximum number of results
            
        Returns:
            List of similar code chunks
        """
        # Try Weaviate first (better semantic search)
        if self.use_weaviate and self.weaviate_service:
            try:
                results = await self.weaviate_service.search_similar_code(query, analysis_id, limit)
                if results:
                    logger.debug(f"Found {len(results)} results using Weaviate")
                    return results
            except Exception as e:
                logger.warning(f"Weaviate search failed: {e}")
        
        # Fallback to Redis
        if self.redis_service:
            try:
                # Note: Redis vector service might not have semantic search
                # This would need to be implemented or use simple text search
                logger.debug(f"Falling back to Redis for search")
                # For now, return empty list as Redis service doesn't have search implemented
                return []
            except Exception as e:
                logger.error(f"Redis search failed: {e}")
        
        return []
    
    async def get_repository_stats(self, analysis_id: int) -> Dict[str, Any]:
        """Get statistics for indexed repository from available services"""
        stats = {"analysis_id": analysis_id}
        
        # Try Weaviate first
        if self.use_weaviate and self.weaviate_service:
            try:
                weaviate_stats = await self.weaviate_service.get_repository_stats(analysis_id)
                stats.update(weaviate_stats)
                stats["primary_source"] = "weaviate"
                return stats
            except Exception as e:
                logger.warning(f"Failed to get Weaviate stats: {e}")
        
        # Fallback to Redis
        if self.redis_service:
            try:
                # Redis service might not have stats method, implement basic version
                stats.update({
                    "total_chunks": 0,  # Would need to implement in Redis service
                    "languages": {},
                    "primary_source": "redis",
                    "storage_type": "redis"
                })
                return stats
            except Exception as e:
                logger.error(f"Failed to get Redis stats: {e}")
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all vector services"""
        health = {
            "timestamp": "2024-01-01T00:00:00Z",  # Would use actual timestamp
            "services": {}
        }
        
        # Check Redis
        if self.redis_service:
            try:
                # Redis service might not have health_check method
                health["services"]["redis"] = {
                    "status": "connected",
                    "ready": True,
                    "type": "fallback"
                }
            except Exception as e:
                health["services"]["redis"] = {
                    "status": "error",
                    "error": str(e),
                    "ready": False
                }
        
        # Check Weaviate
        if self.weaviate_service:
            try:
                weaviate_health = await self.weaviate_service.health_check()
                health["services"]["weaviate"] = weaviate_health
                health["services"]["weaviate"]["type"] = "primary"
            except Exception as e:
                health["services"]["weaviate"] = {
                    "status": "error",
                    "error": str(e),
                    "ready": False
                }
        
        # Overall status
        any_ready = any(
            service.get("ready", False) 
            for service in health["services"].values()
        )
        health["overall_status"] = "healthy" if any_ready else "unhealthy"
        health["primary_service"] = "weaviate" if self.use_weaviate else "redis"
        
        return health
    
    async def migrate_analysis_to_weaviate(self, analysis_id: int) -> Dict[str, Any]:
        """
        Migrate specific analysis from Redis to Weaviate
        
        Args:
            analysis_id: Analysis to migrate
            
        Returns:
            Migration result
        """
        if not self.use_weaviate or not self.weaviate_service:
            return {"success": False, "error": "Weaviate not available"}
        
        try:
            # This would need to be implemented to read from Redis and write to Weaviate
            # For now, return placeholder
            logger.info(f"Migration of analysis {analysis_id} to Weaviate would happen here")
            
            return {
                "success": True,
                "analysis_id": analysis_id,
                "migrated_chunks": 0,  # Would be actual count
                "source": "redis",
                "destination": "weaviate"
            }
            
        except Exception as e:
            logger.error(f"Migration failed for analysis {analysis_id}: {e}")
            return {"success": False, "error": str(e)}
    
    def get_active_service_info(self) -> Dict[str, Any]:
        """Get information about active services"""
        return {
            "weaviate_enabled": self.use_weaviate,
            "weaviate_available": self.weaviate_service is not None,
            "redis_available": self.redis_service is not None,
            "primary_service": "weaviate" if self.use_weaviate else "redis",
            "fallback_enabled": self.fallback_to_redis,
            "unlimited_capacity": self.use_weaviate and self.weaviate_service is not None
        }
