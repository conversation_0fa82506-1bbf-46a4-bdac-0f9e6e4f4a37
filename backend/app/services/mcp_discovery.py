"""
MCP Discovery Service

This service uses Tavily API to discover active MCP servers and integrations
from the web, keeping our database updated with the latest MCP ecosystem.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import re
import aiohttp
from sqlalchemy.orm import Session

from app.config import settings
from app.models.analysis import MCPServer, MCPCategory
from app.database import get_db

logger = logging.getLogger(__name__)


@dataclass
class DiscoveredMCPServer:
    """Represents a discovered MCP server from web search"""
    name: str
    url: str
    description: str
    category: str
    github_url: Optional[str] = None
    npm_url: Optional[str] = None
    documentation_url: Optional[str] = None
    last_updated: Optional[str] = None
    stars: Optional[int] = None
    language: Optional[str] = None
    confidence_score: float = 0.0


class MCPDiscoveryService:
    """Service for discovering MCP servers using web search"""
    
    def __init__(self):
        self.tavily_api_key = settings.tavily_api_key
        self.search_queries = [
            "MCP server Model Context Protocol 2024",
            "MCP servers GitHub Model Context Protocol",
            "Claude MCP server integrations",
            "Model Context Protocol tools servers",
            "MCP marketplace servers 2024",
            "anthropic MCP server examples",
            "MCP server npm packages",
            "Model Context Protocol GitHub repositories"
        ]
        
    async def discover_active_mcp_servers(self) -> List[DiscoveredMCPServer]:
        """
        Discover active MCP servers using Tavily web search
        
        Returns:
            List of discovered MCP servers
        """
        if not self.tavily_api_key:
            logger.warning("Tavily API key not configured, skipping MCP discovery")
            return []
            
        logger.info("Starting MCP server discovery using Tavily")
        
        all_servers = []
        
        for query in self.search_queries:
            try:
                servers = await self._search_mcp_servers(query)
                all_servers.extend(servers)
                # Rate limiting
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Error searching for '{query}': {str(e)}")
                continue
        
        # Deduplicate and rank servers
        unique_servers = self._deduplicate_servers(all_servers)
        ranked_servers = self._rank_servers(unique_servers)
        
        logger.info(f"Discovered {len(ranked_servers)} unique MCP servers")
        return ranked_servers
    
    async def _search_mcp_servers(self, query: str) -> List[DiscoveredMCPServer]:
        """Search for MCP servers using Tavily API"""
        servers = []
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "api_key": self.tavily_api_key,
                    "query": query,
                    "search_depth": "advanced",
                    "include_answer": True,
                    "include_raw_content": False,
                    "max_results": 10,
                    "include_domains": [
                        "github.com",
                        "npmjs.com",
                        "pypi.org",
                        "docs.anthropic.com",
                        "modelcontextprotocol.io"
                    ]
                }
                
                async with session.post(
                    "https://api.tavily.com/search",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        servers = self._parse_search_results(data)
                    else:
                        logger.error(f"Tavily API error: {response.status}")
                        
        except Exception as e:
            logger.error(f"Error calling Tavily API: {str(e)}")
            
        return servers
    
    def _parse_search_results(self, search_data: Dict[str, Any]) -> List[DiscoveredMCPServer]:
        """Parse Tavily search results to extract MCP server information"""
        servers = []
        
        results = search_data.get("results", [])
        
        for result in results:
            try:
                server = self._extract_server_info(result)
                if server:
                    servers.append(server)
            except Exception as e:
                logger.error(f"Error parsing search result: {str(e)}")
                continue
                
        return servers
    
    def _extract_server_info(self, result: Dict[str, Any]) -> Optional[DiscoveredMCPServer]:
        """Extract MCP server information from a search result"""
        url = result.get("url", "")
        title = result.get("title", "")
        content = result.get("content", "")
        
        # Skip if not MCP related
        if not self._is_mcp_related(title, content, url):
            return None
            
        # Extract server name
        name = self._extract_server_name(title, url)
        if not name:
            return None
            
        # Extract description
        description = self._extract_description(content, title)
        
        # Determine category
        category = self._determine_category(title, content, url)
        
        # Extract additional metadata
        github_url = url if "github.com" in url else None
        npm_url = url if "npmjs.com" in url else None
        language = self._detect_language(url, content)
        stars = self._extract_github_stars(content) if github_url else None
        
        # Calculate confidence score
        confidence = self._calculate_confidence(title, content, url)
        
        return DiscoveredMCPServer(
            name=name,
            url=url,
            description=description,
            category=category,
            github_url=github_url,
            npm_url=npm_url,
            language=language,
            stars=stars,
            confidence_score=confidence
        )
    
    def _is_mcp_related(self, title: str, content: str, url: str) -> bool:
        """Check if the result is related to MCP servers"""
        mcp_keywords = [
            "mcp server", "model context protocol", "mcp-server",
            "anthropic mcp", "claude mcp", "mcp tool", "mcp integration"
        ]
        
        text = f"{title} {content} {url}".lower()
        return any(keyword in text for keyword in mcp_keywords)
    
    def _extract_server_name(self, title: str, url: str) -> Optional[str]:
        """Extract server name from title or URL"""
        # Try to extract from GitHub URL
        if "github.com" in url:
            parts = url.split("/")
            if len(parts) >= 5:
                repo_name = parts[4]
                if "mcp" in repo_name.lower():
                    return repo_name
                    
        # Try to extract from title
        title_lower = title.lower()
        if "mcp" in title_lower:
            # Extract the part with MCP
            words = title.split()
            for i, word in enumerate(words):
                if "mcp" in word.lower():
                    if i > 0:
                        return f"{words[i-1]}-{word}".lower()
                    elif i < len(words) - 1:
                        return f"{word}-{words[i+1]}".lower()
                    else:
                        return word.lower()
                        
        return None
    
    def _extract_description(self, content: str, title: str) -> str:
        """Extract description from content"""
        # Take first sentence or first 200 characters
        sentences = content.split(". ")
        if sentences:
            description = sentences[0]
            if len(description) > 200:
                description = description[:200] + "..."
            return description
        return title[:200] if title else "MCP Server"
    
    def _determine_category(self, title: str, content: str, url: str) -> str:
        """Determine the category of the MCP server"""
        text = f"{title} {content} {url}".lower()
        
        categories = {
            "database": ["database", "sql", "postgres", "mysql", "sqlite", "mongo"],
            "ai_chat": ["openai", "anthropic", "claude", "gpt", "llm", "ai"],
            "development": ["github", "git", "code", "development", "programming"],
            "productivity": ["notion", "slack", "email", "calendar", "task"],
            "cloud": ["aws", "azure", "gcp", "cloud", "storage"],
            "monitoring": ["sentry", "logging", "monitoring", "observability"],
            "search": ["search", "tavily", "web", "scraping"],
            "payment": ["stripe", "payment", "billing", "checkout"],
            "communication": ["email", "sms", "twilio", "sendgrid"]
        }
        
        for category, keywords in categories.items():
            if any(keyword in text for keyword in keywords):
                return category
                
        return "general"
    
    def _detect_language(self, url: str, content: str) -> Optional[str]:
        """Detect programming language"""
        if "npmjs.com" in url or "package.json" in content:
            return "javascript"
        elif "pypi.org" in url or "pip install" in content:
            return "python"
        elif "github.com" in url:
            # Try to detect from content
            if any(lang in content.lower() for lang in ["typescript", "javascript", "node"]):
                return "javascript"
            elif any(lang in content.lower() for lang in ["python", "pip", "pypi"]):
                return "python"
            elif "go.mod" in content or "golang" in content.lower():
                return "go"
                
        return None
    
    def _extract_github_stars(self, content: str) -> Optional[int]:
        """Extract GitHub stars count from content"""
        star_pattern = r"(\d+)\s*stars?"
        match = re.search(star_pattern, content, re.IGNORECASE)
        if match:
            try:
                return int(match.group(1))
            except ValueError:
                pass
        return None
    
    def _calculate_confidence(self, title: str, content: str, url: str) -> float:
        """Calculate confidence score for the discovered server"""
        score = 0.0
        
        # URL-based scoring
        if "github.com" in url:
            score += 0.3
        if "npmjs.com" in url or "pypi.org" in url:
            score += 0.2
        if "modelcontextprotocol" in url:
            score += 0.4
            
        # Content-based scoring
        text = f"{title} {content}".lower()
        if "mcp server" in text:
            score += 0.3
        if "model context protocol" in text:
            score += 0.2
        if "anthropic" in text or "claude" in text:
            score += 0.1
            
        return min(score, 1.0)
    
    def _deduplicate_servers(self, servers: List[DiscoveredMCPServer]) -> List[DiscoveredMCPServer]:
        """Remove duplicate servers based on URL and name"""
        seen_urls = set()
        seen_names = set()
        unique_servers = []
        
        for server in servers:
            if server.url not in seen_urls and server.name not in seen_names:
                seen_urls.add(server.url)
                seen_names.add(server.name)
                unique_servers.append(server)
                
        return unique_servers
    
    def _rank_servers(self, servers: List[DiscoveredMCPServer]) -> List[DiscoveredMCPServer]:
        """Rank servers by confidence score and popularity"""
        return sorted(
            servers,
            key=lambda s: (s.confidence_score, s.stars or 0),
            reverse=True
        )
    
    async def update_database(self, discovered_servers: List[DiscoveredMCPServer]):
        """Update database with discovered MCP servers"""
        try:
            db = next(get_db())
            
            for server in discovered_servers:
                # Check if server already exists
                existing = db.query(MCPServer).filter(
                    MCPServer.url == server.url
                ).first()
                
                if existing:
                    # Update existing server
                    existing.description = server.description
                    existing.category = server.category
                    existing.stars = server.stars
                    existing.language = server.language
                    existing.last_updated = datetime.utcnow()
                else:
                    # Create new server
                    new_server = MCPServer(
                        name=server.name,
                        url=server.url,
                        description=server.description,
                        category=server.category,
                        github_url=server.github_url,
                        npm_url=server.npm_url,
                        stars=server.stars,
                        language=server.language,
                        confidence_score=server.confidence_score,
                        created_at=datetime.utcnow(),
                        last_updated=datetime.utcnow()
                    )
                    db.add(new_server)
            
            db.commit()
            logger.info(f"Updated database with {len(discovered_servers)} MCP servers")
            
        except Exception as e:
            logger.error(f"Error updating database: {str(e)}")
            db.rollback()
        finally:
            db.close()
