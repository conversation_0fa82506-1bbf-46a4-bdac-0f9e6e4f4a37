"""
Domain-Specific Prompt Engineering
Creates intelligent, context-aware prompts for repository analysis and MCP suggestions
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class PromptTemplate:
    domain: str
    analysis_prompt: str
    mcp_suggestion_prompt: str
    workflow_prompt: str
    marketplace_prompt: str

class DomainSpecificPrompts:
    """Generates domain-specific prompts for intelligent repository analysis"""
    
    def __init__(self):
        self.prompt_templates = self._initialize_prompt_templates()
    
    def get_repository_analysis_prompt(
        self, 
        domain: str, 
        repository_context: Dict[str, Any],
        indexed_code_summary: Dict[str, Any]
    ) -> str:
        """Generate domain-specific repository analysis prompt"""
        
        template = self.prompt_templates.get(domain, self.prompt_templates['general'])
        
        # Build context-rich prompt
        context_section = self._build_context_section(repository_context, indexed_code_summary)
        domain_section = self._build_domain_section(domain)
        
        return template.analysis_prompt.format(
            context=context_section,
            domain_knowledge=domain_section,
            repo_name=repository_context.get('name', 'Unknown'),
            repo_description=repository_context.get('description', ''),
            technologies=', '.join(repository_context.get('technologies', [])),
            capabilities=', '.join(indexed_code_summary.get('capabilities', [])),
            main_functions=', '.join(indexed_code_summary.get('main_functions', [])),
            api_endpoints=', '.join(indexed_code_summary.get('api_endpoints', []))
        )
    
    def get_mcp_suggestion_prompt(
        self, 
        domain: str, 
        repository_context: Dict[str, Any],
        architecture_analysis: Dict[str, Any],
        user_goals: str
    ) -> str:
        """Generate domain-specific MCP suggestion prompt"""
        
        template = self.prompt_templates.get(domain, self.prompt_templates['general'])
        
        return template.mcp_suggestion_prompt.format(
            user_goals=user_goals,
            repo_name=repository_context.get('name', 'Unknown'),
            domain=domain,
            architecture_type=architecture_analysis.get('architecture_type', 'unknown'),
            core_capabilities=', '.join(architecture_analysis.get('core_capabilities', [])),
            integration_points=', '.join(architecture_analysis.get('integration_points', [])),
            api_types=', '.join(architecture_analysis.get('api_types', [])),
            domain_examples=self._get_domain_examples(domain)
        )
    
    def get_marketplace_integration_prompt(
        self, 
        domain: str, 
        user_goals: str,
        repository_context: Dict[str, Any]
    ) -> str:
        """Generate prompt for marketplace MCP discovery"""
        
        template = self.prompt_templates.get(domain, self.prompt_templates['general'])
        
        return template.marketplace_prompt.format(
            domain=domain,
            user_goals=user_goals,
            repo_name=repository_context.get('name', 'Unknown'),
            technologies=', '.join(repository_context.get('technologies', [])),
            domain_integrations=', '.join(self._get_domain_integrations(domain))
        )
    
    def _build_context_section(
        self, 
        repository_context: Dict[str, Any], 
        indexed_code_summary: Dict[str, Any]
    ) -> str:
        """Build comprehensive context section"""
        
        return f"""
REPOSITORY CONTEXT:
- Name: {repository_context.get('name', 'Unknown')}
- Description: {repository_context.get('description', 'No description')}
- Primary Language: {repository_context.get('language', 'Unknown')}
- Technologies: {', '.join(repository_context.get('technologies', []))}

INDEXED CODE ANALYSIS:
- Summary: {indexed_code_summary.get('summary', 'No analysis available')}
- Main Functions: {', '.join(indexed_code_summary.get('main_functions', []))}
- API Endpoints: {', '.join(indexed_code_summary.get('api_endpoints', []))}
- Business Logic: {', '.join(indexed_code_summary.get('business_logic', []))}
- Capabilities: {', '.join(indexed_code_summary.get('capabilities', []))}
"""
    
    def _build_domain_section(self, domain: str) -> str:
        """Build domain-specific knowledge section"""
        
        domain_knowledge = {
            'cms': "Content Management Systems typically handle content creation, publishing, media management, user roles, and API exposure for headless architectures.",
            'testing': "Testing frameworks focus on test execution, browser automation, performance monitoring, visual regression, and CI/CD integration.",
            'ai_workflow': "AI workflow systems manage agent orchestration, task automation, memory management, tool integration, and multi-step process execution.",
            'web_framework': "Web frameworks provide routing, middleware, authentication, database integration, and API development capabilities.",
            'database': "Database systems handle data storage, querying, migrations, backup/restore, and performance optimization.",
            'devops': "DevOps tools manage infrastructure, deployment, monitoring, scaling, and automation of development workflows."
        }
        
        return domain_knowledge.get(domain, "General purpose software with various capabilities and integration points.")
    
    def _get_domain_examples(self, domain: str) -> str:
        """Get domain-specific MCP examples"""
        
        examples = {
            'cms': """
Example MCP Tools for CMS:
- content_manager: Create, update, delete content entries
- media_processor: Upload, resize, optimize media files
- publishing_workflow: Manage content publishing pipeline
- seo_optimizer: Generate meta tags, sitemaps, SEO analysis
- user_manager: Handle user roles, permissions, authentication
""",
            'testing': """
Example MCP Tools for Testing:
- test_generator: Create test cases from specifications
- browser_automator: Execute cross-browser testing
- performance_monitor: Measure and analyze performance metrics
- visual_tester: Capture and compare screenshots
- report_generator: Create comprehensive test reports
""",
            'ai_workflow': """
Example MCP Tools for AI Workflows:
- agent_orchestrator: Coordinate multi-agent workflows
- memory_manager: Handle context and knowledge persistence
- task_executor: Execute complex multi-step tasks
- tool_integrator: Connect and manage external tools
- workflow_optimizer: Analyze and improve process efficiency
"""
        }
        
        return examples.get(domain, "Example MCP Tools: API integrator, data processor, workflow automator")
    
    def _get_domain_integrations(self, domain: str) -> List[str]:
        """Get common integrations for domain"""
        
        integrations = {
            'cms': ['CDN', 'Analytics', 'Email', 'Payment', 'Search', 'Social Media'],
            'testing': ['CI/CD', 'Bug Tracking', 'Performance Monitoring', 'Code Coverage'],
            'ai_workflow': ['LLM Providers', 'Vector Databases', 'APIs', 'Data Sources'],
            'web_framework': ['Databases', 'Authentication', 'Caching', 'Message Queues'],
            'database': ['Backup Services', 'Monitoring', 'Migration Tools', 'Analytics'],
            'devops': ['Cloud Providers', 'Monitoring', 'Logging', 'Security Tools']
        }
        
        return integrations.get(domain, ['APIs', 'Databases', 'External Services'])
    
    def _initialize_prompt_templates(self) -> Dict[str, PromptTemplate]:
        """Initialize domain-specific prompt templates"""
        
        return {
            'cms': PromptTemplate(
                domain='cms',
                analysis_prompt="""You are an expert in Content Management Systems. Analyze this CMS repository and provide comprehensive insights.

{context}

DOMAIN EXPERTISE: {domain_knowledge}

Based on your analysis of this CMS repository, provide:

1. **Architectural Analysis**:
   - CMS architecture pattern (headless, traditional, hybrid)
   - Content modeling approach
   - API design (REST, GraphQL)
   - Plugin/extension system
   - Admin interface capabilities

2. **Core CMS Capabilities**:
   - Content types and fields
   - Media management features
   - User roles and permissions
   - Publishing workflows
   - Multi-language support

3. **Integration Points**:
   - Frontend frameworks supported
   - Database systems
   - CDN and media services
   - Authentication providers
   - Third-party services

4. **MCP Opportunities**:
   - Content CRUD operations
   - Media processing workflows
   - Publishing automation
   - SEO optimization tools
   - User management systems

Provide specific, actionable insights based on the actual repository code and capabilities.""",
                
                mcp_suggestion_prompt="""As a CMS expert, suggest specific MCP tools for this repository based on user goals.

USER GOALS: {user_goals}
REPOSITORY: {repo_name} ({domain})
ARCHITECTURE: {architecture_type}
CORE CAPABILITIES: {core_capabilities}
INTEGRATION POINTS: {integration_points}
API TYPES: {api_types}

{domain_examples}

Suggest 5-7 specific MCP tools that would be valuable for this CMS:

1. **Repository-Specific Tools** (3-4 tools):
   - Based on actual capabilities found in the code
   - Leverage existing API endpoints and functions
   - Address specific CMS workflows

2. **Workflow Enhancement Tools** (2-3 tools):
   - Improve content management processes
   - Automate publishing workflows
   - Enhance user experience

For each tool, provide:
- Tool name and description
- Specific implementation approach
- Required parameters and return types
- Integration with existing CMS features
- Business value and use cases

Format as JSON with detailed implementation guidance.""",
                
                workflow_prompt="",  # Will be added later
                marketplace_prompt="""Find existing MCP servers that would enhance this CMS workflow.

DOMAIN: {domain}
USER GOALS: {user_goals}
REPOSITORY: {repo_name}
TECHNOLOGIES: {technologies}
COMMON CMS INTEGRATIONS: {domain_integrations}

Search for MCP servers that provide:
1. Database management (PostgreSQL, MySQL, MongoDB)
2. File and media handling
3. API development and testing
4. Authentication and security
5. Analytics and monitoring
6. Content delivery and optimization

Focus on servers that complement CMS workflows and reduce development overhead."""
            ),
            
            'testing': PromptTemplate(
                domain='testing',
                analysis_prompt="""You are an expert in testing frameworks and automation. Analyze this testing repository comprehensively.

{context}

DOMAIN EXPERTISE: {domain_knowledge}

Provide detailed analysis covering:

1. **Testing Framework Analysis**:
   - Framework type (unit, integration, e2e, performance)
   - Browser automation capabilities
   - Test execution patterns
   - Reporting mechanisms
   - CI/CD integration

2. **Automation Capabilities**:
   - Cross-browser support
   - Mobile testing features
   - Visual regression testing
   - Performance monitoring
   - API testing capabilities

3. **Integration Ecosystem**:
   - CI/CD pipeline support
   - Test reporting tools
   - Bug tracking integration
   - Code coverage analysis
   - Performance monitoring

4. **MCP Opportunities**:
   - Test generation and execution
   - Result analysis and reporting
   - Performance monitoring
   - Visual testing automation
   - CI/CD workflow integration

Focus on actual testing capabilities and automation potential.""",
                
                mcp_suggestion_prompt="""As a testing expert, suggest MCP tools for this testing framework.

USER GOALS: {user_goals}
REPOSITORY: {repo_name} ({domain})
ARCHITECTURE: {architecture_type}
TESTING CAPABILITIES: {core_capabilities}
INTEGRATION POINTS: {integration_points}

{domain_examples}

Suggest 5-7 MCP tools for this testing framework:

1. **Test Automation Tools** (3-4 tools):
   - Leverage existing test execution capabilities
   - Enhance browser automation features
   - Improve test generation and maintenance

2. **Analysis and Reporting Tools** (2-3 tools):
   - Test result analysis
   - Performance monitoring
   - Visual regression detection

Provide implementation details, parameters, and integration approaches.""",
                
                workflow_prompt="",
                marketplace_prompt="""Find MCP servers to enhance testing workflows.

DOMAIN: {domain}
USER GOALS: {user_goals}
REPOSITORY: {repo_name}
TECHNOLOGIES: {technologies}
TESTING INTEGRATIONS: {domain_integrations}

Search for MCP servers providing:
1. CI/CD integration (GitHub Actions, Jenkins)
2. Bug tracking (Jira, GitHub Issues)
3. Performance monitoring
4. Code coverage analysis
5. Browser automation enhancement
6. Test data management"""
            ),
            
            'general': PromptTemplate(
                domain='general',
                analysis_prompt="""Analyze this repository and identify MCP opportunities.

{context}

Provide comprehensive analysis:

1. **Repository Purpose and Architecture**
2. **Core Capabilities and Features**
3. **Integration Points and APIs**
4. **Potential MCP Tools**
5. **Workflow Enhancement Opportunities**

Focus on actual functionality that could be exposed as MCP tools.""",
                
                mcp_suggestion_prompt="""Suggest MCP tools for this repository.

USER GOALS: {user_goals}
REPOSITORY: {repo_name}
CAPABILITIES: {core_capabilities}

Suggest specific MCP tools based on repository functionality and user goals.""",
                
                workflow_prompt="",
                marketplace_prompt="""Find relevant MCP servers for this repository.

USER GOALS: {user_goals}
REPOSITORY: {repo_name}
TECHNOLOGIES: {technologies}

Search for complementary MCP servers."""
            )
        }
