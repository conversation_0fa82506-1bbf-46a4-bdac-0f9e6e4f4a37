"""
Interactive MCP Workflow Builder
Helps users build and refine MCP workflows through conversation
"""

import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

from .intelligent_analysis_service import IntelligentAnalysisService
from .mcp_marketplace_service import MC<PERSON>arketplaceService

logger = logging.getLogger(__name__)

@dataclass
class WorkflowStep:
    id: str
    type: str  # 'existing_mcp', 'custom_mcp', 'integration', 'data_flow'
    title: str
    description: str
    dependencies: List[str]
    configuration: Dict[str, Any]
    status: str = 'planned'  # 'planned', 'in_progress', 'completed'

@dataclass
class MCPWorkflow:
    id: str
    name: str
    description: str
    user_goals: str
    repository_context: Dict[str, Any]
    steps: List[WorkflowStep]
    created_at: datetime
    updated_at: datetime
    status: str = 'draft'  # 'draft', 'finalized', 'implemented'

class WorkflowBuilder:
    """Interactive workflow builder for MCP creation"""
    
    def __init__(self):
        self.ai_service = IntelligentAnalysisService()
        self.marketplace_service = MCPMarketplaceService()
        self.workflows: Dict[str, MCPWorkflow] = {}
    
    async def start_workflow(
        self, 
        user_goals: str, 
        repository_context: Dict[str, Any],
        analysis_id: int
    ) -> MCPWorkflow:
        """Start a new MCP workflow based on user goals"""
        
        workflow_id = f"workflow_{analysis_id}_{int(datetime.now().timestamp())}"
        
        # Generate initial workflow structure
        initial_steps = await self._generate_initial_workflow_steps(user_goals, repository_context)
        
        workflow = MCPWorkflow(
            id=workflow_id,
            name=f"MCP Workflow for {repository_context.get('name', 'Repository')}",
            description=f"Workflow to achieve: {user_goals}",
            user_goals=user_goals,
            repository_context=repository_context,
            steps=initial_steps,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self.workflows[workflow_id] = workflow
        logger.info(f"Started workflow {workflow_id} with {len(initial_steps)} steps")
        
        return workflow
    
    async def refine_workflow(
        self, 
        workflow_id: str, 
        user_feedback: str
    ) -> MCPWorkflow:
        """Refine workflow based on user feedback"""
        
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.workflows[workflow_id]
        
        # Analyze feedback and update workflow
        updated_steps = await self._refine_workflow_steps(workflow, user_feedback)
        
        workflow.steps = updated_steps
        workflow.updated_at = datetime.now()
        
        logger.info(f"Refined workflow {workflow_id} based on feedback")
        
        return workflow
    
    async def add_workflow_step(
        self, 
        workflow_id: str, 
        step_type: str, 
        step_description: str,
        after_step_id: Optional[str] = None
    ) -> MCPWorkflow:
        """Add a new step to the workflow"""
        
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.workflows[workflow_id]
        
        # Generate step details
        step_details = await self._generate_step_details(
            step_type, step_description, workflow.repository_context
        )
        
        new_step = WorkflowStep(
            id=f"step_{len(workflow.steps) + 1}",
            type=step_type,
            title=step_details['title'],
            description=step_details['description'],
            dependencies=step_details.get('dependencies', []),
            configuration=step_details.get('configuration', {})
        )
        
        # Insert step at appropriate position
        if after_step_id:
            insert_index = next(
                (i + 1 for i, step in enumerate(workflow.steps) if step.id == after_step_id),
                len(workflow.steps)
            )
            workflow.steps.insert(insert_index, new_step)
        else:
            workflow.steps.append(new_step)
        
        workflow.updated_at = datetime.now()
        
        return workflow
    
    async def finalize_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Finalize workflow for implementation"""
        
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.workflows[workflow_id]
        
        # Validate workflow completeness
        validation_result = await self._validate_workflow(workflow)
        
        if validation_result['valid']:
            workflow.status = 'finalized'
            workflow.updated_at = datetime.now()
            
            # Generate implementation plan
            implementation_plan = await self._generate_implementation_plan(workflow)
            
            return {
                'workflow': asdict(workflow),
                'implementation_plan': implementation_plan,
                'ready_for_generation': True
            }
        else:
            return {
                'workflow': asdict(workflow),
                'validation_errors': validation_result['errors'],
                'ready_for_generation': False
            }
    
    def get_workflow(self, workflow_id: str) -> Optional[MCPWorkflow]:
        """Get workflow by ID"""
        return self.workflows.get(workflow_id)
    
    async def _generate_initial_workflow_steps(
        self, 
        user_goals: str, 
        repository_context: Dict[str, Any]
    ) -> List[WorkflowStep]:
        """Generate initial workflow steps based on user goals"""
        
        prompt = f"""Create an MCP workflow for this goal: "{user_goals}"

Repository Context:
- Name: {repository_context.get('name', 'Unknown')}
- Language: {repository_context.get('language', 'Unknown')}
- Technologies: {', '.join(repository_context.get('technologies', []))}

Create a workflow with 3-5 steps. Each step should be one of:
- existing_mcp: Use an existing MCP from marketplace
- custom_mcp: Create a custom MCP
- integration: Integrate multiple MCPs
- data_flow: Define data flow between components

Return JSON array:
[
  {{
    "type": "existing_mcp|custom_mcp|integration|data_flow",
    "title": "Step title",
    "description": "Detailed description",
    "dependencies": ["step1", "step2"],
    "configuration": {{"key": "value"}}
  }}
]"""

        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=1000)
            
            # Parse JSON response
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                steps_data = json.loads(json_content)
            else:
                # Fallback parsing
                steps_data = self._get_default_workflow_steps(user_goals)
            
            # Convert to WorkflowStep objects
            steps = []
            for i, step_data in enumerate(steps_data):
                step = WorkflowStep(
                    id=f"step_{i + 1}",
                    type=step_data.get('type', 'custom_mcp'),
                    title=step_data.get('title', f'Step {i + 1}'),
                    description=step_data.get('description', ''),
                    dependencies=step_data.get('dependencies', []),
                    configuration=step_data.get('configuration', {})
                )
                steps.append(step)
            
            return steps
            
        except Exception as e:
            logger.error(f"Failed to generate initial workflow steps: {str(e)}")
            return self._get_default_workflow_steps(user_goals)
    
    def _get_default_workflow_steps(self, user_goals: str) -> List[WorkflowStep]:
        """Get default workflow steps as fallback"""
        return [
            WorkflowStep(
                id="step_1",
                type="custom_mcp",
                title="Analyze Requirements",
                description=f"Analyze repository to understand requirements for: {user_goals}",
                dependencies=[],
                configuration={}
            ),
            WorkflowStep(
                id="step_2",
                type="existing_mcp",
                title="Find Existing MCPs",
                description="Search marketplace for existing MCPs that can be leveraged",
                dependencies=["step_1"],
                configuration={}
            ),
            WorkflowStep(
                id="step_3",
                type="custom_mcp",
                title="Create Custom MCP",
                description="Develop custom MCP for repository-specific functionality",
                dependencies=["step_2"],
                configuration={}
            )
        ]
    
    async def _refine_workflow_steps(
        self, 
        workflow: MCPWorkflow, 
        user_feedback: str
    ) -> List[WorkflowStep]:
        """Refine workflow steps based on user feedback"""
        
        current_steps = [asdict(step) for step in workflow.steps]
        
        prompt = f"""Refine this MCP workflow based on user feedback:

Current Workflow:
{json.dumps(current_steps, indent=2)}

User Feedback: {user_feedback}

User Goals: {workflow.user_goals}

Update the workflow steps based on the feedback. Return the complete updated workflow as JSON array."""

        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=1200)
            
            # Parse and convert back to WorkflowStep objects
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                steps_data = json.loads(json_content)
                
                refined_steps = []
                for step_data in steps_data:
                    step = WorkflowStep(
                        id=step_data.get('id', f"step_{len(refined_steps) + 1}"),
                        type=step_data.get('type', 'custom_mcp'),
                        title=step_data.get('title', ''),
                        description=step_data.get('description', ''),
                        dependencies=step_data.get('dependencies', []),
                        configuration=step_data.get('configuration', {}),
                        status=step_data.get('status', 'planned')
                    )
                    refined_steps.append(step)
                
                return refined_steps
            
        except Exception as e:
            logger.error(f"Failed to refine workflow steps: {str(e)}")
        
        # Return original steps if refinement fails
        return workflow.steps
    
    async def _generate_step_details(
        self, 
        step_type: str, 
        step_description: str, 
        repository_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate detailed information for a workflow step"""
        
        prompt = f"""Generate details for this workflow step:

Type: {step_type}
Description: {step_description}
Repository: {repository_context.get('name', 'Unknown')} ({repository_context.get('language', 'Unknown')})

Return JSON with:
{{
  "title": "Clear step title",
  "description": "Detailed description",
  "dependencies": ["list", "of", "dependencies"],
  "configuration": {{"key": "value"}}
}}"""

        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=400)
            
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                return json.loads(json_content)
            
        except Exception as e:
            logger.error(f"Failed to generate step details: {str(e)}")
        
        # Fallback
        return {
            'title': step_description,
            'description': f"Implement {step_type}: {step_description}",
            'dependencies': [],
            'configuration': {}
        }
    
    async def _validate_workflow(self, workflow: MCPWorkflow) -> Dict[str, Any]:
        """Validate workflow for completeness and consistency"""
        
        errors = []
        
        # Check if workflow has steps
        if not workflow.steps:
            errors.append("Workflow must have at least one step")
        
        # Check for circular dependencies
        step_ids = {step.id for step in workflow.steps}
        for step in workflow.steps:
            for dep in step.dependencies:
                if dep not in step_ids:
                    errors.append(f"Step {step.id} depends on non-existent step {dep}")
        
        # Check for at least one custom MCP
        has_custom_mcp = any(step.type == 'custom_mcp' for step in workflow.steps)
        if not has_custom_mcp:
            errors.append("Workflow should include at least one custom MCP step")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    async def _generate_implementation_plan(self, workflow: MCPWorkflow) -> Dict[str, Any]:
        """Generate implementation plan for finalized workflow"""
        
        custom_mcps = [step for step in workflow.steps if step.type == 'custom_mcp']
        existing_mcps = [step for step in workflow.steps if step.type == 'existing_mcp']
        
        return {
            'total_steps': len(workflow.steps),
            'custom_mcps_to_create': len(custom_mcps),
            'existing_mcps_to_integrate': len(existing_mcps),
            'estimated_effort_hours': len(custom_mcps) * 4 + len(existing_mcps) * 1,
            'implementation_order': [step.id for step in workflow.steps],
            'next_action': 'Generate MCP server code for custom MCPs'
        }
