import os
import json
import tempfile
import subprocess
from typing import List, Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from ..models import RepoAnalysis, Dependency
from .github_service import GitHubService


@dataclass
class ParsedDependency:
    name: str
    version: Optional[str]
    language: str
    dependency_type: str
    file_path: str


class RepositoryAnalysisService:
    def __init__(self):
        self.github_service = GitHubService()
        self.supported_languages = {
            'python': [
                'requirements.txt', 
                'pyproject.toml', 
                'Pipfile', 
                'setup.py',
                'environment.yml'
            ],
            'javascript': [
                'package.json', 
                'yarn.lock', 
                'package-lock.json',
                'npm-shrinkwrap.json'
            ],
            'typescript': [
                'package.json', 
                'yarn.lock', 
                'package-lock.json'
            ],
            'go': [
                'go.mod', 
                'go.sum',
                'Gopkg.toml'
            ],
            'java': [
                'pom.xml', 
                'build.gradle', 
                'build.gradle.kts',
                'gradle.properties'
            ],
            'rust': [
                'Cargo.toml',
                'Cargo.lock'
            ],
            'php': [
                'composer.json',
                'composer.lock'
            ],
            'ruby': [
                'Gemfile',
                'Gemfile.lock'
            ],
            'csharp': [
                '*.csproj',
                'packages.config',
                'Directory.Packages.props'
            ]
        }

    async def analyze_repository(self, repo_url: str, access_token: str) -> Dict[str, Any]:
        """Analyze a repository for MCP server potential"""
        try:
            # Parse repository URL
            owner, repo_name = self._parse_repo_url(repo_url)
            
            # Get repository info
            repo_info = await self.github_service.get_repository_info(owner, repo_name, access_token)
            
            # Clone or fetch repository contents
            with tempfile.TemporaryDirectory() as temp_dir:
                repo_path = Path(temp_dir) / repo_name
                
                # Clone repository
                await self._clone_repository(repo_url, repo_path, access_token)
                
                # Analyze dependencies
                dependencies = await self._analyze_dependencies(repo_path)
                
                # Detect API endpoints
                api_endpoints = await self._detect_api_endpoints(repo_path)
                
                # Analyze code structure
                code_structure = await self._analyze_code_structure(repo_path)
                
                # Note: MCP feasibility scoring removed - replaced by conversational analysis

                return {
                    'dependencies': dependencies,
                    'api_endpoints': api_endpoints,
                    'code_structure': code_structure,
                    'repository_info': {
                        'language': repo_info.get('language'),
                        'size': repo_info.get('size'),
                        'topics': repo_info.get('topics', []),
                        'description': repo_info.get('description')
                    }
                }
                
        except Exception as e:
            raise Exception(f"Repository analysis failed: {str(e)}")

    async def _clone_repository(self, repo_url: str, repo_path: Path, access_token: str):
        """Clone repository using git"""
        # Add token to URL for private repos
        if access_token:
            auth_url = repo_url.replace('https://', f'https://{access_token}@')
        else:
            auth_url = repo_url
            
        try:
            subprocess.run([
                'git', 'clone', '--depth', '1', auth_url, str(repo_path)
            ], check=True, capture_output=True, text=True)
        except subprocess.CalledProcessError as e:
            raise Exception(f"Failed to clone repository: {e.stderr}")

    async def _analyze_dependencies(self, repo_path: Path) -> List[Dict[str, Any]]:
        """Parse dependencies from various dependency files"""
        dependencies = []
        
        for language, files in self.supported_languages.items():
            for file_pattern in files:
                if '*' in file_pattern:
                    # Handle glob patterns
                    matching_files = list(repo_path.rglob(file_pattern))
                else:
                    # Handle exact filenames
                    matching_files = list(repo_path.rglob(file_pattern))
                
                for file_path in matching_files:
                    try:
                        parsed_deps = await self._parse_dependency_file(file_path, language)
                        dependencies.extend(parsed_deps)
                    except Exception as e:
                        print(f"Error parsing {file_path}: {e}")
                        continue
        
        return [dep.__dict__ for dep in dependencies]

    async def _parse_dependency_file(self, file_path: Path, language: str) -> List[ParsedDependency]:
        """Parse individual dependency files"""
        dependencies = []
        file_name = file_path.name.lower()
        
        try:
            if language == 'python':
                dependencies = await self._parse_python_dependencies(file_path)
            elif language in ['javascript', 'typescript']:
                dependencies = await self._parse_nodejs_dependencies(file_path)
            elif language == 'go':
                dependencies = await self._parse_go_dependencies(file_path)
            elif language == 'java':
                dependencies = await self._parse_java_dependencies(file_path)
            elif language == 'rust':
                dependencies = await self._parse_rust_dependencies(file_path)
            elif language == 'php':
                dependencies = await self._parse_php_dependencies(file_path)
            elif language == 'ruby':
                dependencies = await self._parse_ruby_dependencies(file_path)
            elif language == 'csharp':
                dependencies = await self._parse_csharp_dependencies(file_path)
                
        except Exception as e:
            print(f"Error parsing {file_path} as {language}: {e}")
            
        return dependencies

    async def _parse_python_dependencies(self, file_path: Path) -> List[ParsedDependency]:
        """Parse Python dependency files"""
        dependencies = []
        file_name = file_path.name.lower()
        
        if file_name == 'requirements.txt':
            content = file_path.read_text(encoding='utf-8')
            for line in content.split('\n'):
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('-'):
                    name, version = self._parse_requirement_line(line)
                    if name:
                        dependencies.append(ParsedDependency(
                            name=name,
                            version=version,
                            language='python',
                            dependency_type='direct',
                            file_path=str(file_path.relative_to(file_path.parents[1]))
                        ))
                        
        elif file_name == 'pyproject.toml':
            # Basic parsing without toml library for now
            content = file_path.read_text(encoding='utf-8')
            # Simple regex parsing for dependencies
            import re
            deps_match = re.search(r'dependencies\s*=\s*\[(.*?)\]', content, re.DOTALL)
            if deps_match:
                deps_content = deps_match.group(1)
                for line in deps_content.split(','):
                    line = line.strip().strip('"\'')
                    if line:
                        name, version = self._parse_requirement_line(line)
                        if name:
                            dependencies.append(ParsedDependency(
                                name=name,
                                version=version,
                                language='python',
                                dependency_type='direct',
                                file_path=str(file_path.relative_to(file_path.parents[1]))
                            ))
                
        return dependencies

    async def _parse_nodejs_dependencies(self, file_path: Path) -> List[ParsedDependency]:
        """Parse Node.js package.json dependencies"""
        dependencies = []
        
        if file_path.name == 'package.json':
            try:
                content = json.loads(file_path.read_text(encoding='utf-8'))
                
                # Parse direct dependencies
                if 'dependencies' in content:
                    for name, version in content['dependencies'].items():
                        dependencies.append(ParsedDependency(
                            name=name,
                            version=version,
                            language='javascript',
                            dependency_type='direct',
                            file_path=str(file_path.relative_to(file_path.parents[1]))
                        ))
                
                # Parse dev dependencies
                if 'devDependencies' in content:
                    for name, version in content['devDependencies'].items():
                        dependencies.append(ParsedDependency(
                            name=name,
                            version=version,
                            language='javascript',
                            dependency_type='dev',
                            file_path=str(file_path.relative_to(file_path.parents[1]))
                        ))
                        
                # Parse peer dependencies
                if 'peerDependencies' in content:
                    for name, version in content['peerDependencies'].items():
                        dependencies.append(ParsedDependency(
                            name=name,
                            version=version,
                            language='javascript',
                            dependency_type='peer',
                            file_path=str(file_path.relative_to(file_path.parents[1]))
                        ))
                        
            except json.JSONDecodeError as e:
                print(f"Error parsing package.json: {e}")
                
        return dependencies

    async def _parse_go_dependencies(self, file_path: Path) -> List[ParsedDependency]:
        """Parse Go module dependencies"""
        dependencies = []
        
        if file_path.name == 'go.mod':
            content = file_path.read_text(encoding='utf-8')
            in_require_block = False
            
            for line in content.split('\n'):
                line = line.strip()
                
                if line.startswith('require ('):
                    in_require_block = True
                    continue
                elif line == ')' and in_require_block:
                    in_require_block = False
                    continue
                elif line.startswith('require ') and not in_require_block:
                    # Single require statement
                    parts = line.replace('require ', '').split()
                    if len(parts) >= 2:
                        name = parts[0]
                        version = parts[1]
                        dependencies.append(ParsedDependency(
                            name=name,
                            version=version,
                            language='go',
                            dependency_type='direct',
                            file_path=str(file_path.relative_to(file_path.parents[1]))
                        ))
                elif in_require_block and line and not line.startswith('//'):
                    # Inside require block
                    parts = line.split()
                    if len(parts) >= 2:
                        name = parts[0]
                        version = parts[1]
                        dep_type = 'indirect' if '// indirect' in line else 'direct'
                        dependencies.append(ParsedDependency(
                            name=name,
                            version=version,
                            language='go',
                            dependency_type=dep_type,
                            file_path=str(file_path.relative_to(file_path.parents[1]))
                        ))
                        
        return dependencies

    async def _parse_java_dependencies(self, file_path: Path) -> List[ParsedDependency]:
        """Parse Java dependencies from Maven/Gradle files"""
        dependencies = []
        
        if file_path.name == 'pom.xml':
            # Basic XML parsing for Maven dependencies
            try:
                content = file_path.read_text(encoding='utf-8')
                # Simple regex-based parsing (for production, use proper XML parser)
                import re
                dependency_pattern = r'<dependency>.*?<groupId>(.*?)</groupId>.*?<artifactId>(.*?)</artifactId>.*?<version>(.*?)</version>.*?</dependency>'
                matches = re.findall(dependency_pattern, content, re.DOTALL)
                
                for group_id, artifact_id, version in matches:
                    dependencies.append(ParsedDependency(
                        name=f"{group_id}:{artifact_id}",
                        version=version.strip(),
                        language='java',
                        dependency_type='direct',
                        file_path=str(file_path.relative_to(file_path.parents[1]))
                    ))
            except Exception as e:
                print(f"Error parsing pom.xml: {e}")
                
        elif 'build.gradle' in file_path.name:
            # Basic Gradle parsing
            content = file_path.read_text(encoding='utf-8')
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                if ('implementation' in line or 'compile' in line or 'api' in line) and '"' in line:
                    # Extract dependency string
                    import re
                    match = re.search(r'["\']([^"\']+)["\']', line)
                    if match:
                        dep_string = match.group(1)
                        parts = dep_string.split(':')
                        if len(parts) >= 2:
                            name = f"{parts[0]}:{parts[1]}"
                            version = parts[2] if len(parts) > 2 else None
                            dependencies.append(ParsedDependency(
                                name=name,
                                version=version,
                                language='java',
                                dependency_type='direct',
                                file_path=str(file_path.relative_to(file_path.parents[1]))
                            ))
                            
        return dependencies

    async def _parse_rust_dependencies(self, file_path: Path) -> List[ParsedDependency]:
        """Parse Rust Cargo.toml dependencies (basic parsing)"""
        dependencies = []
        
        if file_path.name == 'Cargo.toml':
            content = file_path.read_text(encoding='utf-8')
            
            # Simple regex parsing for dependencies section
            import re
            deps_section = re.search(r'\[dependencies\](.*?)(?:\[|$)', content, re.DOTALL)
            if deps_section:
                deps_content = deps_section.group(1)
                for line in deps_content.split('\n'):
                    line = line.strip()
                    if '=' in line and not line.startswith('#'):
                        parts = line.split('=', 1)
                        if len(parts) == 2:
                            name = parts[0].strip()
                            version = parts[1].strip().strip('"\'')
                            dependencies.append(ParsedDependency(
                                name=name,
                                version=version,
                                language='rust',
                                dependency_type='direct',
                                file_path=str(file_path.relative_to(file_path.parents[1]))
                            ))
                        
        return dependencies

    async def _parse_php_dependencies(self, file_path: Path) -> List[ParsedDependency]:
        """Parse PHP composer.json dependencies"""
        dependencies = []
        
        if file_path.name == 'composer.json':
            try:
                content = json.loads(file_path.read_text(encoding='utf-8'))
                
                # Parse require dependencies
                if 'require' in content:
                    for name, version in content['require'].items():
                        dependencies.append(ParsedDependency(
                            name=name,
                            version=version,
                            language='php',
                            dependency_type='direct',
                            file_path=str(file_path.relative_to(file_path.parents[1]))
                        ))
                
                # Parse dev dependencies
                if 'require-dev' in content:
                    for name, version in content['require-dev'].items():
                        dependencies.append(ParsedDependency(
                            name=name,
                            version=version,
                            language='php',
                            dependency_type='dev',
                            file_path=str(file_path.relative_to(file_path.parents[1]))
                        ))
                        
            except json.JSONDecodeError as e:
                print(f"Error parsing composer.json: {e}")
                
        return dependencies

    async def _parse_ruby_dependencies(self, file_path: Path) -> List[ParsedDependency]:
        """Parse Ruby Gemfile dependencies"""
        dependencies = []
        
        if file_path.name == 'Gemfile':
            content = file_path.read_text(encoding='utf-8')
            
            for line in content.split('\n'):
                line = line.strip()
                if line.startswith('gem '):
                    # Parse gem line
                    import re
                    match = re.match(r'gem\s+["\']([^"\']+)["\'](?:\s*,\s*["\']([^"\']+)["\'])?', line)
                    if match:
                        name = match.group(1)
                        version = match.group(2) if match.group(2) else None
                        dependencies.append(ParsedDependency(
                            name=name,
                            version=version,
                            language='ruby',
                            dependency_type='direct',
                            file_path=str(file_path.relative_to(file_path.parents[1]))
                        ))
                        
        return dependencies

    async def _parse_csharp_dependencies(self, file_path: Path) -> List[ParsedDependency]:
        """Parse C# project dependencies"""
        dependencies = []
        
        if file_path.suffix == '.csproj':
            content = file_path.read_text(encoding='utf-8')
            # Simple XML parsing for PackageReference
            import re
            package_pattern = r'<PackageReference\s+Include="([^"]+)"\s+Version="([^"]+)"'
            matches = re.findall(package_pattern, content)
            
            for package_name, version in matches:
                dependencies.append(ParsedDependency(
                    name=package_name,
                    version=version,
                    language='csharp',
                    dependency_type='direct',
                    file_path=str(file_path.relative_to(file_path.parents[1]))
                ))
                
        return dependencies

    def _parse_requirement_line(self, line: str) -> tuple[Optional[str], Optional[str]]:
        """Parse a Python requirement line"""
        line = line.strip()
        if not line or line.startswith('#'):
            return None, None
            
        # Handle git URLs and other complex formats
        if line.startswith('git+') or line.startswith('http'):
            return None, None
            
        # Simple version parsing
        import re
        match = re.match(r'^([a-zA-Z0-9\-_\.]+)([><=!]*[0-9\.\*]*)?', line)
        if match:
            name = match.group(1)
            version = match.group(2) if match.group(2) else None
            return name, version
            
        return None, None

    async def _detect_api_endpoints(self, repo_path: Path) -> List[Dict[str, Any]]:
        """Detect API endpoints in the codebase"""
        endpoints = []
        
        # Search for common API patterns
        api_patterns = [
            r'@app\.route\(["\']([^"\']+)["\']',  # Flask
            r'@router\.(get|post|put|delete)\(["\']([^"\']+)["\']',  # FastAPI
            r'app\.(get|post|put|delete)\(["\']([^"\']+)["\']',  # Express.js
            r'@GetMapping\(["\']([^"\']+)["\']',  # Spring Boot
            r'@PostMapping\(["\']([^"\']+)["\']',
            r'@PutMapping\(["\']([^"\']+)["\']',
            r'@DeleteMapping\(["\']([^"\']+)["\']',
        ]
        
        try:
            for file_path in repo_path.rglob('*.py'):
                content = file_path.read_text(encoding='utf-8', errors='ignore')
                for pattern in api_patterns:
                    import re
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if isinstance(match, tuple):
                            method, path = match
                            endpoints.append({
                                'method': method.upper(),
                                'path': path,
                                'file': str(file_path.relative_to(repo_path))
                            })
                        else:
                            endpoints.append({
                                'method': 'GET',
                                'path': match,
                                'file': str(file_path.relative_to(repo_path))
                            })
                            
            # Also check JavaScript/TypeScript files
            for file_path in repo_path.rglob('*.js'):
                content = file_path.read_text(encoding='utf-8', errors='ignore')
                for pattern in api_patterns:
                    import re
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if isinstance(match, tuple):
                            method, path = match
                            endpoints.append({
                                'method': method.upper(),
                                'path': path,
                                'file': str(file_path.relative_to(repo_path))
                            })
                        else:
                            endpoints.append({
                                'method': 'GET',
                                'path': match,
                                'file': str(file_path.relative_to(repo_path))
                            })
                            
        except Exception as e:
            print(f"Error detecting API endpoints: {e}")
            
        return endpoints

    async def _analyze_code_structure(self, repo_path: Path) -> Dict[str, Any]:
        """Analyze code structure for MCP potential"""
        structure = {
            'languages': {},
            'file_count': 0,
            'has_cli': False,
            'has_web_interface': False,
            'has_database': False,
            'has_api': False,
            'complexity_score': 0
        }
        
        try:
            # Count files by language
            language_extensions = {
                '.py': 'python',
                '.js': 'javascript',
                '.ts': 'typescript',
                '.go': 'go',
                '.java': 'java',
                '.rs': 'rust',
                '.php': 'php',
                '.rb': 'ruby',
                '.cs': 'csharp',
                '.cpp': 'cpp',
                '.c': 'c'
            }
            
            for file_path in repo_path.rglob('*'):
                if file_path.is_file():
                    structure['file_count'] += 1
                    ext = file_path.suffix.lower()
                    if ext in language_extensions:
                        lang = language_extensions[ext]
                        structure['languages'][lang] = structure['languages'].get(lang, 0) + 1
            
            # Check for CLI indicators
            cli_indicators = ['main.py', 'cli.py', '__main__.py', 'bin/', 'cmd/']
            for indicator in cli_indicators:
                if list(repo_path.rglob(indicator)):
                    structure['has_cli'] = True
                    break
            
            # Check for web interface
            web_indicators = ['templates/', 'static/', 'public/', 'frontend/', 'web/']
            for indicator in web_indicators:
                if list(repo_path.rglob(indicator)):
                    structure['has_web_interface'] = True
                    break
            
            # Check for database usage
            db_indicators = ['models.py', 'schema.sql', 'migrations/', 'alembic/']
            for indicator in db_indicators:
                if list(repo_path.rglob(indicator)):
                    structure['has_database'] = True
                    break
            
            # Calculate complexity score (0-100)
            complexity_factors = [
                structure['file_count'] > 10,  # Has substantial codebase
                len(structure['languages']) > 1,  # Multi-language
                structure['has_cli'],  # Has CLI interface
                structure['has_web_interface'],  # Has web interface
                structure['has_database'],  # Has database
                structure['file_count'] > 50,  # Large codebase
            ]
            
            structure['complexity_score'] = sum(complexity_factors) * 16.67  # Scale to 0-100
            
        except Exception as e:
            print(f"Error analyzing code structure: {e}")
            
        return structure

    # MCP feasibility scoring removed - replaced by conversational analysis system

    def _parse_repo_url(self, repo_url: str) -> tuple[str, str]:
        """Parse GitHub repository URL to extract owner and repo name"""
        import re
        pattern = r'github\.com[/:]([^/]+)/([^/]+?)(?:\.git)?/?$'
        match = re.search(pattern, repo_url)
        if match:
            return match.group(1), match.group(2)
        else:
            raise ValueError(f"Invalid GitHub repository URL: {repo_url}")


# Legacy compatibility - keep the original class for existing code
class AnalysisService:
    """Legacy service - use RepositoryAnalysisService for new code"""
    
    def __init__(self):
        self.repo_service = RepositoryAnalysisService()
    
    # MCP feasibility scoring removed - replaced by conversational analysis system