"""
MCP Server Code Generator
Generates complete MCP server implementations based on repository analysis
"""
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import re


class MCPServerGenerator:
    """Generates complete MCP server implementations"""
    
    def __init__(self):
        self.templates = {
            'python': self._get_python_template(),
            'javascript': self._get_javascript_template(),
            'typescript': self._get_typescript_template()
        }
    
    def generate_mcp_server(
        self, 
        repo_analysis: Dict[str, Any], 
        language: str = 'python',
        server_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate complete MCP server implementation
        
        Args:
            repo_analysis: Complete repository analysis results
            language: Target language for MCP server (python, javascript, typescript)
            server_name: Custom server name
            
        Returns:
            Dictionary containing generated MCP server code, config, and documentation
        """
        if not server_name:
            repo_name = repo_analysis.get('repository_info', {}).get('name', 'unknown')
            server_name = f"{repo_name}-mcp-server"
        
        # Analyze repository capabilities
        capabilities = self._analyze_mcp_capabilities(repo_analysis)
        
        # Generate server code based on capabilities
        server_code = self._generate_server_code(capabilities, language, server_name)
        
        # Generate configuration
        config = self._generate_server_config(capabilities, server_name)
        
        # Generate documentation
        documentation = self._generate_documentation(capabilities, server_name, language)
        
        # Generate deployment scripts
        deployment = self._generate_deployment_scripts(server_name, language)
        
        return {
            'server_name': server_name,
            'language': language,
            'capabilities': capabilities,
            'code': server_code,
            'config': config,
            'documentation': documentation,
            'deployment': deployment,
            'generated_at': datetime.utcnow().isoformat()
        }
    
    def _analyze_mcp_capabilities(self, repo_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze repository to determine MCP server capabilities"""
        capabilities = {
            'tools': [],
            'resources': [],
            'prompts': [],
            'server_info': {}
        }
        
        code_structure = repo_analysis.get('code_structure', {})
        api_endpoints = repo_analysis.get('api_endpoints', [])
        dependencies = repo_analysis.get('dependencies', [])
        repo_info = repo_analysis.get('repository_info', {})
        
        # Analyze API endpoints for tools
        for endpoint in api_endpoints:
            tool = self._endpoint_to_tool(endpoint)
            if tool:
                capabilities['tools'].append(tool)
        
        # Analyze code structure for resources
        if code_structure.get('has_database'):
            capabilities['resources'].extend(self._generate_database_resources(repo_analysis))
        
        if code_structure.get('has_file_operations'):
            capabilities['resources'].extend(self._generate_file_resources(repo_analysis))
        
        # Analyze for prompt templates
        capabilities['prompts'] = self._generate_prompt_templates(repo_analysis)
        
        # Server metadata
        capabilities['server_info'] = {
            'name': repo_info.get('name', 'Unknown'),
            'description': repo_info.get('description', 'MCP Server generated from repository analysis'),
            'version': '1.0.0',
            'author': repo_info.get('owner', 'Unknown'),
            'primary_language': repo_info.get('language', 'Unknown'),
            'repository_url': repo_info.get('url', ''),
            # MCP feasibility score removed - using conversational analysis instead
        }
        
        return capabilities
    
    def _endpoint_to_tool(self, endpoint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Convert API endpoint to MCP tool definition"""
        path = endpoint.get('path', '')
        method = endpoint.get('method', 'GET').upper()
        description = endpoint.get('description', f"{method} {path}")
        
        # Generate tool name from path
        tool_name = re.sub(r'[^a-zA-Z0-9_]', '_', path.strip('/').replace('/', '_'))
        if not tool_name:
            tool_name = f"{method.lower()}_endpoint"
        
        # Determine input schema based on endpoint
        input_schema = {
            "type": "object",
            "properties": {},
            "required": []
        }
        
        # Add path parameters
        path_params = re.findall(r'\{([^}]+)\}', path)
        for param in path_params:
            input_schema["properties"][param] = {
                "type": "string",
                "description": f"The {param} parameter"
            }
            input_schema["required"].append(param)
        
        # Add common parameters based on method
        if method in ['POST', 'PUT', 'PATCH']:
            input_schema["properties"]["data"] = {
                "type": "object",
                "description": "Request body data"
            }
        
        if method == 'GET':
            input_schema["properties"]["params"] = {
                "type": "object",
                "description": "Query parameters"
            }
        
        return {
            "name": tool_name,
            "description": description,
            "inputSchema": input_schema,
            "endpoint": {
                "path": path,
                "method": method
            }
        }
    
    def _generate_database_resources(self, repo_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate database-related MCP resources"""
        resources = []
        
        # Generic database operations
        db_operations = [
            {
                "uri": "database://tables",
                "name": "Database Tables",
                "description": "List all database tables",
                "mimeType": "application/json"
            },
            {
                "uri": "database://schema",
                "name": "Database Schema",
                "description": "Get database schema information",
                "mimeType": "application/json"
            }
        ]
        
        resources.extend(db_operations)
        return resources
    
    def _generate_file_resources(self, repo_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate file-related MCP resources"""
        resources = []
        
        file_operations = [
            {
                "uri": "file://read",
                "name": "File Reader",
                "description": "Read file contents",
                "mimeType": "text/plain"
            },
            {
                "uri": "file://list",
                "name": "File Listing",
                "description": "List files in directory",
                "mimeType": "application/json"
            }
        ]
        
        resources.extend(file_operations)
        return resources
    
    def _generate_prompt_templates(self, repo_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate prompt templates based on repository analysis"""
        prompts = []
        
        repo_info = repo_analysis.get('repository_info', {})
        language = repo_info.get('language', '').lower()
        
        # Language-specific prompts
        if language == 'python':
            prompts.append({
                "name": "python_code_analysis",
                "description": "Analyze Python code for best practices",
                "arguments": [
                    {
                        "name": "code",
                        "description": "Python code to analyze",
                        "required": True
                    }
                ]
            })
        
        # Generic prompts
        prompts.extend([
            {
                "name": "code_review",
                "description": "Perform code review and suggest improvements",
                "arguments": [
                    {
                        "name": "code",
                        "description": "Code to review",
                        "required": True
                    },
                    {
                        "name": "language",
                        "description": "Programming language",
                        "required": False
                    }
                ]
            },
            {
                "name": "documentation_generator",
                "description": "Generate documentation for code",
                "arguments": [
                    {
                        "name": "code",
                        "description": "Code to document",
                        "required": True
                    }
                ]
            }
        ])
        
        return prompts
    
    def _generate_server_code(
        self, 
        capabilities: Dict[str, Any], 
        language: str, 
        server_name: str
    ) -> Dict[str, str]:
        """Generate MCP server code files"""
        if language not in self.templates:
            raise ValueError(f"Unsupported language: {language}")
        
        template = self.templates[language]
        
        # Replace template variables
        code = template.replace('{{SERVER_NAME}}', server_name)
        code = code.replace('{{TOOLS_JSON}}', json.dumps(capabilities['tools'], indent=2))
        code = code.replace('{{RESOURCES_JSON}}', json.dumps(capabilities['resources'], indent=2))
        code = code.replace('{{PROMPTS_JSON}}', json.dumps(capabilities['prompts'], indent=2))
        code = code.replace('{{SERVER_INFO_JSON}}', json.dumps(capabilities['server_info'], indent=2))
        
        # Generate additional files based on language
        files = {
            'main': code
        }
        
        if language == 'python':
            files['requirements.txt'] = self._generate_python_requirements()
            files['setup.py'] = self._generate_python_setup(server_name, capabilities['server_info'])
        elif language in ['javascript', 'typescript']:
            files['package.json'] = self._generate_package_json(server_name, capabilities['server_info'])
        
        return files
    
    def _generate_server_config(self, capabilities: Dict[str, Any], server_name: str) -> Dict[str, Any]:
        """Generate MCP server configuration"""
        return {
            "mcpServers": {
                server_name: {
                    "command": self._get_command_for_server(server_name),
                    "args": [],
                    "env": {
                        "LOG_LEVEL": "info"
                    }
                }
            },
            "capabilities": {
                "tools": len(capabilities['tools']),
                "resources": len(capabilities['resources']),
                "prompts": len(capabilities['prompts'])
            }
        }
    
    def _generate_documentation(
        self, 
        capabilities: Dict[str, Any], 
        server_name: str, 
        language: str
    ) -> Dict[str, str]:
        """Generate comprehensive documentation"""
        server_info = capabilities['server_info']
        
        readme = f"""# {server_name}

{server_info.get('description', 'MCP Server generated from repository analysis')}

## Overview

This MCP server was automatically generated based on analysis of the [{server_info.get('name')}]({server_info.get('repository_url')}) repository. It provides {len(capabilities['tools'])} tools, {len(capabilities['resources'])} resources, and {len(capabilities['prompts'])} prompts.

**Generated by:** SuperMCP Conversational Analysis System

## Features

### Tools ({len(capabilities['tools'])})
"""
        
        for tool in capabilities['tools']:
            readme += f"\n- **{tool['name']}**: {tool['description']}"
        
        readme += f"\n\n### Resources ({len(capabilities['resources'])})\n"
        for resource in capabilities['resources']:
            readme += f"\n- **{resource['name']}**: {resource['description']}"
        
        readme += f"\n\n### Prompts ({len(capabilities['prompts'])})\n"
        for prompt in capabilities['prompts']:
            readme += f"\n- **{prompt['name']}**: {prompt['description']}"
        
        readme += f"""

## Installation

### Prerequisites
- {language.title()} runtime environment
- MCP-compatible client (Claude Desktop, etc.)

### Setup

1. Clone or download the generated server code
2. Install dependencies:
"""
        
        if language == 'python':
            readme += """
```bash
pip install -r requirements.txt
```
"""
        elif language in ['javascript', 'typescript']:
            readme += """
```bash
npm install
```
"""
        
        readme += f"""
3. Configure in your MCP client settings:

```json
{{
  "mcpServers": {{
    "{server_name}": {{
      "command": "{self._get_command_for_server(server_name)}",
      "args": []
    }}
  }}
}}
```

## Usage

Once configured, you can use the following capabilities:

### Available Tools
"""
        
        for tool in capabilities['tools']:
            readme += f"""
#### {tool['name']}
{tool['description']}

**Input Schema:**
```json
{json.dumps(tool['inputSchema'], indent=2)}
```
"""
        
        readme += """
## Development

This server was auto-generated. To customize:

1. Modify the tool implementations in the main server file
2. Add new tools by extending the tools array
3. Update documentation as needed

## Support

For issues related to the generated MCP server, please refer to:
- [MCP Documentation](https://modelcontextprotocol.io/docs)
- [Original Repository]({})

Generated on: {}
""".format(server_info.get('repository_url', ''), datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC'))
        
        return {
            'README.md': readme,
            'API.md': self._generate_api_documentation(capabilities),
            'DEPLOYMENT.md': self._generate_deployment_documentation(server_name, language)
        }
    
    def _generate_deployment_scripts(self, server_name: str, language: str) -> Dict[str, str]:
        """Generate deployment scripts and configurations"""
        scripts = {}
        
        if language == 'python':
            scripts['run.sh'] = f"""#!/bin/bash
# Run {server_name}
set -e

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# Install dependencies
pip install -r requirements.txt

# Run the server
python main.py
"""
            
            scripts['Dockerfile'] = f"""FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

CMD ["python", "main.py"]
"""
        
        elif language in ['javascript', 'typescript']:
            scripts['run.sh'] = f"""#!/bin/bash
# Run {server_name}
set -e

# Install dependencies
npm install

# Run the server
npm start
"""
            
            scripts['Dockerfile'] = f"""FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

CMD ["npm", "start"]
"""
        
        return scripts
    
    def _get_python_template(self) -> str:
        """Get Python MCP server template"""
        return '''#!/usr/bin/env python3
"""
{{SERVER_NAME}} - Auto-generated MCP Server
Generated from repository analysis
"""

import asyncio
import json
import logging
from typing import Any, Dict, List
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp import types


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("{{SERVER_NAME}}")

# Server configuration
SERVER_INFO = {{SERVER_INFO_JSON}}
TOOLS = {{TOOLS_JSON}}
RESOURCES = {{RESOURCES_JSON}}
PROMPTS = {{PROMPTS_JSON}}

# Initialize MCP server
app = Server("{{SERVER_NAME}}")


@app.list_tools()
async def list_tools() -> List[types.Tool]:
    """List available tools"""
    tools = []
    for tool_config in TOOLS:
        tools.append(types.Tool(
            name=tool_config["name"],
            description=tool_config["description"],
            inputSchema=tool_config["inputSchema"]
        ))
    return tools


@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Execute a tool"""
    logger.info(f"Executing tool: {name} with arguments: {arguments}")
    
    # Find the tool configuration
    tool_config = next((t for t in TOOLS if t["name"] == name), None)
    if not tool_config:
        raise ValueError(f"Unknown tool: {name}")
    
    try:
        # Execute the tool based on its configuration
        result = await execute_tool(tool_config, arguments)
        return [types.TextContent(
            type="text",
            text=json.dumps(result, indent=2)
        )]
    except Exception as e:
        logger.error(f"Tool execution failed: {e}")
        raise


async def execute_tool(tool_config: Dict[str, Any], arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Execute a specific tool"""
    tool_name = tool_config["name"]
    
    # Handle different types of tools
    if "endpoint" in tool_config:
        return await execute_api_tool(tool_config, arguments)
    else:
        return await execute_custom_tool(tool_name, arguments)


async def execute_api_tool(tool_config: Dict[str, Any], arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Execute an API-based tool"""
    endpoint = tool_config["endpoint"]
    
    # This is a placeholder - implement actual API calls based on your repository
    return {
        "status": "success",
        "message": f"Executed {endpoint['method']} {endpoint['path']}",
        "arguments": arguments,
        "timestamp": asyncio.get_event_loop().time()
    }


async def execute_custom_tool(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Execute a custom tool"""
    # Implement custom tool logic here
    return {
        "status": "success",
        "tool": tool_name,
        "result": f"Custom tool {tool_name} executed",
        "arguments": arguments
    }


@app.list_resources()
async def list_resources() -> List[types.Resource]:
    """List available resources"""
    resources = []
    for resource_config in RESOURCES:
        resources.append(types.Resource(
            uri=resource_config["uri"],
            name=resource_config["name"],
            description=resource_config["description"],
            mimeType=resource_config.get("mimeType", "text/plain")
        ))
    return resources


@app.read_resource()
async def read_resource(uri: str) -> str:
    """Read a resource"""
    logger.info(f"Reading resource: {uri}")
    
    # Find the resource configuration
    resource_config = next((r for r in RESOURCES if r["uri"] == uri), None)
    if not resource_config:
        raise ValueError(f"Unknown resource: {uri}")
    
    # Implement resource reading logic based on URI
    return await read_resource_content(uri)


async def read_resource_content(uri: str) -> str:
    """Read the actual content of a resource"""
    # Implement resource reading logic here
    return f"Content of resource: {uri}"


@app.list_prompts()
async def list_prompts() -> List[types.Prompt]:
    """List available prompts"""
    prompts = []
    for prompt_config in PROMPTS:
        arguments = []
        for arg in prompt_config.get("arguments", []):
            arguments.append(types.PromptArgument(
                name=arg["name"],
                description=arg["description"],
                required=arg.get("required", False)
            ))
        
        prompts.append(types.Prompt(
            name=prompt_config["name"],
            description=prompt_config["description"],
            arguments=arguments
        ))
    return prompts


@app.get_prompt()
async def get_prompt(name: str, arguments: Dict[str, str]) -> types.GetPromptResult:
    """Get a prompt"""
    logger.info(f"Getting prompt: {name} with arguments: {arguments}")
    
    # Find the prompt configuration
    prompt_config = next((p for p in PROMPTS if p["name"] == name), None)
    if not prompt_config:
        raise ValueError(f"Unknown prompt: {name}")
    
    # Generate prompt content
    content = await generate_prompt_content(name, arguments)
    
    return types.GetPromptResult(
        description=prompt_config["description"],
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(type="text", text=content)
            )
        ]
    )


async def generate_prompt_content(name: str, arguments: Dict[str, str]) -> str:
    """Generate prompt content based on name and arguments"""
    # Implement prompt generation logic here
    return f"Generated prompt for {name} with arguments: {json.dumps(arguments, indent=2)}"


async def main():
    """Main entry point"""
    logger.info(f"Starting {{SERVER_NAME}} MCP Server")
    logger.info(f"Server info: {SERVER_INFO}")
    logger.info(f"Available tools: {len(TOOLS)}")
    logger.info(f"Available resources: {len(RESOURCES)}")
    logger.info(f"Available prompts: {len(PROMPTS)}")
    
    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream,
            app.create_initialization_options()
        )


if __name__ == "__main__":
    asyncio.run(main())
'''
    
    def _get_javascript_template(self) -> str:
        """Get JavaScript MCP server template"""
        return '''#!/usr/bin/env node
/**
 * {{SERVER_NAME}} - Auto-generated MCP Server
 * Generated from repository analysis
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';

// Server configuration
const SERVER_INFO = {{SERVER_INFO_JSON}};
const TOOLS = {{TOOLS_JSON}};
const RESOURCES = {{RESOURCES_JSON}};
const PROMPTS = {{PROMPTS_JSON}};

// Initialize MCP server
const server = new Server(
  {
    name: "{{SERVER_NAME}}",
    version: SERVER_INFO.version || "1.0.0",
  },
  {
    capabilities: {
      tools: {},
      resources: {},
      prompts: {}
    },
  }
);

// List tools handler
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: TOOLS.map(tool => ({
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema
    }))
  };
});

// Call tool handler
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  
  console.log(`Executing tool: ${name} with arguments:`, args);
  
  // Find the tool configuration
  const toolConfig = TOOLS.find(t => t.name === name);
  if (!toolConfig) {
    throw new Error(`Unknown tool: ${name}`);
  }
  
  try {
    // Execute the tool
    const result = await executeTool(toolConfig, args);
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2)
        }
      ]
    };
  } catch (error) {
    console.error(`Tool execution failed:`, error);
    throw error;
  }
});

async function executeTool(toolConfig, arguments) {
  const toolName = toolConfig.name;
  
  // Handle different types of tools
  if (toolConfig.endpoint) {
    return await executeApiTool(toolConfig, arguments);
  } else {
    return await executeCustomTool(toolName, arguments);
  }
}

async function executeApiTool(toolConfig, arguments) {
  const endpoint = toolConfig.endpoint;
  
  // This is a placeholder - implement actual API calls based on your repository
  return {
    status: "success",
    message: `Executed ${endpoint.method} ${endpoint.path}`,
    arguments,
    timestamp: Date.now()
  };
}

async function executeCustomTool(toolName, arguments) {
  // Implement custom tool logic here
  return {
    status: "success",
    tool: toolName,
    result: `Custom tool ${toolName} executed`,
    arguments
  };
}

async function main() {
  console.log(`Starting {{SERVER_NAME}} MCP Server`);
  console.log(`Server info:`, SERVER_INFO);
  console.log(`Available tools: ${TOOLS.length}`);
  console.log(`Available resources: ${RESOURCES.length}`);
  console.log(`Available prompts: ${PROMPTS.length}`);
  
  const transport = new StdioServerTransport();
  await server.connect(transport);
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down server...');
  process.exit(0);
});

if (require.main === module) {
  main().catch(console.error);
}
'''
    
    def _get_typescript_template(self) -> str:
        """Get TypeScript MCP server template"""
        return '''#!/usr/bin/env ts-node
/**
 * {{SERVER_NAME}} - Auto-generated MCP Server
 * Generated from repository analysis
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
  CallToolResult,
  TextContent
} from '@modelcontextprotocol/sdk/types.js';

// Type definitions
interface ToolConfig {
  name: string;
  description: string;
  inputSchema: any;
  endpoint?: {
    path: string;
    method: string;
  };
}

interface ServerInfo {
  name: string;
  description: string;
  version: string;
  author: string;
  primary_language: string;
  repository_url: string;
  // mcp_score removed - using conversational analysis instead
}

// Server configuration
const SERVER_INFO: ServerInfo = {{SERVER_INFO_JSON}};
const TOOLS: ToolConfig[] = {{TOOLS_JSON}};
const RESOURCES: any[] = {{RESOURCES_JSON}};
const PROMPTS: any[] = {{PROMPTS_JSON}};

// Initialize MCP server
const server = new Server(
  {
    name: "{{SERVER_NAME}}",
    version: SERVER_INFO.version || "1.0.0",
  },
  {
    capabilities: {
      tools: {},
      resources: {},
      prompts: {}
    },
  }
);

// List tools handler
server.setRequestHandler(ListToolsRequestSchema, async (): Promise<{ tools: Tool[] }> => {
  return {
    tools: TOOLS.map(tool => ({
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema
    }))
  };
});

// Call tool handler
server.setRequestHandler(CallToolRequestSchema, async (request): Promise<CallToolResult> => {
  const { name, arguments: args } = request.params;
  
  console.log(`Executing tool: ${name} with arguments:`, args);
  
  // Find the tool configuration
  const toolConfig = TOOLS.find(t => t.name === name);
  if (!toolConfig) {
    throw new Error(`Unknown tool: ${name}`);
  }
  
  try {
    // Execute the tool
    const result = await executeTool(toolConfig, args || {});
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2)
        } as TextContent
      ]
    };
  } catch (error) {
    console.error(`Tool execution failed:`, error);
    throw error;
  }
});

async function executeTool(toolConfig: ToolConfig, arguments: Record<string, any>): Promise<any> {
  const toolName = toolConfig.name;
  
  // Handle different types of tools
  if (toolConfig.endpoint) {
    return await executeApiTool(toolConfig, arguments);
  } else {
    return await executeCustomTool(toolName, arguments);
  }
}

async function executeApiTool(toolConfig: ToolConfig, arguments: Record<string, any>): Promise<any> {
  const endpoint = toolConfig.endpoint!;
  
  // This is a placeholder - implement actual API calls based on your repository
  return {
    status: "success",
    message: `Executed ${endpoint.method} ${endpoint.path}`,
    arguments,
    timestamp: Date.now()
  };
}

async function executeCustomTool(toolName: string, arguments: Record<string, any>): Promise<any> {
  // Implement custom tool logic here
  return {
    status: "success",
    tool: toolName,
    result: `Custom tool ${toolName} executed`,
    arguments
  };
}

async function main(): Promise<void> {
  console.log(`Starting {{SERVER_NAME}} MCP Server`);
  console.log(`Server info:`, SERVER_INFO);
  console.log(`Available tools: ${TOOLS.length}`);
  console.log(`Available resources: ${RESOURCES.length}`);
  console.log(`Available prompts: ${PROMPTS.length}`);
  
  const transport = new StdioServerTransport();
  await server.connect(transport);
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down server...');
  process.exit(0);
});

if (require.main === module) {
  main().catch(console.error);
}
'''
    
    def _generate_python_requirements(self) -> str:
        """Generate Python requirements.txt"""
        return """# MCP Server Dependencies
mcp>=1.0.0
asyncio
logging
json
typing
"""
    
    def _generate_python_setup(self, server_name: str, server_info: Dict[str, Any]) -> str:
        """Generate Python setup.py"""
        return f"""from setuptools import setup, find_packages

setup(
    name="{server_name}",
    version="{server_info.get('version', '1.0.0')}",
    description="{server_info.get('description', 'Auto-generated MCP Server')}",
    author="{server_info.get('author', 'Unknown')}",
    packages=find_packages(),
    install_requires=[
        "mcp>=1.0.0",
    ],
    entry_points={{
        "console_scripts": [
            "{server_name}=main:main",
        ],
    }},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
)
"""
    
    def _generate_package_json(self, server_name: str, server_info: Dict[str, Any]) -> str:
        """Generate package.json for Node.js"""
        return json.dumps({
            "name": server_name,
            "version": server_info.get('version', '1.0.0'),
            "description": server_info.get('description', 'Auto-generated MCP Server'),
            "type": "module",
            "main": "index.js",
            "scripts": {
                "start": "node index.js",
                "dev": "nodemon index.js",
                "test": "node test/server.test.js"
            },
            "dependencies": {
                "@modelcontextprotocol/sdk": "^1.0.0"
            },
            "devDependencies": {
                "nodemon": "^3.0.0"
            },
            "author": server_info.get('author', 'Unknown'),
            "license": "MIT",
            "repository": {
                "type": "git",
                "url": server_info.get('repository_url', '')
            },
            "engines": {
                "node": ">=18.0.0"
            }
        }, indent=2)
    
    def _get_command_for_server(self, server_name: str) -> str:
        """Get the command to run the server"""
        return f"python main.py"  # Default to Python
    
    def _generate_api_documentation(self, capabilities: Dict[str, Any]) -> str:
        """Generate API documentation"""
        return f"""# API Documentation

## Tools

{json.dumps(capabilities['tools'], indent=2)}

## Resources

{json.dumps(capabilities['resources'], indent=2)}

## Prompts

{json.dumps(capabilities['prompts'], indent=2)}
"""
    
    def _generate_deployment_documentation(self, server_name: str, language: str) -> str:
        """Generate deployment documentation"""
        return f"""# Deployment Guide for {server_name}

## Local Development

1. Install dependencies
2. Run the server: `./run.sh`

## Docker Deployment

1. Build: `docker build -t {server_name} .`
2. Run: `docker run -it {server_name}`

## Production

For production deployment, consider:
- Environment variable configuration
- Logging setup
- Monitoring integration
- Health checks
"""