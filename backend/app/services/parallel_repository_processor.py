"""
Parallel Repository Processor

Handles unlimited repository cloning, processing, and indexing with parallel execution.
Eliminates GitHub API rate limits by working with local repository clones.
"""

import os
import shutil
import tempfile
import asyncio
import logging
import fnmatch
from typing import Dict, List, Any, Optional, Set
from pathlib import Path
import git
from concurrent.futures import ThreadPoolExecutor
from ..config import settings

logger = logging.getLogger(__name__)


class ParallelRepositoryProcessor:
    """
    Processes repositories by cloning them locally and indexing all files in parallel.
    Eliminates GitHub API rate limits and enables unlimited file processing.
    """
    
    def __init__(self):
        self.base_temp_dir = "/tmp/supermcp_repos"
        self.max_workers = 16  # Increased parallel workers for faster processing
        self.max_file_size = 5 * 1024 * 1024  # 5MB per file (reduced for speed)

        # Prioritized file extensions (most important first)
        self.supported_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs', '.cpp', '.c', '.h',
            '.php', '.rb', '.swift', '.kt', '.scala', '.sh', '.sql', '.yaml', '.yml',
            '.json', '.xml', '.html', '.css', '.md', '.txt', '.toml', '.ini', '.cfg', '.conf'
        }

        # Important files without extensions that should always be processed
        self.important_files_no_extension = {
            '.gitignore', '.dockerignore', '.env', '.env.example', '.env.local', '.env.production',
            '.prettierrc', '.prettierignore', '.eslintrc', '.eslintignore', '.babelrc',
            '.editorconfig', '.nvmrc', '.node-version', '.python-version', '.ruby-version',
            'Dockerfile', 'Makefile', 'LICENSE', 'CHANGELOG', 'CONTRIBUTING', 'AUTHORS',
            'CODEOWNERS', 'SECURITY', 'SUPPORT', 'FUNDING', 'CITATION', 'ACKNOWLEDGMENTS'
        }

        # Skip patterns for faster processing (reduced aggressiveness)
        self.skip_patterns = {
            'node_modules', 'dist', 'build', '.next', '.nuxt', 'target', 'bin', 'obj',
            'vendor', 'packages', '.pnpm', 'bower_components', '.git', '.svn', '.hg',
            'logs', 'tmp', 'temp', '.cache', '.pytest_cache', '__pycache__',
            'coverage', '.coverage', '.nyc_output'
            # Removed .vscode, .idea to allow more files for analysis
        }

        # UNLIMITED: No file size limits for comprehensive analysis
        self.file_size_limits = {}  # Removed all size limits
        
    async def process_repository_unlimited(self, repo_url: str, analysis_id: int) -> Dict[str, Any]:
        """
        Process repository with unlimited capacity using local cloning
        
        Args:
            repo_url: Repository URL to process
            analysis_id: Analysis ID for tracking
            
        Returns:
            Complete repository analysis with all files processed
        """
        repo_path = None
        try:
            # Step 1: Clone repository locally
            logger.info(f"Cloning repository {repo_url} for unlimited processing")
            repo_path = await self._clone_repository(repo_url, analysis_id)
            
            # Step 2: Discover files with intelligent filtering (optimized for speed!)
            logger.info(f"Discovering files with smart filtering for faster processing")
            all_files = self._discover_files_optimized(repo_path)
            logger.info(f"Found {len(all_files)} priority files to process (filtered for speed)")
            
            # Step 3: Process files in parallel (much faster!)
            logger.info(f"Processing {len(all_files)} files in parallel")
            processed_files = await self._process_files_parallel(all_files, repo_path)
            
            # Step 4: Extract repository metadata
            repo_metadata = await self._extract_repository_metadata(repo_path, repo_url)
            
            # Step 5: Build comprehensive analysis result
            result = {
                "repository_info": repo_metadata,
                "code_samples": processed_files,
                "files_count": len(processed_files),
                "total_files_discovered": len(all_files),
                "processing_method": "local_clone_parallel",
                "unlimited_capacity": True,
                "api_calls_used": 1,  # Only for cloning
                "analysis_id": analysis_id
            }
            
            logger.info(f"Successfully processed {len(processed_files)} files from {repo_url}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to process repository {repo_url}: {e}")
            raise Exception(f"Repository processing failed: {e}")
            
        finally:
            # Always cleanup temporary files
            if repo_path and os.path.exists(repo_path):
                try:
                    shutil.rmtree(repo_path)
                    logger.info(f"Cleaned up temporary repository: {repo_path}")
                except Exception as e:
                    logger.warning(f"Failed to cleanup {repo_path}: {e}")
    
    async def _clone_repository(self, repo_url: str, analysis_id: int) -> str:
        """Clone repository to temporary location"""
        try:
            # Create unique temporary directory
            repo_name = repo_url.split('/')[-1].replace('.git', '')
            temp_dir = f"{self.base_temp_dir}/analysis_{analysis_id}_{repo_name}"
            
            # Ensure base directory exists
            os.makedirs(self.base_temp_dir, exist_ok=True)
            
            # Remove existing if present
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            
            # Clone repository (shallow clone for speed)
            logger.info(f"Cloning {repo_url} to {temp_dir}")
            
            # Use asyncio to run git clone in thread pool
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor(max_workers=1) as executor:
                await loop.run_in_executor(
                    executor,
                    lambda: git.Repo.clone_from(
                        repo_url, 
                        temp_dir, 
                        depth=1,  # Shallow clone for speed
                        single_branch=True
                    )
                )
            
            logger.info(f"Successfully cloned repository to {temp_dir}")
            return temp_dir
            
        except Exception as e:
            logger.error(f"Failed to clone repository {repo_url}: {e}")
            raise Exception(f"Repository cloning failed: {e}")
    
    async def _discover_all_files(self, repo_path: str) -> List[str]:
        """Discover all processable files in repository"""
        try:
            all_files = []
            
            # Walk through all directories
            for root, dirs, files in os.walk(repo_path):
                # Skip .git directory and other common ignore patterns
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in {
                    '__pycache__', 'node_modules', 'venv', 'env', '.venv', 
                    'dist', 'build', 'target', '.next', '.nuxt'
                }]
                
                for file in files:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, repo_path)
                    
                    # Check if file should be processed
                    if self._should_process_file(file_path, relative_path):
                        all_files.append(relative_path)
            
            logger.info(f"Discovered {len(all_files)} processable files")
            return all_files
            
        except Exception as e:
            logger.error(f"Failed to discover files: {e}")
            return []
    
    def _should_process_file(self, full_path: str, relative_path: str) -> bool:
        """Determine if file should be processed"""
        try:
            # Check file extension
            file_ext = Path(full_path).suffix.lower()
            if file_ext not in self.supported_extensions:
                return False
            
            # Check file size
            if os.path.getsize(full_path) > self.max_file_size:
                logger.debug(f"Skipping large file: {relative_path}")
                return False
            
            # Skip binary files and common ignore patterns
            ignore_patterns = {
                '.git/', '__pycache__/', 'node_modules/', '.venv/', 'venv/',
                'dist/', 'build/', 'target/', '.next/', '.nuxt/', 'coverage/',
                '.pytest_cache/', '.mypy_cache/', '.tox/', 'htmlcov/'
            }
            
            for pattern in ignore_patterns:
                if pattern in relative_path:
                    return False
            
            return True
            
        except Exception:
            return False
    
    async def _process_files_parallel(self, file_paths: List[str], repo_path: str) -> Dict[str, str]:
        """Process multiple files in parallel for maximum speed"""
        try:
            processed_files = {}
            
            # Create semaphore to limit concurrent file operations
            semaphore = asyncio.Semaphore(self.max_workers)
            
            async def process_single_file(relative_path: str) -> tuple:
                async with semaphore:
                    return await self._process_single_file(relative_path, repo_path)
            
            # Process all files concurrently
            tasks = [process_single_file(file_path) for file_path in file_paths]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Collect successful results
            for result in results:
                if isinstance(result, tuple) and len(result) == 2:
                    file_path, content = result
                    if content:
                        processed_files[file_path] = content
                elif isinstance(result, Exception):
                    logger.warning(f"File processing failed: {result}")
            
            logger.info(f"Successfully processed {len(processed_files)} files in parallel")
            return processed_files
            
        except Exception as e:
            logger.error(f"Parallel file processing failed: {e}")
            return {}
    
    async def _process_single_file(self, relative_path: str, repo_path: str) -> tuple:
        """Process a single file and return its content"""
        try:
            full_path = os.path.join(repo_path, relative_path)
            
            # Read file content
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor(max_workers=1) as executor:
                content = await loop.run_in_executor(
                    executor,
                    self._read_file_content,
                    full_path
                )
            
            if content:
                return (relative_path, content)
            else:
                return (relative_path, None)
                
        except Exception as e:
            logger.debug(f"Failed to process file {relative_path}: {e}")
            return (relative_path, None)
    
    def _read_file_content(self, file_path: str) -> Optional[str]:
        """Read file content with encoding detection"""
        try:
            # Try UTF-8 first
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            try:
                # Try latin-1 as fallback
                with open(file_path, 'r', encoding='latin-1') as f:
                    return f.read()
            except Exception:
                logger.debug(f"Could not decode file: {file_path}")
                return None
        except Exception as e:
            logger.debug(f"Could not read file {file_path}: {e}")
            return None
    
    async def _extract_repository_metadata(self, repo_path: str, repo_url: str) -> Dict[str, Any]:
        """Extract comprehensive repository metadata with technology stack analysis"""
        try:
            repo_name = repo_url.split('/')[-1].replace('.git', '')
            owner = repo_url.split('/')[-2] if '/' in repo_url else 'unknown'

            # Get repository size and file count
            total_size = 0
            file_count = 0
            for root, dirs, files in os.walk(repo_path):
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
                        file_count += 1
                    except:
                        continue

            # Enhanced language detection with proper mapping
            language_stats = {}
            language_map = {
                '.py': 'Python', '.js': 'JavaScript', '.ts': 'TypeScript', '.jsx': 'JavaScript',
                '.tsx': 'TypeScript', '.java': 'Java', '.go': 'Go', '.rs': 'Rust',
                '.cpp': 'C++', '.c': 'C', '.php': 'PHP', '.rb': 'Ruby', '.swift': 'Swift',
                '.kt': 'Kotlin', '.scala': 'Scala', '.sh': 'Shell', '.sql': 'SQL',
                '.html': 'HTML', '.css': 'CSS', '.scss': 'SCSS', '.vue': 'Vue',
                '.dart': 'Dart', '.r': 'R', '.m': 'Objective-C'
            }

            for root, dirs, files in os.walk(repo_path):
                for file in files:
                    ext = Path(file).suffix.lower()
                    if ext in language_map:
                        lang = language_map[ext]
                        language_stats[lang] = language_stats.get(lang, 0) + 1

            primary_language = max(language_stats.items(), key=lambda x: x[1])[0] if language_stats else 'Unknown'

            # Extract dependencies and technology stack
            dependencies = await self._extract_dependencies(repo_path)
            tech_stack = await self._analyze_technology_stack(repo_path, language_stats)
            repo_structure = await self._build_repository_structure(repo_path)

            # Read README for description
            description = await self._extract_description(repo_path)

            return {
                "name": repo_name,
                "full_name": f"{owner}/{repo_name}",
                "owner": {"login": owner},
                "html_url": repo_url,
                "clone_url": repo_url,
                "description": description,
                "size": total_size // 1024,  # Size in KB
                "language": primary_language,
                "languages": language_stats,
                "file_count": file_count,
                "processing_method": "local_clone",
                "unlimited_processing": True,
                # Enhanced metadata for frontend
                "dependencies": dependencies,
                "technology_stack": tech_stack,
                "repository_structure": repo_structure
            }

        except Exception as e:
            logger.error(f"Failed to extract repository metadata: {e}")
            return {
                "name": "unknown",
                "processing_method": "local_clone",
                "error": str(e)
            }

    def _should_skip_path(self, path: str) -> bool:
        """Check if a path should be skipped for faster processing"""
        path_lower = path.lower()

        # Skip if any directory component matches skip patterns (not just substring)
        path_parts = Path(path_lower).parts
        for pattern in self.skip_patterns:
            if any(pattern == part or part.startswith(pattern + '.') for part in path_parts):
                return True

        # UNLIMITED: Allow deep nested paths for comprehensive analysis
        # Removed path depth limit

        # Skip files that are too large
        try:
            if os.path.exists(path):
                file_size = os.path.getsize(path)
                file_ext = Path(path).suffix.lower()

                # UNLIMITED: No file size limits for comprehensive analysis
                # Removed all file size restrictions
        except:
            return True

        return False

    def _parse_gitignore(self, repo_path: str) -> Set[str]:
        """
        Parse .gitignore file and return set of patterns to ignore.
        Supports wildcards, directory patterns, and negations.
        """
        gitignore_patterns = set()
        gitignore_path = os.path.join(repo_path, '.gitignore')

        if not os.path.exists(gitignore_path):
            return gitignore_patterns

        try:
            with open(gitignore_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()

                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue

                    # Handle negation patterns (files that should NOT be ignored)
                    if line.startswith('!'):
                        # For now, we'll skip negation patterns (complex to implement)
                        continue

                    # Clean up the pattern
                    pattern = line.rstrip('/')
                    gitignore_patterns.add(pattern)

                    # Also add directory version if not already a directory pattern
                    if not line.endswith('/'):
                        gitignore_patterns.add(pattern + '/')

        except Exception as e:
            logger.warning(f"Failed to parse .gitignore: {e}")

        return gitignore_patterns

    def _should_ignore_by_gitignore(self, rel_path: str, gitignore_patterns: Set[str]) -> bool:
        """
        Check if a file should be ignored based on gitignore patterns.
        Supports wildcards and directory patterns.
        """
        if not gitignore_patterns:
            return False

        # Normalize path separators
        rel_path = rel_path.replace('\\', '/')

        for pattern in gitignore_patterns:
            # Direct match
            if pattern == rel_path:
                return True

            # Directory pattern match
            if pattern.endswith('/'):
                dir_pattern = pattern[:-1]
                if rel_path.startswith(dir_pattern + '/') or rel_path == dir_pattern:
                    return True

            # Wildcard pattern match
            if '*' in pattern or '?' in pattern:
                if fnmatch.fnmatch(rel_path, pattern):
                    return True
                # Also check if any parent directory matches
                path_parts = rel_path.split('/')
                for i in range(len(path_parts)):
                    partial_path = '/'.join(path_parts[:i+1])
                    if fnmatch.fnmatch(partial_path, pattern):
                        return True

            # Check if any parent directory matches the pattern
            path_parts = rel_path.split('/')
            for i in range(len(path_parts)):
                if path_parts[i] == pattern:
                    return True

        return False

    def _should_skip_path_relative(self, file_path: str, repo_path: str) -> bool:
        """Check if a path should be skipped, considering only the relative path within the repo"""
        try:
            # Get relative path from repo root
            rel_path = os.path.relpath(file_path, repo_path)
            rel_path_lower = rel_path.lower()

            # Check if any part of the relative path matches skip patterns
            path_parts = Path(rel_path_lower).parts
            for pattern in self.skip_patterns:
                if any(pattern == part or part.startswith(pattern + '.') for part in path_parts):
                    return True

            # Skip very deep nested paths (likely generated/build files)
            if rel_path.count('/') > 8:
                return True

            # Skip files that are too large
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                file_ext = Path(file_path).suffix.lower()

                # UNLIMITED: No file size limits for comprehensive analysis
                # Removed all file size restrictions

            return False
        except:
            return True

    def _discover_files_optimized(self, repo_path: str) -> List[str]:
        """Discover files with intelligent filtering for speed and gitignore compliance"""
        files = []

        # Parse .gitignore patterns from the repository
        gitignore_patterns = self._parse_gitignore(repo_path)
        logger.info(f"Loaded {len(gitignore_patterns)} .gitignore patterns")

        for root, dirs, filenames in os.walk(repo_path):
            # Skip entire directories that match skip patterns
            dirs[:] = [d for d in dirs if not any(pattern in d.lower() for pattern in self.skip_patterns)]

            for filename in filenames:
                file_path = os.path.join(root, filename)
                rel_path = os.path.relpath(file_path, repo_path)

                # Skip if path should be skipped (relative to repo root)
                if self._should_skip_path_relative(file_path, repo_path):
                    continue

                # Skip if file matches .gitignore patterns
                if self._should_ignore_by_gitignore(rel_path, gitignore_patterns):
                    continue

                # Only include supported extensions (exclude config/system files)
                if Path(filename).suffix.lower() in self.supported_extensions:
                    files.append(file_path)

                # NO LIMITS - Process ALL files in the repository
                # Removed artificial 2000 file limit for unlimited processing

        # Sort by importance (Python, JS, TS files first)
        priority_extensions = ['.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go']
        files.sort(key=lambda f: (
            0 if Path(f).suffix.lower() in priority_extensions else 1,
            len(f),  # Shorter paths first (likely more important)
            f
        ))

        logger.info(f"📁 Discovered {len(files)} files after intelligent filtering")
        if len(files) > 0:
            logger.info(f"📁 Sample files: {files[:5]}")

        return files

    async def _extract_dependencies(self, repo_path: str) -> List[Dict[str, Any]]:
        """Extract and parse dependencies from common package files"""
        dependencies = []

        # Use the same dependency parsing logic as AnalysisService
        from pathlib import Path
        from ..services.analysis_service import AnalysisService

        try:
            # Initialize analysis service for dependency parsing
            analysis_service = AnalysisService()
            repo_path_obj = Path(repo_path)

            # Parse dependencies using the same logic as sequential processing
            parsed_deps = await analysis_service._analyze_dependencies(repo_path_obj)

            # Convert to the format expected by the frontend
            for dep in parsed_deps:
                dependencies.append({
                    'name': dep.get('name', ''),
                    'version': dep.get('version', ''),
                    'language': dep.get('language', ''),
                    'dependency_type': dep.get('dependency_type', 'direct'),
                    'file_path': dep.get('file_path', ''),
                    'mcp_potential': dep.get('mcp_potential', 0.0)
                })

            logger.info(f"📦 Extracted {len(dependencies)} dependencies from {repo_path}")

        except Exception as e:
            logger.warning(f"Failed to parse dependencies: {e}")

            # Fallback: Just detect dependency files without parsing
            dep_files = {
                'package.json': 'npm',
                'requirements.txt': 'pip',
                'Pipfile': 'pipenv',
                'pyproject.toml': 'poetry',
                'Cargo.toml': 'cargo',
                'go.mod': 'go',
                'pom.xml': 'maven',
                'build.gradle': 'gradle'
            }

            for filename, package_manager in dep_files.items():
                file_path = os.path.join(repo_path, filename)
                if os.path.exists(file_path):
                    dependencies.append({
                        'name': f'{package_manager}_dependencies',
                        'version': 'detected',
                        'language': package_manager,
                        'dependency_type': 'file_detected',
                        'file_path': filename,
                        'mcp_potential': 0.5
                    })

        return dependencies

    async def _analyze_technology_stack(self, repo_path: str, language_stats: Dict[str, int]) -> Dict[str, Any]:
        """Analyze technology stack from files and structure"""
        tech_stack = {
            'languages': language_stats,
            'frameworks': [],
            'tools': [],
            'databases': []
        }

        # Check for common framework indicators
        framework_indicators = {
            'react': ['package.json', 'src/App.jsx', 'src/App.tsx'],
            'next.js': ['next.config.js', 'next.config.ts', 'pages/', 'app/'],
            'django': ['manage.py', 'settings.py', 'urls.py'],
            'flask': ['app.py', 'application.py'],
            'fastapi': ['main.py', 'app.py'],
            'express': ['package.json'],
            'spring': ['pom.xml', 'build.gradle']
        }

        for framework, indicators in framework_indicators.items():
            for indicator in indicators:
                if os.path.exists(os.path.join(repo_path, indicator)):
                    tech_stack['frameworks'].append(framework)
                    break

        return tech_stack

    async def _build_repository_structure(self, repo_path: str) -> Dict[str, Any]:
        """Build repository structure overview"""
        structure = {
            'directories': [],
            'key_files': [],
            'total_files': 0
        }

        try:
            for root, dirs, files in os.walk(repo_path):
                # Skip hidden and build directories
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in self.skip_patterns]

                rel_path = os.path.relpath(root, repo_path)
                if rel_path != '.':
                    structure['directories'].append(rel_path)

                structure['total_files'] += len(files)

                # Identify key files
                for file in files:
                    if file in ['README.md', 'package.json', 'requirements.txt', 'Dockerfile', 'docker-compose.yml']:
                        structure['key_files'].append(os.path.join(rel_path, file) if rel_path != '.' else file)

        except Exception as e:
            logger.warning(f"Failed to build repository structure: {e}")

        return structure

    async def _extract_description(self, repo_path: str) -> str:
        """Extract description from README or other documentation"""
        readme_files = ['README.md', 'README.rst', 'README.txt', 'README']

        for readme in readme_files:
            readme_path = os.path.join(repo_path, readme)
            if os.path.exists(readme_path):
                try:
                    with open(readme_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Return first paragraph or first 500 characters
                        lines = content.split('\n')
                        for line in lines:
                            if line.strip() and not line.startswith('#'):
                                return line.strip()[:500]
                        return content[:500]
                except Exception as e:
                    logger.warning(f"Failed to read {readme}: {e}")

        return "No description available"
