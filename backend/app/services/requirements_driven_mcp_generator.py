"""
Requirements-Driven MCP Generator
Ensures MCP server generation is based on actual user requirements and use cases
"""

import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .intelligent_analysis_service import IntelligentAnalysisService
from .real_mcp_generator import RealMCPGenerator
from .repository_code_extractor import RepositoryCodeExtractor

logger = logging.getLogger(__name__)

@dataclass
class UserRequirement:
    """Represents a specific user requirement for MCP functionality"""
    description: str
    category: str  # 'api_integration', 'data_processing', 'workflow_automation', etc.
    priority: str  # 'high', 'medium', 'low'
    expected_inputs: List[str]
    expected_outputs: List[str]
    business_context: str

@dataclass
class RequirementToToolMapping:
    """Maps user requirements to specific repository functions"""
    requirement: UserRequirement
    matched_functions: List[Dict[str, Any]]
    implementation_approach: str
    confidence_score: float
    missing_capabilities: List[str]

class RequirementsDrivenMCPGenerator:
    """Generates MCP servers based on actual user requirements rather than generic templates"""
    
    def __init__(self):
        self.ai_service = IntelligentAnalysisService()
        self.real_generator = RealMCPGenerator()
        self.code_extractor = RepositoryCodeExtractor()
    
    async def generate_requirements_based_mcp(
        self,
        analysis_id: int,
        user_requirements: str,
        repository_context: Dict[str, Any],
        analysis_results: Dict[str, Any],
        target_language: str = "python"
    ) -> Dict[str, Any]:
        """Generate MCP server based on specific user requirements"""
        
        logger.info(f"Generating requirements-driven MCP for analysis {analysis_id}")
        
        # Step 1: Parse and analyze user requirements
        parsed_requirements = await self._parse_user_requirements(
            user_requirements, repository_context
        )
        
        # Step 2: Map requirements to repository capabilities
        requirement_mappings = await self._map_requirements_to_repository(
            parsed_requirements, analysis_results, repository_context
        )
        
        # Step 3: Validate that requirements can be met
        validation_result = await self._validate_requirements_feasibility(
            requirement_mappings, analysis_results
        )
        
        if not validation_result['feasible']:
            return {
                'success': False,
                'error': 'Requirements cannot be met with current repository capabilities',
                'missing_capabilities': validation_result['missing_capabilities'],
                'suggestions': validation_result['suggestions']
            }
        
        # Step 4: Generate targeted tools based on requirements
        targeted_tools = await self._generate_requirement_based_tools(
            requirement_mappings, repository_context
        )
        
        # Step 5: Generate MCP server with requirement-specific implementations
        mcp_server = await self._generate_targeted_mcp_server(
            analysis_id, targeted_tools, repository_context, target_language
        )
        
        return {
            'success': True,
            'requirements_analysis': {
                'parsed_requirements': [req.__dict__ for req in parsed_requirements],
                'requirement_mappings': [mapping.__dict__ for mapping in requirement_mappings],
                'feasibility': validation_result
            },
            'generated_server': mcp_server,
            'tools_generated': len(targeted_tools),
            'requirements_coverage': self._calculate_requirements_coverage(requirement_mappings)
        }
    
    async def _parse_user_requirements(
        self, 
        user_requirements: str, 
        repository_context: Dict[str, Any]
    ) -> List[UserRequirement]:
        """Parse user requirements into structured format"""
        
        prompt = f"""Analyze the following user requirements for an MCP server and extract specific, actionable requirements.

User Requirements: {user_requirements}

Repository Context:
- Name: {repository_context.get('name', 'Unknown')}
- Language: {repository_context.get('language', 'Unknown')}
- Description: {repository_context.get('description', 'No description')}

Extract specific requirements and return as JSON array:
[
  {{
    "description": "Specific requirement description",
    "category": "api_integration|data_processing|workflow_automation|file_management|database_operations|external_service|monitoring|testing",
    "priority": "high|medium|low",
    "expected_inputs": ["input1", "input2"],
    "expected_outputs": ["output1", "output2"],
    "business_context": "Why this requirement is important"
  }}
]

Focus on extracting concrete, implementable requirements that can be mapped to repository functions."""
        
        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=1500)
            
            # Parse JSON response
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                requirements_data = json.loads(json_content)
            else:
                # Fallback parsing
                requirements_data = self._extract_default_requirements(user_requirements)
            
            # Convert to UserRequirement objects
            requirements = []
            for req_data in requirements_data:
                requirement = UserRequirement(
                    description=req_data.get('description', ''),
                    category=req_data.get('category', 'workflow_automation'),
                    priority=req_data.get('priority', 'medium'),
                    expected_inputs=req_data.get('expected_inputs', []),
                    expected_outputs=req_data.get('expected_outputs', []),
                    business_context=req_data.get('business_context', '')
                )
                requirements.append(requirement)
            
            logger.info(f"Parsed {len(requirements)} specific requirements from user input")
            return requirements
            
        except Exception as e:
            logger.error(f"Failed to parse requirements: {str(e)}")
            # NO FALLBACKS - Fail fast if we can't parse requirements properly
            raise Exception(f"Requirements parsing failed: {str(e)}. Cannot generate MCP server without proper requirement analysis.")
    
    async def _map_requirements_to_repository(
        self,
        requirements: List[UserRequirement],
        analysis_results: Dict[str, Any],
        repository_context: Dict[str, Any]
    ) -> List[RequirementToToolMapping]:
        """Map each requirement to specific repository functions"""
        
        mappings = []
        extracted_functions = analysis_results.get('extracted_functions', [])
        api_endpoints = analysis_results.get('api_endpoints', [])
        
        for requirement in requirements:
            # Find matching functions for this requirement
            matched_functions = await self._find_functions_for_requirement(
                requirement, extracted_functions, api_endpoints
            )
            
            # Determine implementation approach
            implementation_approach = await self._determine_implementation_approach(
                requirement, matched_functions, repository_context
            )
            
            # Calculate confidence score
            confidence_score = self._calculate_mapping_confidence(
                requirement, matched_functions
            )
            
            # Identify missing capabilities
            missing_capabilities = await self._identify_missing_capabilities(
                requirement, matched_functions
            )
            
            mapping = RequirementToToolMapping(
                requirement=requirement,
                matched_functions=matched_functions,
                implementation_approach=implementation_approach,
                confidence_score=confidence_score,
                missing_capabilities=missing_capabilities
            )
            
            mappings.append(mapping)
            logger.info(f"Mapped requirement '{requirement.description}' to {len(matched_functions)} functions (confidence: {confidence_score:.2f})")
        
        return mappings
    
    async def _find_functions_for_requirement(
        self,
        requirement: UserRequirement,
        extracted_functions: List[Dict[str, Any]],
        api_endpoints: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Find repository functions that can fulfill a specific requirement"""
        
        matched_functions = []
        
        # Search through extracted functions
        for func in extracted_functions:
            score = self._calculate_function_requirement_match(requirement, func)
            if score > 0.4:  # Minimum threshold
                func_with_score = func.copy()
                func_with_score['match_score'] = score
                func_with_score['match_reason'] = self._explain_match_reason(requirement, func)
                matched_functions.append(func_with_score)
        
        # Search through API endpoints
        for endpoint in api_endpoints:
            score = self._calculate_endpoint_requirement_match(requirement, endpoint)
            if score > 0.4:
                endpoint_func = {
                    'name': f"{endpoint.get('method', 'GET')}_{endpoint.get('path', '').replace('/', '_')}",
                    'type': 'api_endpoint',
                    'endpoint_data': endpoint,
                    'match_score': score,
                    'match_reason': self._explain_endpoint_match_reason(requirement, endpoint)
                }
                matched_functions.append(endpoint_func)
        
        # Sort by match score
        matched_functions.sort(key=lambda x: x.get('match_score', 0), reverse=True)
        
        return matched_functions[:5]  # Return top 5 matches
    
    def _calculate_function_requirement_match(
        self, 
        requirement: UserRequirement, 
        function: Dict[str, Any]
    ) -> float:
        """Calculate how well a function matches a requirement"""
        
        score = 0.0
        
        func_name = function.get('name', '').lower()
        func_docstring = (function.get('docstring') or '').lower()
        req_desc = requirement.description.lower()
        
        # Name matching
        req_keywords = req_desc.split()
        for keyword in req_keywords:
            if len(keyword) > 3:  # Skip short words
                if keyword in func_name:
                    score += 0.3
                if keyword in func_docstring:
                    score += 0.2
        
        # Category matching
        category_keywords = {
            'api_integration': ['api', 'request', 'http', 'client', 'service'],
            'data_processing': ['process', 'transform', 'parse', 'convert', 'analyze'],
            'workflow_automation': ['execute', 'run', 'workflow', 'task', 'job'],
            'file_management': ['file', 'read', 'write', 'save', 'load'],
            'database_operations': ['db', 'database', 'query', 'insert', 'update'],
            'external_service': ['external', 'third_party', 'integration', 'webhook'],
            'monitoring': ['monitor', 'track', 'log', 'metric', 'health'],
            'testing': ['test', 'validate', 'verify', 'check']
        }
        
        category_words = category_keywords.get(requirement.category, [])
        for word in category_words:
            if word in func_name or word in func_docstring:
                score += 0.1
        
        # Input/output matching
        func_params = function.get('parameters', [])
        param_names = [p.get('name', '').lower() for p in func_params]
        
        for expected_input in requirement.expected_inputs:
            for param_name in param_names:
                if expected_input.lower() in param_name:
                    score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_endpoint_requirement_match(
        self, 
        requirement: UserRequirement, 
        endpoint: Dict[str, Any]
    ) -> float:
        """Calculate how well an API endpoint matches a requirement"""
        
        score = 0.0
        
        endpoint_path = endpoint.get('path', '').lower()
        endpoint_method = endpoint.get('method', '').lower()
        req_desc = requirement.description.lower()
        
        # Path matching
        req_keywords = req_desc.split()
        for keyword in req_keywords:
            if len(keyword) > 3 and keyword in endpoint_path:
                score += 0.3
        
        # Method matching based on requirement category
        method_category_match = {
            'api_integration': ['get', 'post', 'put', 'delete'],
            'data_processing': ['post', 'put'],
            'workflow_automation': ['post'],
            'file_management': ['get', 'post', 'put'],
            'database_operations': ['get', 'post', 'put', 'delete']
        }
        
        expected_methods = method_category_match.get(requirement.category, [])
        if endpoint_method in expected_methods:
            score += 0.2
        
        return min(score, 1.0)
    
    def _explain_match_reason(self, requirement: UserRequirement, function: Dict[str, Any]) -> str:
        """Explain why a function matches a requirement"""
        
        reasons = []
        func_name = function.get('name', '')
        func_docstring = function.get('docstring', '')
        
        # Check for keyword matches
        req_keywords = requirement.description.lower().split()
        for keyword in req_keywords:
            if len(keyword) > 3:
                if keyword in func_name.lower():
                    reasons.append(f"Function name contains '{keyword}'")
                if keyword in func_docstring.lower():
                    reasons.append(f"Function documentation mentions '{keyword}'")
        
        if not reasons:
            reasons.append("General functionality alignment")
        
        return "; ".join(reasons[:3])  # Limit to 3 reasons
    
    def _explain_endpoint_match_reason(self, requirement: UserRequirement, endpoint: Dict[str, Any]) -> str:
        """Explain why an endpoint matches a requirement"""
        
        path = endpoint.get('path', '')
        method = endpoint.get('method', '')
        
        return f"API endpoint {method} {path} aligns with requirement category '{requirement.category}'"

    async def _determine_implementation_approach(
        self,
        requirement: UserRequirement,
        matched_functions: List[Dict[str, Any]],
        repository_context: Dict[str, Any]
    ) -> str:
        """Determine the best implementation approach for a requirement"""

        if not matched_functions:
            return "custom_implementation_needed"

        # Check if we have direct function matches
        direct_matches = [f for f in matched_functions if f.get('match_score', 0) > 0.7]
        if direct_matches:
            return "direct_function_wrapper"

        # Check if we have API endpoints
        api_matches = [f for f in matched_functions if f.get('type') == 'api_endpoint']
        if api_matches:
            return "api_endpoint_wrapper"

        # Check if we have partial matches that can be combined
        partial_matches = [f for f in matched_functions if f.get('match_score', 0) > 0.4]
        if len(partial_matches) > 1:
            return "composite_function_wrapper"

        return "adapted_function_wrapper"

    def _calculate_mapping_confidence(
        self,
        requirement: UserRequirement,
        matched_functions: List[Dict[str, Any]]
    ) -> float:
        """Calculate confidence that requirement can be fulfilled"""

        if not matched_functions:
            return 0.0

        # Base confidence on best match score
        best_score = max(f.get('match_score', 0) for f in matched_functions)

        # Boost confidence if we have multiple good matches
        good_matches = [f for f in matched_functions if f.get('match_score', 0) > 0.5]
        if len(good_matches) > 1:
            best_score += 0.1

        # Boost confidence for high-priority requirements with good matches
        if requirement.priority == 'high' and best_score > 0.6:
            best_score += 0.1

        return min(best_score, 1.0)

    async def _identify_missing_capabilities(
        self,
        requirement: UserRequirement,
        matched_functions: List[Dict[str, Any]]
    ) -> List[str]:
        """Identify what capabilities are missing to fulfill the requirement"""

        missing = []

        if not matched_functions:
            missing.append("No matching functions found in repository")
            return missing

        best_match = matched_functions[0] if matched_functions else None
        if not best_match or best_match.get('match_score', 0) < 0.5:
            missing.append("Low confidence match - may need custom implementation")

        # Check for input/output mismatches
        if requirement.expected_inputs:
            func_params = best_match.get('parameters', []) if best_match else []
            param_names = [p.get('name', '') for p in func_params]

            for expected_input in requirement.expected_inputs:
                if not any(expected_input.lower() in param.lower() for param in param_names):
                    missing.append(f"Missing expected input: {expected_input}")

        return missing

    async def _validate_requirements_feasibility(
        self,
        requirement_mappings: List[RequirementToToolMapping],
        analysis_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate that requirements can be feasibly implemented"""

        total_requirements = len(requirement_mappings)
        feasible_requirements = 0
        all_missing_capabilities = []

        for mapping in requirement_mappings:
            if mapping.confidence_score > 0.4:
                feasible_requirements += 1
            all_missing_capabilities.extend(mapping.missing_capabilities)

        feasibility_ratio = feasible_requirements / total_requirements if total_requirements > 0 else 0

        return {
            'feasible': feasibility_ratio >= 0.6,  # At least 60% of requirements can be met
            'feasibility_ratio': feasibility_ratio,
            'feasible_requirements': feasible_requirements,
            'total_requirements': total_requirements,
            'missing_capabilities': list(set(all_missing_capabilities)),
            'suggestions': self._generate_feasibility_suggestions(requirement_mappings)
        }

    def _generate_feasibility_suggestions(
        self,
        requirement_mappings: List[RequirementToToolMapping]
    ) -> List[str]:
        """Generate suggestions for improving feasibility"""

        suggestions = []

        low_confidence_mappings = [m for m in requirement_mappings if m.confidence_score < 0.4]
        if low_confidence_mappings:
            suggestions.append(f"Consider refining {len(low_confidence_mappings)} requirements to better match repository capabilities")

        high_priority_missing = [m for m in requirement_mappings
                               if m.requirement.priority == 'high' and m.confidence_score < 0.5]
        if high_priority_missing:
            suggestions.append("High-priority requirements may need additional repository functions")

        if not requirement_mappings:
            suggestions.append("No requirements could be mapped to repository functions - consider more specific requirements")

        return suggestions

    async def _generate_requirement_based_tools(
        self,
        requirement_mappings: List[RequirementToToolMapping],
        repository_context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate MCP tools based on requirement mappings"""

        tools = []

        for mapping in requirement_mappings:
            if mapping.confidence_score > 0.4:  # Only generate tools for feasible requirements
                tool = await self._create_tool_from_mapping(mapping, repository_context)
                if tool:
                    tools.append(tool)

        return tools

    async def _create_tool_from_mapping(
        self,
        mapping: RequirementToToolMapping,
        repository_context: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Create an MCP tool definition from a requirement mapping"""

        requirement = mapping.requirement
        best_function = mapping.matched_functions[0] if mapping.matched_functions else None

        if not best_function:
            return None

        # Generate tool name from requirement
        tool_name = self._generate_tool_name(requirement)

        # Create tool definition
        tool = {
            'name': tool_name,
            'tool_name': tool_name,
            'description': requirement.description,
            'category': requirement.category,
            'priority': requirement.priority,
            'implementation_approach': mapping.implementation_approach,
            'confidence_score': mapping.confidence_score,
            'business_context': requirement.business_context,
            'expected_inputs': requirement.expected_inputs,
            'expected_outputs': requirement.expected_outputs,
            'source_functions': [best_function.get('name', '')],
            'source_files': [best_function.get('file_path', '')],
            'match_reason': best_function.get('match_reason', ''),
            'requirements_driven': True  # Flag to indicate this is requirements-based
        }

        return tool

    def _generate_tool_name(self, requirement: UserRequirement) -> str:
        """Generate a tool name from requirement description"""

        # Extract key words from description
        words = requirement.description.lower().split()
        key_words = [w for w in words if len(w) > 3 and w not in ['the', 'and', 'for', 'with', 'that', 'this']]

        # Take first 2-3 key words
        tool_words = key_words[:3] if len(key_words) >= 3 else key_words

        # Create tool name
        tool_name = '_'.join(tool_words).replace(' ', '_')

        # Add category prefix if needed
        if requirement.category and not any(cat_word in tool_name for cat_word in requirement.category.split('_')):
            tool_name = f"{requirement.category}_{tool_name}"

        return tool_name[:50]  # Limit length

    async def _generate_targeted_mcp_server(
        self,
        analysis_id: int,
        targeted_tools: List[Dict[str, Any]],
        repository_context: Dict[str, Any],
        target_language: str
    ) -> Dict[str, Any]:
        """Generate MCP server with requirement-specific implementations"""

        # Use the real MCP generator with our targeted tools
        result = await self.real_generator.generate_mcp_server(
            analysis_id=analysis_id,
            repo_url=repository_context.get('url', ''),
            repo_owner=repository_context.get('owner', ''),
            repo_name=repository_context.get('name', ''),
            selected_tools=targeted_tools,
            target_language=target_language,
            github_token=None,
            server_name=f"{repository_context.get('name', 'custom')}-requirements-mcp"
        )

        # Add requirements metadata to the result
        result['requirements_metadata'] = {
            'tools_count': len(targeted_tools),
            'requirements_driven': True,
            'target_language': target_language,
            'generation_approach': 'requirements_based'
        }

        return result

    def _calculate_requirements_coverage(
        self,
        requirement_mappings: List[RequirementToToolMapping]
    ) -> Dict[str, Any]:
        """Calculate how well requirements are covered"""

        total_requirements = len(requirement_mappings)
        if total_requirements == 0:
            return {'coverage_percentage': 0, 'covered_requirements': 0, 'total_requirements': 0}

        covered_requirements = sum(1 for m in requirement_mappings if m.confidence_score > 0.4)
        coverage_percentage = (covered_requirements / total_requirements) * 100

        return {
            'coverage_percentage': round(coverage_percentage, 1),
            'covered_requirements': covered_requirements,
            'total_requirements': total_requirements,
            'high_confidence_mappings': sum(1 for m in requirement_mappings if m.confidence_score > 0.7),
            'average_confidence': sum(m.confidence_score for m in requirement_mappings) / total_requirements
        }

    def _extract_default_requirements(self, user_requirements: str) -> List[Dict[str, Any]]:
        """Extract default requirements when AI parsing fails"""

        return [{
            'description': user_requirements,
            'category': 'workflow_automation',
            'priority': 'high',
            'expected_inputs': ['user_input'],
            'expected_outputs': ['processed_result'],
            'business_context': 'User-specified functionality'
        }]
