"""
Code Indexing Service

Handles indexing of repository code for better context understanding.
Provides progress tracking and re-indexing capabilities.
"""

import asyncio
import hashlib
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from ..database import get_db
from ..models.analysis import RepoAnalysis
from .weaviate_vector_service import WeaviateVectorService
from .intelligent_analysis_service import IntelligentAnalysisService

logger = logging.getLogger(__name__)


class IndexingService:
    """Service for indexing repository code with progress tracking"""
    
    def __init__(self):
        self.chunk_size = 2000  # Increased chunk size for better context
        self.max_file_size = 1000000  # Increased to 1MB for larger files
        self.max_total_files = 1000  # Significantly increased for large repositories
        self.chunk_overlap = 200  # Overlap between chunks for better context
        self.ai_service = IntelligentAnalysisService()
    
    async def index_repository(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Index repository code with progress tracking
        
        Args:
            analysis_id: ID of the analysis record
            repo_content: Repository content from analysis service
            
        Returns:
            Indexing result with status and metadata
        """
        logger.info(f"Starting code indexing for analysis {analysis_id}")
        
        try:
            # Update status to indexing
            await self._update_indexing_status(analysis_id, "indexing", 0)
            
            # Calculate code hash for change detection
            code_hash = self._calculate_code_hash(repo_content)
            
            # Get files to index
            code_samples = repo_content.get("code_samples", {})
            indexable_files = self._filter_indexable_files(code_samples)
            
            total_files = len(indexable_files)
            if total_files == 0:
                await self._update_indexing_status(analysis_id, "completed", 100)
                return {
                    "status": "completed",
                    "files_indexed": 0,
                    "total_chunks": 0,
                    "code_hash": code_hash
                }
            
            # Index files with progress updates using WEAVIATE ONLY
            from app.services.weaviate_vector_service import WeaviateVectorService
            weaviate_service = WeaviateVectorService()

            # CRITICAL: Connect to Weaviate before using it
            await weaviate_service.connect()

            weaviate_result = await weaviate_service.index_repository_code(analysis_id, repo_content)
            total_chunks = weaviate_result.get("chunks_indexed", 0)
            indexed_files = weaviate_result.get("files_processed", 0)

            # Update progress incrementally for better UX
            for progress in range(10, 101, 10):
                await self._update_indexing_status(analysis_id, "indexing", progress)
                await asyncio.sleep(0.2)  # Small delay to make progress visible
            
            # Mark as completed with files and chunks count
            await self._update_indexing_status(analysis_id, "completed", 100, code_hash, indexed_files, total_chunks)
            
            result = {
                "status": "completed",
                "files_indexed": indexed_files,
                "total_chunks": total_chunks,
                "code_hash": code_hash,
                "indexed_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Indexing completed for analysis {analysis_id}: {indexed_files} files, {total_chunks} chunks")
            return result
            
        except Exception as e:
            logger.error(f"Error indexing repository for analysis {analysis_id}: {str(e)}")
            await self._update_indexing_status(analysis_id, "failed", 0)
            raise Exception(f"Code indexing failed: {str(e)}")
    
    async def should_reindex(self, analysis_id: int, repo_content: Dict[str, Any]) -> bool:
        """
        Check if repository should be re-indexed based on code changes
        
        Args:
            analysis_id: ID of the analysis record
            repo_content: Current repository content
            
        Returns:
            True if re-indexing is needed
        """
        try:
            # Get current analysis record
            db = next(get_db())
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            
            if not analysis or not analysis.code_hash:
                return True  # No previous indexing, should index
            
            # Calculate current code hash
            current_hash = self._calculate_code_hash(repo_content)
            
            # Compare with stored hash
            return analysis.code_hash != current_hash
            
        except Exception as e:
            logger.error(f"Error checking reindex status: {str(e)}")
            return True  # Default to reindex on error
    
    async def get_indexing_status(self, analysis_id: int) -> Dict[str, Any]:
        """Get current indexing status for an analysis"""
        try:
            db = next(get_db())
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            
            if not analysis:
                return {"status": "not_found"}
            
            # Get files and chunks count safely
            files_indexed = getattr(analysis, 'files_indexed', 0) or 0
            total_chunks = getattr(analysis, 'total_chunks', 0) or 0

            return {
                "indexing_status": analysis.indexing_status or "never",
                "indexing_progress": analysis.indexing_progress or 0,
                "last_indexed_at": analysis.last_indexed_at.isoformat() if analysis.last_indexed_at else None,
                "vector_db_id": analysis.vector_db_id,
                "files_indexed": files_indexed,
                "total_chunks": total_chunks
            }
            
        except Exception as e:
            logger.error(f"Error getting indexing status: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def _calculate_code_hash(self, repo_content: Dict[str, Any]) -> str:
        """Calculate hash of repository code for change detection"""
        try:
            code_samples = repo_content.get("code_samples", {})
            
            # Create a deterministic string from all code content
            code_string = ""
            for file_path in sorted(code_samples.keys()):
                content = code_samples[file_path]
                if isinstance(content, str):
                    code_string += f"{file_path}:{content}\n"
            
            # Calculate SHA-256 hash
            return hashlib.sha256(code_string.encode('utf-8')).hexdigest()
            
        except Exception as e:
            logger.error(f"Error calculating code hash: {str(e)}")
            return "error"
    
    def _filter_indexable_files(self, code_samples: Dict[str, str]) -> Dict[str, str]:
        """Filter files that should be indexed with intelligent prioritization"""

        # File extensions to index (expanded)
        indexable_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs', '.cpp', '.c', '.h',
            '.php', '.rb', '.swift', '.kt', '.scala', '.clj', '.hs', '.ml', '.r', '.sql',
            '.yaml', '.yml', '.json', '.xml', '.toml', '.ini', '.cfg', '.conf', '.md',
            '.vue', '.svelte', '.dart', '.lua', '.perl', '.sh', '.bash', '.zsh'
        }

        # Files to skip
        skip_patterns = [
            'node_modules/', 'venv/', '.git/', '__pycache__/', '.pytest_cache/',
            'dist/', 'build/', 'target/', '.next/', '.nuxt/', 'coverage/',
            'package-lock.json', 'yarn.lock', 'poetry.lock', 'Pipfile.lock',
            '.min.js', '.min.css', 'bundle.js', 'vendor.js'
        ]

        # Priority files (always include if under size limit)
        priority_patterns = [
            'main.', 'index.', 'app.', 'server.', 'config.', 'settings.',
            'README', 'package.json', 'requirements.txt', 'Cargo.toml',
            'go.mod', 'pom.xml', 'build.gradle'
        ]

        # Categorize files by priority
        priority_files = {}
        regular_files = {}
        large_files = {}

        for file_path, content in code_samples.items():
            # Skip if not string content
            if not isinstance(content, str):
                continue

            # Skip files in excluded directories
            if any(pattern in file_path for pattern in skip_patterns):
                continue

            # Check file extension
            file_ext = '.' + file_path.split('.')[-1] if '.' in file_path else ''
            if file_ext.lower() not in indexable_extensions:
                continue

            file_name = file_path.split('/')[-1].lower()
            content_size = len(content)

            # Categorize by priority and size
            if any(pattern.lower() in file_name for pattern in priority_patterns):
                if content_size <= self.max_file_size:
                    priority_files[file_path] = content
                elif content_size <= self.max_file_size * 2:  # Allow larger priority files
                    large_files[file_path] = content
            elif content_size <= self.max_file_size:
                regular_files[file_path] = content
            elif content_size <= self.max_file_size * 2:
                large_files[file_path] = content

        # Build final selection with intelligent limits
        indexable = {}

        # Always include priority files
        indexable.update(priority_files)

        # Add regular files up to limit
        remaining_slots = max(0, self.max_total_files - len(indexable))
        for file_path, content in list(regular_files.items())[:remaining_slots]:
            indexable[file_path] = content

        # Add some large files if we have remaining slots
        remaining_slots = max(0, self.max_total_files - len(indexable))
        for file_path, content in list(large_files.items())[:min(remaining_slots, 10)]:
            # Truncate large files to manageable size
            indexable[file_path] = content[:self.max_file_size]

        logger.info(f"Selected {len(indexable)} files for indexing: "
                   f"{len(priority_files)} priority, "
                   f"{len([f for f in indexable if f in regular_files])} regular, "
                   f"{len([f for f in indexable if f in large_files])} large (truncated)")

        return indexable
    
    async def _index_file_content(self, analysis_id: int, file_path: str, content: str) -> List[Dict[str, Any]]:
        """Index content of a single file into overlapping chunks for better context"""
        chunks = []

        # Use overlapping chunks for better context preservation
        step_size = self.chunk_size - self.chunk_overlap

        for i in range(0, len(content), step_size):
            chunk_end = min(i + self.chunk_size, len(content))
            chunk_content = content[i:chunk_end]

            # Skip very small chunks at the end
            if len(chunk_content.strip()) < 50:
                break

            chunk = {
                "analysis_id": analysis_id,
                "file_path": file_path,
                "chunk_index": len(chunks),
                "content": chunk_content,
                "start_char": i,
                "end_char": chunk_end,
                "overlap_with_previous": i > 0,
                "file_size": len(content)
            }

            chunks.append(chunk)

        logger.debug(f"Indexed {len(chunks)} overlapping chunks from {file_path} "
                    f"(size: {len(content)} chars, overlap: {self.chunk_overlap})")

        return chunks
    
    async def _update_indexing_status(self, analysis_id: int, status: str,
                                    progress: int, code_hash: Optional[str] = None,
                                    files_indexed: Optional[int] = None,
                                    total_chunks: Optional[int] = None):
        """Update indexing status in database"""
        try:
            db = next(get_db())
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

            if analysis:
                analysis.indexing_status = status
                analysis.indexing_progress = progress

                if status == "completed":
                    analysis.last_indexed_at = datetime.utcnow()
                    if code_hash:
                        analysis.code_hash = code_hash

                # Update files and chunks count if provided
                if files_indexed is not None:
                    # Try to set the attribute, handle gracefully if field doesn't exist yet
                    try:
                        analysis.files_indexed = files_indexed
                    except AttributeError:
                        logger.warning("files_indexed field not available in database model")

                if total_chunks is not None:
                    try:
                        analysis.total_chunks = total_chunks
                    except AttributeError:
                        logger.warning("total_chunks field not available in database model")

                db.commit()
                logger.debug(f"Updated indexing status for analysis {analysis_id}: {status} ({progress}%)")

        except Exception as e:
            logger.error(f"Error updating indexing status: {str(e)}")
    
    async def trigger_reindex(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Trigger re-indexing of repository code"""
        logger.info(f"Triggering re-index for analysis {analysis_id}")
        
        # Reset indexing status
        await self._update_indexing_status(analysis_id, "not_started", 0)
        
        # Start indexing
        return await self.index_repository(analysis_id, repo_content)

    async def search_code_for_capabilities(self, analysis_id: int, query: str) -> List[Dict[str, Any]]:
        """Search indexed code for specific capabilities or functions"""

        try:
            # Use WEAVIATE vector search to find relevant code chunks
            vector_service = WeaviateVectorService()
            await vector_service.connect()  # Connect before using

            search_results = await vector_service.search_similar_code(
                analysis_id=analysis_id,
                query=query,
                limit=10
            )

            # Enhance results with AI analysis
            enhanced_results = []
            for result in search_results:
                enhanced_result = await self._analyze_code_chunk_relevance(
                    code_chunk=result.get('content', ''),
                    query=query,
                    metadata=result.get('metadata', {})
                )
                enhanced_results.append(enhanced_result)

            return enhanced_results

        except Exception as e:
            logger.error(f"Failed to search code capabilities: {str(e)}")
            return []

    async def get_repository_capabilities_summary(self, analysis_id: int) -> Dict[str, Any]:
        """Get a comprehensive summary of repository capabilities"""

        try:
            # Get all indexed code chunks from WEAVIATE
            vector_service = WeaviateVectorService()
            await vector_service.connect()  # Connect before using

            all_chunks = await vector_service.get_all_chunks(analysis_id)

            if not all_chunks:
                return {'capabilities': [], 'summary': 'No code indexed yet'}

            # Analyze capabilities using AI
            capabilities_summary = await self._generate_capabilities_summary(all_chunks)

            return capabilities_summary

        except Exception as e:
            logger.error(f"Failed to get capabilities summary: {str(e)}")
            return {'capabilities': [], 'summary': 'Failed to analyze capabilities'}

    async def answer_repository_question(self, analysis_id: int, question: str) -> str:
        """Answer questions about the repository using indexed code"""

        try:
            # Search for relevant code
            relevant_code = await self.search_code_for_capabilities(analysis_id, question)

            if not relevant_code:
                return "I couldn't find relevant code to answer your question. The repository might not be fully indexed yet."

            # Generate answer using AI
            answer = await self._generate_code_based_answer(question, relevant_code)

            return answer

        except Exception as e:
            logger.error(f"Failed to answer repository question: {str(e)}")
            return "I encountered an error while analyzing the repository code."

    async def _analyze_code_chunk_relevance(self, code_chunk: str, query: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze how relevant a code chunk is to a query"""

        prompt = f"""Analyze this code chunk for relevance to the query: "{query}"

Code chunk:
```
{code_chunk[:1000]}  # Limit to first 1000 chars
```

File: {metadata.get('file_path', 'unknown')}

Rate the relevance (1-10) and explain what capabilities this code provides.
Respond in JSON format:
{{
  "relevance_score": 8,
  "capabilities": ["capability1", "capability2"],
  "description": "Brief description of what this code does",
  "file_path": "{metadata.get('file_path', 'unknown')}"
}}"""

        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=500)
            # Try to parse JSON response
            import json
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                return json.loads(json_content)
            else:
                # Fallback if not JSON
                return {
                    "relevance_score": 5,
                    "capabilities": ["code analysis"],
                    "description": response[:200],
                    "file_path": metadata.get('file_path', 'unknown')
                }
        except Exception as e:
            logger.error(f"Failed to analyze code chunk relevance: {str(e)}")
            return {
                "relevance_score": 3,
                "capabilities": ["unknown"],
                "description": "Failed to analyze code chunk",
                "file_path": metadata.get('file_path', 'unknown')
            }

    async def _generate_capabilities_summary(self, code_chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate a summary of repository capabilities from code chunks"""

        # Sample a subset of chunks for analysis
        sample_chunks = code_chunks[:20]  # Analyze first 20 chunks

        chunks_text = ""
        for i, chunk in enumerate(sample_chunks):
            chunks_text += f"Chunk {i+1} ({chunk.get('metadata', {}).get('file_path', 'unknown')}):\n"
            chunks_text += chunk.get('content', '')[:500] + "\n\n"

        prompt = f"""Analyze these code chunks and provide a comprehensive summary of the repository's capabilities.

Code chunks:
{chunks_text}

Provide a JSON response with:
{{
  "capabilities": ["capability1", "capability2", ...],
  "summary": "Overall description of what this repository can do",
  "main_functions": ["function1", "function2", ...],
  "technologies": ["tech1", "tech2", ...],
  "api_endpoints": ["endpoint1", "endpoint2", ...],
  "business_logic": ["logic1", "logic2", ...]
}}"""

        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=1000)
            import json
            if '```json' in response:
                json_start = response.find('```json') + 7
                json_end = response.find('```', json_start)
                json_content = response[json_start:json_end].strip()
                return json.loads(json_content)
            else:
                return {
                    "capabilities": ["code analysis"],
                    "summary": response[:500],
                    "main_functions": [],
                    "technologies": [],
                    "api_endpoints": [],
                    "business_logic": []
                }
        except Exception as e:
            logger.error(f"Failed to generate capabilities summary: {str(e)}")
            return {
                "capabilities": ["analysis failed"],
                "summary": "Failed to analyze repository capabilities",
                "main_functions": [],
                "technologies": [],
                "api_endpoints": [],
                "business_logic": []
            }

    async def _generate_code_based_answer(self, question: str, relevant_code: List[Dict[str, Any]]) -> str:
        """Generate an answer to a question based on relevant code"""

        code_context = ""
        for i, code_item in enumerate(relevant_code[:5]):  # Use top 5 most relevant
            code_context += f"Code {i+1} (Relevance: {code_item.get('relevance_score', 0)}/10):\n"
            code_context += f"File: {code_item.get('file_path', 'unknown')}\n"
            code_context += f"Description: {code_item.get('description', 'No description')}\n"
            code_context += f"Capabilities: {', '.join(code_item.get('capabilities', []))}\n\n"

        prompt = f"""Based on the following code analysis, answer this question about the repository:

Question: {question}

Relevant code analysis:
{code_context}

Provide a clear, helpful answer based on the actual code capabilities. If the code doesn't contain enough information to answer the question, say so clearly."""

        try:
            response = await self.ai_service._call_ai_service(prompt, max_tokens=800)
            return response
        except Exception as e:
            logger.error(f"Failed to generate code-based answer: {str(e)}")
            return "I encountered an error while analyzing the code to answer your question."

    async def get_code_summary(self, analysis_id: int) -> Dict[str, Any]:
        """Get summary of indexed code for an analysis"""

        try:
            # Get indexed chunks for analysis
            chunks = await self.search_code(analysis_id, "main functions capabilities", max_results=10)

            if not chunks:
                return {
                    'summary': 'No indexed code available',
                    'main_functions': [],
                    'api_endpoints': [],
                    'business_logic': [],
                    'capabilities': []
                }

            # Extract information from chunks
            main_functions = []
            api_endpoints = []
            business_logic = []
            capabilities = []

            for chunk in chunks:
                content = chunk.get('content', '')

                # Extract function names
                import re
                functions = re.findall(r'def\s+(\w+)\s*\(', content)
                main_functions.extend(functions[:3])  # Limit to 3 per chunk

                # Extract API endpoints
                endpoints = re.findall(r'@app\.route\([\'"]([^\'"]+)[\'"]', content)
                endpoints.extend(re.findall(r'@router\.\w+\([\'"]([^\'"]+)[\'"]', content))
                api_endpoints.extend(endpoints[:2])  # Limit to 2 per chunk

                # Extract business logic keywords
                if any(keyword in content.lower() for keyword in ['class', 'business', 'service', 'manager']):
                    business_logic.append(chunk.get('file_path', 'unknown')[:50])

                # Extract capabilities
                if 'def ' in content:
                    capabilities.append(f"Function definitions in {chunk.get('file_path', 'unknown')}")

            # Create summary
            summary = f"Repository contains {len(set(main_functions))} main functions, {len(set(api_endpoints))} API endpoints, and various business logic components."

            return {
                'summary': summary,
                'main_functions': list(set(main_functions))[:10],
                'api_endpoints': list(set(api_endpoints))[:10],
                'business_logic': list(set(business_logic))[:5],
                'capabilities': list(set(capabilities))[:10]
            }

        except Exception as e:
            logger.error(f"Failed to get code summary: {str(e)}")
            return {
                'summary': 'Error retrieving code summary',
                'main_functions': [],
                'api_endpoints': [],
                'business_logic': [],
                'capabilities': []
            }
