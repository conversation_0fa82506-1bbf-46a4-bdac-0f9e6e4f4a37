import requests
from urllib.parse import urlencode
from ..config import settings


class GitHubService:
    """GitHub OAuth and API service"""
    
    def __init__(self):
        self.client_id = settings.github_client_id
        self.client_secret = settings.github_client_secret
        self.redirect_uri = settings.github_redirect_url
        self.base_url = "https://github.com/login/oauth"
        self.api_url = "https://api.github.com"
    
    def get_oauth_url(self) -> str:
        """Generate GitHub OAuth authorization URL"""
        params = {
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "scope": "user:email repo",
            "state": "supermcp-auth"  # Add CSRF protection
        }
        return f"{self.base_url}/authorize?{urlencode(params)}"
    
    def exchange_code_for_token(self, code: str) -> str:
        """Exchange authorization code for access token"""
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "redirect_uri": self.redirect_uri
        }
        
        headers = {
            "Accept": "application/json",
            "User-Agent": "SuperMCP/1.0"
        }
        
        response = requests.post(
            f"{self.base_url}/access_token",
            data=data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to exchange code for token: {response.text}")
        
        token_data = response.json()
        
        if "error" in token_data:
            raise Exception(f"GitHub OAuth error: {token_data.get('error_description', 'Unknown error')}")
        
        return token_data["access_token"]
    
    def get_user_info(self, access_token: str) -> dict:
        """Get user information from GitHub API"""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "SuperMCP/1.0"
        }
        
        response = requests.get(
            f"{self.api_url}/user",
            headers=headers,
            timeout=30
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to get user info: {response.text}")
        
        return response.json()
    
    def get_user_repos(self, access_token: str, per_page: int = 100) -> list:
        """Get user's repositories from GitHub API"""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "SuperMCP/1.0"
        }
        
        repos = []
        page = 1
        
        while len(repos) < 1000:  # Limit to prevent excessive API calls
            response = requests.get(
                f"{self.api_url}/user/repos",
                headers=headers,
                params={
                    "page": page,
                    "per_page": per_page,
                    "sort": "updated",
                    "type": "all"
                },
                timeout=30
            )
            
            if response.status_code != 200:
                break
            
            page_repos = response.json()
            if not page_repos:
                break
            
            repos.extend(page_repos)
            page += 1
        
        return repos
    
    def get_repo_info(self, access_token: str, owner: str, repo: str) -> dict:
        """Get repository information from GitHub API"""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "SuperMCP/1.0"
        }
        
        response = requests.get(
            f"{self.api_url}/repos/{owner}/{repo}",
            headers=headers,
            timeout=30
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to get repo info: {response.text}")
        
        return response.json()

    def get_repo_languages(self, access_token: str, owner: str, repo: str) -> dict:
        """Get repository languages breakdown from GitHub API"""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "SuperMCP/1.0"
        }

        response = requests.get(
            f"{self.api_url}/repos/{owner}/{repo}/languages",
            headers=headers,
            timeout=30
        )

        if response.status_code != 200:
            raise Exception(f"Failed to get repo languages: {response.text}")

        return response.json()

    def get_repo_contents(self, access_token: str, owner: str, repo: str, path: str = "") -> list:
        """Get repository contents from GitHub API"""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "SuperMCP/1.0"
        }
        
        response = requests.get(
            f"{self.api_url}/repos/{owner}/{repo}/contents/{path}",
            headers=headers,
            timeout=30
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to get repo contents: {response.text}")
        
        return response.json()
    
    def get_file_content(self, access_token: str, owner: str, repo: str, path: str) -> str:
        """Get file content from GitHub API"""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3.raw",
            "User-Agent": "SuperMCP/1.0"
        }
        
        response = requests.get(
            f"{self.api_url}/repos/{owner}/{repo}/contents/{path}",
            headers=headers,
            timeout=30
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to get file content: {response.text}")
        
        return response.text
    
    def get_repository_info_sync(self, owner: str, repo: str, access_token: str) -> dict:
        """Synchronous version of get repository information"""
        return self.get_repo_info(access_token, owner, repo)
    
    def get_repository_tree_sync(self, owner: str, repo: str, access_token: str, max_depth: int = 3) -> list:
        """Get repository tree structure synchronously - returns flat list for analysis"""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "SuperMCP/1.0"
        }
        
        # Get the repository tree
        response = requests.get(
            f"{self.api_url}/repos/{owner}/{repo}/git/trees/HEAD",
            headers=headers,
            params={"recursive": "1"},
            timeout=30
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to get repository tree: {response.text}")
        
        tree_data = response.json()
        tree_items = tree_data.get("tree", [])
        
        # Filter and limit based on max_depth - return flat list for analysis
        filtered_items = []
        for item in tree_items:
            path = item.get("path", "")
            depth = len(path.split("/"))
            
            if depth <= max_depth:
                filtered_items.append({
                    "path": path,
                    "type": item.get("type"),
                    "size": item.get("size", 0),
                    "url": item.get("url")
                })
        
        return filtered_items
    
    def get_repository_tree_nested(self, owner: str, repo: str, access_token: str, max_depth: int = 3) -> dict:
        """Get repository tree in nested format for frontend display"""
        # Get flat tree first
        flat_tree = self.get_repository_tree_sync(owner, repo, access_token, max_depth)
        
        # Convert to nested format
        return self._build_nested_tree_from_flat(flat_tree)
    
    def _build_nested_tree_from_flat(self, flat_items: list) -> dict:
        """Build nested tree structure from flat list of items"""
        # Sort by path to ensure proper nesting
        flat_items.sort(key=lambda x: x.get("path", ""))
        
        # Build root node
        root = {
            "name": "root",
            "path": "",
            "type": "dir",
            "children": []
        }
        
        # Track all directories
        directories = {"": root}
        
        for item in flat_items:
            path = item.get("path", "")
            if not path:
                continue
                
            path_parts = path.split("/")
            
            # Create directory structure
            current_path = ""
            for i, part in enumerate(path_parts[:-1]):  # All parts except filename
                if current_path:
                    current_path += "/" + part
                else:
                    current_path = part
                
                if current_path not in directories:
                    # Create directory node
                    parent_path = "/".join(path_parts[:i])
                    parent = directories.get(parent_path, root)
                    
                    dir_node = {
                        "name": part,
                        "path": current_path,
                        "type": "dir",
                        "children": []
                    }
                    parent["children"].append(dir_node)
                    directories[current_path] = dir_node
            
            # Add file node (only for blobs/files)
            if item.get("type") == "blob":
                parent_path = "/".join(path_parts[:-1])
                parent = directories.get(parent_path, root)
                
                file_node = {
                    "name": path_parts[-1],
                    "path": path,
                    "type": "file", 
                    "size": item.get("size", 0),
                    "url": item.get("url")
                }
                parent["children"].append(file_node)
        
        return root

    def _build_nested_tree(self, tree_items: list, max_depth: int = 3) -> dict:
        """Build nested tree structure from flat GitHub tree response"""
        # Filter items by depth
        filtered_items = []
        for item in tree_items:
            path = item.get("path", "")
            depth = len(path.split("/"))
            if depth <= max_depth:
                filtered_items.append(item)
        
        # Sort by path to ensure proper nesting
        filtered_items.sort(key=lambda x: x.get("path", ""))
        
        # Build root node
        root = {
            "name": "root",
            "path": "",
            "type": "dir",
            "children": []
        }
        
        # Track all directories
        directories = {"": root}
        
        for item in filtered_items:
            path = item.get("path", "")
            path_parts = path.split("/")
            
            # Create directory structure
            current_path = ""
            for i, part in enumerate(path_parts[:-1]):  # All parts except filename
                if current_path:
                    current_path += "/" + part
                else:
                    current_path = part
                
                if current_path not in directories:
                    # Create directory node
                    parent_path = "/".join(path_parts[:i])
                    parent = directories.get(parent_path, root)
                    
                    dir_node = {
                        "name": part,
                        "path": current_path,
                        "type": "dir",
                        "children": []
                    }
                    parent["children"].append(dir_node)
                    directories[current_path] = dir_node
            
            # Add file node
            if item.get("type") == "blob":
                parent_path = "/".join(path_parts[:-1])
                parent = directories.get(parent_path, root)
                
                file_node = {
                    "name": path_parts[-1],
                    "path": path,
                    "type": "file",
                    "size": item.get("size", 0),
                    "url": item.get("url")
                }
                parent["children"].append(file_node)
        
        return root
    
    def get_file_content_sync(self, owner: str, repo: str, path: str, access_token: str) -> str:
        """Synchronous version of get file content"""
        return self.get_file_content(access_token, owner, repo, path)