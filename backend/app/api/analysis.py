from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status, BackgroundTasks, Query
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
from celery.result import AsyncResult
from ..database import get_db
from ..models import User, RepoAnalysis, Dependency
from ..schemas.analysis import AnalysisCreate, AnalysisResponse, DependencyResponse, AnalysisStats
from ..services.analysis_service import AnalysisService
from ..utils.auth import get_current_user
from ..tasks.real_analysis import analyze_repository_real
from ..tasks.enhanced_analysis import analyze_repository_enhanced, analyze_repository_with_enhanced_mcps
from ..tasks.parallel_analysis import start_parallel_analysis
import aiohttp
import asyncio
from ..services.github_service import GitHubService
from ..tasks.celery_app import celery_app
from ..services.indexing_service import IndexingService
from ..services.enhanced_tech_stack_analyzer import EnhancedTechStackAnalyzer
from ..config import settings

router = APIRouter()
security = HTTPBearer()
logger = logging.getLogger(__name__)


@router.post("/", response_model=AnalysisResponse)
async def create_analysis(
    analysis_data: AnalysisCreate,
    force_reanalysis: bool = Query(False, description="Force reanalysis of existing repository"),
    parallel_processing: bool = Query(False, description="Use parallel processing with 8 workers (faster for large repos)"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a new repository analysis"""
    
    # Check if repository was already analyzed recently
    if not force_reanalysis:
        existing_analysis = db.query(RepoAnalysis).filter(
            RepoAnalysis.user_id == current_user.id,
            RepoAnalysis.repo_url == str(analysis_data.repo_url),
            RepoAnalysis.status == "completed",
            RepoAnalysis.created_at >= datetime.utcnow() - timedelta(hours=24)  # Within last 24 hours
        ).first()
        
        if existing_analysis:
            return existing_analysis
    
    # Create analysis record
    analysis = RepoAnalysis(
        user_id=current_user.id,
        repo_url=str(analysis_data.repo_url),
        repo_name=analysis_data.repo_name,
        repo_owner=analysis_data.repo_owner,
        status="pending",
        parallel_mode=parallel_processing
    )

    db.add(analysis)
    db.commit()
    db.refresh(analysis)

    # Auto-detect if parallel processing should be used based on repository characteristics
    auto_parallel = await should_use_parallel_processing(analysis.repo_owner, analysis.repo_name, current_user.github_token)

    # Choose processing mode
    if parallel_processing or auto_parallel:
        # Use parallel processing with 8 workers (faster for large repos)
        task = start_parallel_analysis.delay(analysis.id, current_user.github_token)
        mode = "parallel_requested" if parallel_processing else "parallel_auto"
        analysis.analysis_results = {"task_id": task.id, "processing_mode": mode, "workers": 8}
    else:
        # Use traditional single-worker processing
        task = analyze_repository_with_enhanced_mcps.delay(analysis.id, current_user.github_token)
        analysis.analysis_results = {"task_id": task.id, "processing_mode": "sequential", "workers": 1}

    db.commit()
    
    return analysis


@router.get("/", response_model=List[AnalysisResponse])
async def get_user_analyses(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all analyses for the current user"""
    
    analyses = db.query(RepoAnalysis).filter(
        RepoAnalysis.user_id == current_user.id
    ).offset(skip).limit(limit).all()
    
    return analyses


@router.get("/history", response_model=List[AnalysisResponse])
async def get_analysis_history(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    repo_name: Optional[str] = Query(None, description="Filter by repository name"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's analysis history with filtering and pagination"""
    
    query = db.query(RepoAnalysis).filter(RepoAnalysis.user_id == current_user.id)
    
    if status_filter:
        query = query.filter(RepoAnalysis.status == status_filter)
    
    if repo_name:
        query = query.filter(RepoAnalysis.repo_name.ilike(f"%{repo_name}%"))
    
    analyses = query.order_by(desc(RepoAnalysis.created_at)).offset(skip).limit(limit).all()
    
    return analyses


@router.get("/stats", response_model=AnalysisStats)
async def get_analysis_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's analysis statistics"""
    
    # Get counts by status
    stats = db.query(
        RepoAnalysis.status,
        func.count(RepoAnalysis.id).label('count')
    ).filter(
        RepoAnalysis.user_id == current_user.id
    ).group_by(RepoAnalysis.status).all()
    
    # Convert to dict
    status_counts = {stat.status: stat.count for stat in stats}
    
    # Average MCP score calculation removed - using conversational analysis instead
    avg_score = None
    
    print(f"Status Counts: {status_counts}")
    print(f"Average Score: {avg_score}")
    return AnalysisStats(
        total_analyses=sum(status_counts.values()),
        completed_analyses=status_counts.get("completed", 0),
        failed_analyses=status_counts.get("failed", 0),
        pending_analyses=status_counts.get("pending", 0) + status_counts.get("analyzing", 0),
        average_mcp_score=float(avg_score) if avg_score else None
    )


@router.get("/repositories")
async def get_user_repositories(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(30, ge=1, le=100, description="Repositories per page"),
    type_filter: Optional[str] = Query(None, description="Filter by type: all, public, private"),
    sort: str = Query("updated", description="Sort by: updated, created, pushed, full_name"),
    current_user: User = Depends(get_current_user)
):
    """Get user's GitHub repositories for selection"""

    if not current_user.github_token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="GitHub token not found. Please re-authenticate."
        )

    try:
        github_service = GitHubService()

        # Get repositories from GitHub API
        all_repos = github_service.get_user_repos(current_user.github_token, per_page=100)

        # Apply type filter
        if type_filter == "private":
            filtered_repos = [repo for repo in all_repos if repo.get('private', False)]
        elif type_filter == "public":
            filtered_repos = [repo for repo in all_repos if not repo.get('private', False)]
        else:
            filtered_repos = all_repos

        # Sort repositories
        if sort == "updated":
            filtered_repos.sort(key=lambda x: x.get('updated_at', ''), reverse=True)
        elif sort == "created":
            filtered_repos.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        elif sort == "pushed":
            filtered_repos.sort(key=lambda x: x.get('pushed_at', ''), reverse=True)
        elif sort == "full_name":
            filtered_repos.sort(key=lambda x: x.get('full_name', '').lower())

        # Paginate results
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        paginated_repos = filtered_repos[start_idx:end_idx]

        # Format response
        repositories = []
        for repo in paginated_repos:
            repositories.append({
                "id": repo.get('id'),
                "name": repo.get('name'),
                "full_name": repo.get('full_name'),
                "description": repo.get('description'),
                "private": repo.get('private', False),
                "language": repo.get('language'),
                "stars": repo.get('stargazers_count', 0),
                "forks": repo.get('forks_count', 0),
                "updated_at": repo.get('updated_at'),
                "created_at": repo.get('created_at'),
                "pushed_at": repo.get('pushed_at'),
                "html_url": repo.get('html_url'),
                "clone_url": repo.get('clone_url'),
                "size": repo.get('size', 0)
            })

        return {
            "repositories": repositories,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": len(filtered_repos),
                "total_pages": (len(filtered_repos) + per_page - 1) // per_page,
                "has_next": end_idx < len(filtered_repos),
                "has_prev": page > 1
            },
            "summary": {
                "total_repos": len(all_repos),
                "private_repos": len([r for r in all_repos if r.get('private', False)]),
                "public_repos": len([r for r in all_repos if not r.get('private', False)])
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch repositories: {str(e)}"
        )


@router.get("/{analysis_id}", response_model=AnalysisResponse)
async def get_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get analysis results by ID"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    return analysis


@router.delete("/{analysis_id}")
async def delete_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete an analysis"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    db.delete(analysis)
    db.commit()
    
    return {"message": "Analysis deleted successfully"}


@router.get("/{analysis_id}/status")
async def get_analysis_status(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get real-time analysis status and progress"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    # Calculate elapsed time and health status
    from datetime import timezone
    now = datetime.now(timezone.utc)

    # Ensure both datetimes are timezone-aware
    created_at = analysis.created_at
    if created_at.tzinfo is None:
        created_at = created_at.replace(tzinfo=timezone.utc)

    elapsed_seconds = (now - created_at).total_seconds()

    health_status = "healthy"
    health_message = None

    if analysis.status in ['analyzing', 'pending']:
        if elapsed_seconds > 600:  # 10 minutes
            health_status = "warning"
            health_message = "Analysis is taking longer than expected. This may indicate the repository is large or there are network issues."
        elif elapsed_seconds > 900:  # 15 minutes
            health_status = "critical"
            health_message = "Analysis may be stuck. Consider retrying the analysis."

    response = {
        "analysis_id": analysis_id,
        "status": analysis.status,
        "created_at": analysis.created_at,
        "updated_at": analysis.updated_at,
        "completed_at": analysis.completed_at,
        "error_message": analysis.error_message,
        "elapsed_seconds": int(elapsed_seconds),
        "health_status": health_status,
        "health_message": health_message
    }
    
    # Get task progress if available
    task_id = None
    if analysis.analysis_results and isinstance(analysis.analysis_results, dict):
        task_id = analysis.analysis_results.get("task_id")
    
    if task_id:
        try:
            task_result = AsyncResult(task_id, app=celery_app)
            response.update({
                "task_id": task_id,
                "task_status": task_result.status,
                "task_info": task_result.info
            })
        except Exception as e:
            response["task_error"] = str(e)
    
    return response


@router.get("/{analysis_id}/dependencies", response_model=List[DependencyResponse])
async def get_analysis_dependencies(
    analysis_id: int,
    language: Optional[str] = Query(None, description="Filter by programming language"),
    dependency_type: Optional[str] = Query(None, description="Filter by dependency type"),
    min_mcp_potential: Optional[float] = Query(None, description="Minimum MCP potential score"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get dependencies found in the analysis"""
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    # Build query with filters
    query = db.query(Dependency).filter(Dependency.analysis_id == analysis_id)
    
    if language:
        query = query.filter(Dependency.language == language)
    
    if dependency_type:
        query = query.filter(Dependency.dependency_type == dependency_type)
    
    if min_mcp_potential is not None:
        query = query.filter(Dependency.mcp_potential >= min_mcp_potential)
    
    dependencies = query.order_by(desc(Dependency.mcp_potential)).all()
    
    return dependencies


@router.post("/{analysis_id}/retry")
async def retry_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Retry a failed analysis"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    if analysis.status not in ["failed", "completed", "analyzing"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only retry failed, completed, or stuck analyses"
        )

    # Clean up any existing task if retrying a stuck analysis
    if analysis.analysis_results and isinstance(analysis.analysis_results, dict):
        old_task_id = analysis.analysis_results.get("task_id")
        if old_task_id:
            try:
                from celery.result import AsyncResult
                old_task = AsyncResult(old_task_id, app=celery_app)
                old_task.revoke(terminate=True)
            except Exception:
                pass  # Ignore cleanup errors
    
    # Reset analysis status
    analysis.status = "pending"
    analysis.error_message = None
    analysis.completed_at = None
    
    # Clear previous results but keep dependencies for reference
    analysis.analysis_results = None
    # MCP feasibility score removed - using conversational analysis instead
    
    db.commit()
    
    # Start new analysis task (use enhanced analysis if any AI API key is available)
    if settings.anthropic_api_key or settings.openai_api_key:
        task = analyze_repository_enhanced.delay(analysis.id, current_user.github_token)
    else:
        task = analyze_repository_real.delay(analysis.id, current_user.github_token)
    
    # Store new task ID
    analysis.analysis_results = {"task_id": task.id}
    db.commit()
    
    return {"message": "Analysis retry started", "task_id": task.id}


@router.get("/{analysis_id}/export")
async def export_analysis(
    analysis_id: int,
    format: str = Query("json", regex="^(json|csv)$", description="Export format"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Export analysis results in different formats"""
    
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    if analysis.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only export completed analyses"
        )
    
    # Get dependencies
    dependencies = db.query(Dependency).filter(Dependency.analysis_id == analysis_id).all()
    
    export_data = {
        "analysis": {
            "id": analysis.id,
            "repo_url": analysis.repo_url,
            "repo_name": analysis.repo_name,
            "repo_owner": analysis.repo_owner,
            # "mcp_feasibility_score" removed - using conversational analysis instead
            "status": analysis.status,
            "created_at": analysis.created_at.isoformat(),
            "completed_at": analysis.completed_at.isoformat() if analysis.completed_at else None,
            "analysis_results": analysis.analysis_results
        },
        "dependencies": [
            {
                "name": dep.name,
                "version": dep.version,
                "language": dep.language,
                "dependency_type": dep.dependency_type,
                "file_path": dep.file_path,
                "mcp_potential": dep.mcp_potential,
                "existing_mcp_servers": dep.existing_mcp_servers
            }
            for dep in dependencies
        ]
    }
    
    if format == "json":
        return export_data
    elif format == "csv":
        # For CSV, flatten the data structure
        import io
        import csv
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            "Analysis ID", "Repository", "MCP Score", "Dependency Name", 
            "Version", "Language", "Type", "File Path", "MCP Potential"
        ])
        
        # Write data rows
        for dep in dependencies:
            writer.writerow([
                analysis.id,
                f"{analysis.repo_owner}/{analysis.repo_name}",
                # analysis.mcp_feasibility_score removed - using conversational analysis instead
                None,
                dep.name,
                dep.version,
                dep.language,
                dep.dependency_type,
                dep.file_path,
                dep.mcp_potential
            ])
        
        return {"csv_data": output.getvalue()}

    return export_data


@router.get("/{analysis_id}/indexing-status")
async def get_indexing_status(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current indexing status for an analysis"""

    # Verify analysis belongs to user
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    indexing_service = IndexingService()
    status_info = await indexing_service.get_indexing_status(analysis_id)

    return status_info


@router.post("/{analysis_id}/reindex")
async def reindex_repository(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Trigger re-indexing of repository code"""

    # Verify analysis belongs to user
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    if not analysis.analysis_results:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis must be completed before indexing"
        )

    # Get repository content from analysis results
    repo_content = analysis.analysis_results

    indexing_service = IndexingService()
    result = await indexing_service.trigger_reindex(analysis_id, repo_content)

    return {
        "message": "Re-indexing started",
        "result": result
    }


@router.get("/{analysis_id}/integrations")
async def get_detected_integrations(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detected third-party integrations for an analysis"""

    # Verify analysis belongs to user
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    # Get detected integrations from analysis results
    detected_integrations = []
    mcp_alternatives = {}

    if analysis.analysis_results:
        detected_integrations = analysis.analysis_results.get("detected_integrations", [])
        mcp_alternatives = analysis.analysis_results.get("mcp_alternatives", {})

    return {
        "detected_integrations": detected_integrations,
        "mcp_alternatives": mcp_alternatives,
        "total_integrations": len(detected_integrations),
        "has_alternatives": len(mcp_alternatives) > 0
    }


@router.get("/{analysis_id}/tech-stack")
async def get_tech_stack_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get enhanced tech stack analysis with confidence scores"""

    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")

    if analysis.status != 'completed':
        raise HTTPException(status_code=400, detail="Analysis not completed yet")

    try:
        # Get enhanced tech stack analysis
        tech_analyzer = EnhancedTechStackAnalyzer()

        # Extract repository info from analysis
        repo_info = analysis.analysis_results.get('repository_info', {})

        # For now, return enhanced analysis based on existing data
        # In a full implementation, this would re-analyze the repository
        enhanced_analysis = {
            "primary_language": repo_info.get('language', 'Unknown'),
            "languages": analysis.analysis_results.get('code_structure', {}).get('languages', {}),
            "frameworks": analysis.analysis_results.get('frameworks', []),
            "dependencies_count": len(analysis.dependencies),
            "confidence_score": 85.0,  # Calculate based on detection methods
            "detection_methods": [
                "GitHub API Language Detection",
                "Dependency File Analysis",
                "Code Structure Analysis",
                "File Extension Analysis"
            ],
            "validation_status": "pending",
            "last_updated": analysis.updated_at.isoformat() if analysis.updated_at else None
        }

        return enhanced_analysis

    except Exception as e:
        logger.error(f"Failed to get tech stack analysis: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to analyze tech stack")


@router.post("/{analysis_id}/tech-stack/validate")
async def validate_tech_stack(
    analysis_id: int,
    validation_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Validate tech stack analysis accuracy"""

    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")

    try:
        # Update validation status in analysis results
        if not analysis.analysis_results:
            analysis.analysis_results = {}

        analysis.analysis_results['tech_stack_validation'] = {
            "status": "validated",
            "validated_by": current_user.id,
            "validated_at": datetime.utcnow().isoformat(),
            "validation_data": validation_data
        }

        db.commit()

        return {"message": "Tech stack validation recorded successfully"}

    except Exception as e:
        logger.error(f"Failed to validate tech stack: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to record validation")


@router.post("/{analysis_id}/tech-stack/correct")
async def correct_tech_stack(
    analysis_id: int,
    corrections: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Submit corrections for tech stack analysis"""

    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")

    try:
        # Update analysis with corrections
        if not analysis.analysis_results:
            analysis.analysis_results = {}

        analysis.analysis_results['tech_stack_corrections'] = {
            "corrected_by": current_user.id,
            "corrected_at": datetime.utcnow().isoformat(),
            "corrections": corrections,
            "status": "needs_review"
        }

        db.commit()

        return {"message": "Tech stack corrections submitted successfully"}

    except Exception as e:
        logger.error(f"Failed to submit corrections: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to submit corrections")


async def should_use_parallel_processing(repo_owner: str, repo_name: str, github_token: str) -> bool:
    """
    Intelligently determine if a repository should use parallel processing
    based on repository characteristics, not hardcoded lists.
    """
    try:
        headers = {"Authorization": f"token {github_token}"}

        async with aiohttp.ClientSession() as session:
            # Get repository metadata
            repo_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}"
            async with session.get(repo_url, headers=headers) as response:
                if response.status != 200:
                    logger.warning(f"Failed to get repo info for {repo_owner}/{repo_name}, defaulting to sequential")
                    return False

                repo_data = await response.json()

                # Criteria for parallel processing (any of these triggers parallel mode)
                criteria_met = []

                # 1. Repository size (> 10MB suggests many files)
                size_kb = repo_data.get('size', 0)
                if size_kb > 10000:  # 10MB+
                    criteria_met.append(f"Large size: {size_kb}KB")

                # 2. High star count (popular repos are usually complex)
                stars = repo_data.get('stargazers_count', 0)
                if stars > 1000:
                    criteria_met.append(f"Popular: {stars} stars")

                # 3. Many forks (indicates active development)
                forks = repo_data.get('forks_count', 0)
                if forks > 100:
                    criteria_met.append(f"Active: {forks} forks")

                # 4. Language indicators (complex tech stacks)
                language = repo_data.get('language', '').lower()
                complex_languages = ['typescript', 'javascript', 'python', 'java', 'go', 'rust']
                if language in complex_languages:
                    # Get additional language stats
                    languages_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}/languages"
                    async with session.get(languages_url, headers=headers) as lang_response:
                        if lang_response.status == 200:
                            languages = await lang_response.json()
                            if len(languages) >= 3:  # Multi-language projects
                                criteria_met.append(f"Multi-language: {len(languages)} languages")

                # 5. Framework indicators from topics
                topics = repo_data.get('topics', [])
                framework_topics = ['react', 'vue', 'angular', 'django', 'flask', 'fastapi', 'express', 'nextjs', 'nuxt']
                if any(topic in framework_topics for topic in topics):
                    criteria_met.append(f"Framework project: {topics}")

                # 6. Recent activity (active repos tend to be larger)
                from datetime import datetime, timedelta
                updated_at = repo_data.get('updated_at')
                if updated_at:
                    try:
                        last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        if datetime.now().astimezone() - last_update < timedelta(days=30):
                            criteria_met.append("Recently active")
                    except:
                        pass

                # Decision logic: use parallel if any significant criteria are met
                use_parallel = len(criteria_met) >= 2  # At least 2 criteria

                if use_parallel:
                    logger.info(f"🚀 Auto-enabling parallel processing for {repo_owner}/{repo_name}")
                    logger.info(f"📊 Criteria met: {', '.join(criteria_met)}")
                else:
                    logger.info(f"📝 Using sequential processing for {repo_owner}/{repo_name}")
                    if criteria_met:
                        logger.info(f"📊 Some criteria met: {', '.join(criteria_met)} (need 2+ for parallel)")

                return use_parallel

    except Exception as e:
        logger.error(f"Error in parallel processing detection: {e}")
        # Default to sequential processing on error
        return False