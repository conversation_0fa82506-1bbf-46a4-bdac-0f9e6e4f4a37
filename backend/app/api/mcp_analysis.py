from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import asyncio
import re
from app.services.mcp_analysis_service import MCPAnalysisService, MCPAnalysisResult
from app.services.mcp_chat_service import MCPChatService
from app.services.enhanced_mcp_conversation_service import EnhancedMCPConversationService
from app.services.mcp_workflow_integration_service import MCPWorkflowIntegrationService
from app.tasks.mcp_analysis_task import get_mcp_analysis_result, analyze_mcp_opportunities_task

router = APIRouter()
mcp_analysis_service = MCPAnalysisService()
mcp_chat_service = MCPChatService()
enhanced_conversation_service = EnhancedMCPConversationService()
workflow_integration_service = MCPWorkflowIntegrationService()

def clean_response(response: str) -> str:
    """Clean up response by removing pseudo-code blocks and separating mixed content."""
    import re

    # Remove empty code blocks
    response = re.sub(r'```\w*\s*\n\s*\n```', '', response)
    response = re.sub(r'```\w*\s*```', '', response)

    # Remove code blocks that only contain whitespace or minimal content
    response = re.sub(r'```\w*\s*\n\s*[\s\n]*\n```', '', response)

    # Function to analyze and clean code blocks
    def clean_code_block(match):
        language = match.group(1) or ''
        code_content = match.group(2)

        if not code_content.strip():
            return ''

        lines = [line.rstrip() for line in code_content.split('\n')]
        non_empty_lines = [line for line in lines if line.strip()]

        if not non_empty_lines:
            return ''

        # Separate code from comments/descriptions
        pure_code_lines = []
        extracted_descriptions = []

        for line in non_empty_lines:
            stripped = line.strip()

            # Skip pass statements
            if stripped == 'pass':
                continue

            # Extract comments and convert to descriptions
            if stripped.startswith('#'):
                comment_text = stripped.lstrip('# ').strip()
                if comment_text and len(comment_text) > 3:
                    extracted_descriptions.append(comment_text)
                continue

            # Check if line contains actual code (not just declarations)
            is_actual_code = (
                '=' in stripped or
                stripped.startswith(('import ', 'from ', 'return ', 'yield ', 'raise ', 'await ', 'async ')) or
                any(op in stripped for op in ['->', '=>', '==', '!=', '<=', '>=', '&&', '||']) or
                stripped.endswith((';', '{', '}', ')', ']')) or
                re.match(r'^\s*[a-zA-Z_][a-zA-Z0-9_]*\s*\(.*\)\s*[{;]?\s*$', stripped) or  # function calls
                ('(' in stripped and ')' in stripped and not stripped.endswith(':'))  # method calls
            )

            # Keep function/class declarations and actual code
            if (stripped.endswith(':') and any(keyword in stripped for keyword in ['def ', 'class ', 'async def ', 'if ', 'for ', 'while ', 'try:', 'except', 'with '])) or is_actual_code:
                pure_code_lines.append(line)

        # Count meaningful code lines
        meaningful_code_lines = len([line for line in pure_code_lines if not line.strip().endswith(':')])

        # If there's no meaningful code, convert to descriptive text
        if meaningful_code_lines < 2:
            if extracted_descriptions:
                return '\n'.join(f"- {desc}" for desc in extracted_descriptions)
            else:
                return ''  # Remove empty blocks

        # If we have good code, keep it as a clean code block
        if pure_code_lines:
            clean_code = '\n'.join(pure_code_lines)
            result = f"```{language}\n{clean_code}\n```"

            # Add descriptions as separate text if any
            if extracted_descriptions:
                descriptions_text = '\n'.join(f"- {desc}" for desc in extracted_descriptions)
                result = f"{descriptions_text}\n\n{result}"

            return result

        return ''

    # Apply cleaning to all code blocks
    response = re.sub(r'```(\w+)?\s*\n(.*?)\n```', clean_code_block, response, flags=re.DOTALL)

    # Remove excessive newlines
    response = re.sub(r'\n{3,}', '\n\n', response)

    # Clean up any remaining empty sections
    response = re.sub(r'\n\s*\n\s*\n', '\n\n', response)

    return response.strip()

def detect_user_intent(user_message: str) -> str:
    """Detect user intent to show appropriate buttons."""
    message_lower = user_message.lower()

    # MCP Generation Intent
    build_keywords = ['build', 'create', 'generate', 'make', 'develop']
    mcp_keywords = ['mcp', 'server', 'tool', 'integration']

    if any(keyword in message_lower for keyword in build_keywords) and \
       any(keyword in message_lower for keyword in mcp_keywords):
        return 'build_mcp'

    # Learning/Explanation Intent
    learn_keywords = ['what', 'how', 'explain', 'show', 'example', 'tell me about']
    if any(keyword in message_lower for keyword in learn_keywords):
        return 'learn'

    # Troubleshooting Intent
    trouble_keywords = ['error', 'issue', 'problem', 'fix', 'debug', 'not working', 'failed']
    if any(keyword in message_lower for keyword in trouble_keywords):
        return 'troubleshoot'

    return 'general'

class StartMCPAnalysisRequest(BaseModel):
    repo_url: str

class MCPChatRequest(BaseModel):
    message: str
    conversation_history: Optional[List[Dict[str, str]]] = None

class MCPServerGenerationRequest(BaseModel):
    repository: Dict[str, str]
    analysis_data: Optional[Dict] = None
    mcp_analysis: Optional[Dict] = None
    chat_context: str
    user_requirements: str

class MCPButtonActionRequest(BaseModel):
    action: str  # "generate" or "continue"
    server_name: str
    requirements: str
    conversation_history: Optional[List[Dict[str, str]]] = None

class ActionButtonRequest(BaseModel):
    action_text: str
    conversation_history: Optional[List[Dict[str, str]]] = None

class MCPServerGenerateRequest(BaseModel):
    prompt: str

class MCPServerTestRequest(BaseModel):
    server_code: str
    package_json: str
    user_prompt: Optional[str] = None

class MCPServerDownloadRequest(BaseModel):
    server: Dict[str, Any]

class MCPAnalysisResponse(BaseModel):
    success: bool
    data: Optional[dict] = None
    message: Optional[str] = None
    error: Optional[str] = None

class MCPChatResponse(BaseModel):
    success: bool
    response: Optional[str] = None
    error: Optional[str] = None
    generated_server: Optional[Dict] = None
    is_generation: Optional[bool] = False
    show_mcp_buttons: Optional[bool] = False
    server_name: Optional[str] = None
    requirements: Optional[str] = None
    show_action_buttons: Optional[bool] = False
    action_buttons: Optional[List[str]] = None

class MCPServerResponse(BaseModel):
    success: bool
    server: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class MCPTestResponse(BaseModel):
    success: bool
    test_results: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None

@router.get("/analysis/{analysis_id}/mcp")
async def get_mcp_analysis(analysis_id: str) -> MCPAnalysisResponse:
    """Get MCP analysis results for a repository."""
    try:
        # Check if analysis exists in cache
        result = get_mcp_analysis_result(int(analysis_id))
        if result:
            return MCPAnalysisResponse(
                success=True,
                data=result  # result is already a dict from Redis
            )
        else:
            # Return loading state if analysis not found
            return MCPAnalysisResponse(
                success=True,
                data={
                    "executive_summary": "MCP analysis in progress...",
                    "capability_matrix": [],
                    "new_mcp_server_specs": [],
                    "existing_mcp_servers": [],
                    "gap_analysis": "Analyzing repository capabilities...",
                    "implementation_starter": "# Analysis in progress...",
                    "client_config_snippet": '{"mcps": {}}'
                }
            )
    except Exception as e:
        print(f"Error getting MCP analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to get MCP analysis")

@router.post("/analysis/{analysis_id}/mcp/start")
async def start_mcp_analysis(
    analysis_id: str,
    request: StartMCPAnalysisRequest,
    background_tasks: BackgroundTasks
) -> MCPAnalysisResponse:
    """Start MCP analysis for a repository."""
    try:
        # Start the analysis as a Celery task
        task = analyze_mcp_opportunities_task.delay(int(analysis_id), request.repo_url)

        return MCPAnalysisResponse(
            success=True,
            message=f"MCP analysis started with task ID: {task.id}"
        )
    except Exception as e:
        print(f"Error starting MCP analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to start MCP analysis")

@router.post("/analysis/{analysis_id}/mcp/chat")
async def mcp_chat(analysis_id: str, request: MCPChatRequest) -> MCPChatResponse:
    """Chat with MCP assistant using full repository context."""
    try:
        response = await mcp_chat_service.chat_with_context(
            int(analysis_id),
            request.message,
            request.conversation_history
        )

        # Clean up the response to remove empty code blocks
        response = clean_response(response)

        # Simple detection: only look for MCP generation buttons
        if "SHOW_MCP_BUTTONS:" in response:
            # Extract generation details
            lines = response.split('\n')
            server_name = ""
            requirements = ""

            # Find server name
            for line in lines:
                if line.startswith("SHOW_MCP_BUTTONS:"):
                    server_name = line.replace("SHOW_MCP_BUTTONS:", "").strip()
                    break

            # Extract requirements (everything after REQUIREMENTS: line)
            requirements_started = False
            requirements_lines = []
            for line in lines:
                if line.startswith("REQUIREMENTS:"):
                    requirements_started = True
                    # Include the content after REQUIREMENTS: on the same line if any
                    req_content = line.replace("REQUIREMENTS:", "").strip()
                    if req_content:
                        requirements_lines.append(req_content)
                elif requirements_started and line.strip():
                    # Stop collecting if we hit another marker
                    if line.startswith("GENERATE_MCP_SERVER:") or line.startswith("SHOW_MCP_BUTTONS:"):
                        break
                    requirements_lines.append(line.strip())

            requirements = "\n".join(requirements_lines).strip()

            # Clean the response by removing the markers
            cleaned_response = response
            for line in lines:
                if line.startswith("SHOW_MCP_BUTTONS:") or line.startswith("REQUIREMENTS:"):
                    cleaned_response = cleaned_response.replace(line, "").strip()

            # Remove any remaining requirement lines from the clean response
            clean_lines = []
            skip_mode = False
            for line in cleaned_response.split('\n'):
                if line.startswith("REQUIREMENTS:"):
                    skip_mode = True
                    continue
                elif skip_mode and line.strip():
                    # Skip all lines that are part of requirements (start with -, *, or are indented)
                    if line.startswith("-") or line.startswith("*") or line.startswith("  "):
                        continue
                    else:
                        # If we hit a non-requirement line, stop skipping
                        skip_mode = False

                if not skip_mode:
                    clean_lines.append(line)

            cleaned_response = "\n".join(clean_lines).strip()

            # Additional cleanup: remove any standalone requirement-like lines
            cleaned_response = '\n'.join([
                line for line in cleaned_response.split('\n')
                if not (line.strip().startswith('- ') and any(keyword in line.lower() for keyword in ['api', 'integration', 'implementation', 'connection', 'middleware', 'handling', 'parsing', 'security', 'rate limiting', 'caching']))
            ])

            print(f"DEBUG: Extracted server_name='{server_name}', requirements='{requirements[:100]}...'")
            print(f"DEBUG: Cleaned response preview: '{cleaned_response[:200]}...'")

            return MCPChatResponse(
                success=True,
                response=cleaned_response,
                show_mcp_buttons=True,
                server_name=server_name,
                requirements=requirements
            )

        # Check if the AI wants to generate an MCP server directly
        elif "GENERATE_MCP_SERVER:" in response:
            # Extract generation details
            lines = response.split('\n')
            server_name = ""
            requirements = ""

            for line in lines:
                if line.startswith("GENERATE_MCP_SERVER:"):
                    server_name = line.replace("GENERATE_MCP_SERVER:", "").strip()
                elif line.startswith("REQUIREMENTS:"):
                    requirements = line.replace("REQUIREMENTS:", "").strip()

            # Generate the MCP server
            from app.services.mcp_server_generator import MCPServerGenerator
            generator = MCPServerGenerator()

            # Get repository and analysis data
            from app.database import SessionLocal
            from app.models import RepoAnalysis
            from app.tasks.mcp_analysis_task import get_mcp_analysis_result

            db = SessionLocal()
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == int(analysis_id)).first()
            mcp_analysis = get_mcp_analysis_result(int(analysis_id))
            db.close()

            generation_context = {
                "repository": {
                    "name": analysis.repo_name if analysis else "repository",
                    "owner": analysis.repo_owner if analysis else "owner",
                    "url": analysis.repo_url if analysis else ""
                },
                "analysis_data": analysis.analysis_results if analysis else {},
                "mcp_analysis": mcp_analysis.__dict__ if hasattr(mcp_analysis, '__dict__') else (mcp_analysis if isinstance(mcp_analysis, dict) else {}),
                "chat_context": '\n'.join([msg.get('content', '') for msg in (request.conversation_history or [])]),
                "user_requirements": requirements,
                "server_name": server_name
            }

            try:
                generation_result = await generator.generate_server(**generation_context)

                # Format the response with generated code and download links
                formatted_response = f"""🎉 **MCP Server Generated Successfully!**

**{server_name}** has been created for your repository.

## Generated Code:
```python
{generation_result['server_code']}
```

## Installation Instructions:
{generation_result['installation_instructions']}

## Usage Example:
```bash
{generation_result['usage_example']}
```

The MCP server is ready to use! The files have been generated and are ready for download."""

                return MCPChatResponse(
                    success=True,
                    response=formatted_response,
                    generated_server=generation_result,
                    is_generation=True
                )
            except Exception as gen_error:
                print(f"Error generating MCP server: {gen_error}")
                error_response = f"❌ Sorry, I encountered an error while generating the MCP server: {str(gen_error)}\n\nPlease try again or provide more specific requirements."
                return MCPChatResponse(
                    success=True,
                    response=error_response
                )

        # No fallback buttons - let the conversation be natural
        # Only show buttons when AI explicitly requests them

        return MCPChatResponse(
            success=True,
            response=response
        )
    except Exception as e:
        print(f"Error in MCP chat: {e}")
        raise HTTPException(status_code=500, detail="Failed to process chat request")

# Removed duplicate endpoint - using the simpler playground endpoint below

@router.post("/analysis/{analysis_id}/mcp/button-action")
async def handle_mcp_button_action(analysis_id: str, request: MCPButtonActionRequest) -> MCPChatResponse:
    """Handle MCP button actions (Generate MCP Server or Continue Chat)."""
    try:
        if request.action == "generate":
            # Generate the MCP server
            from app.services.mcp_server_generator import MCPServerGenerator
            generator = MCPServerGenerator()

            # Get repository and analysis data
            from app.database import SessionLocal
            from app.models import RepoAnalysis
            from app.tasks.mcp_analysis_task import get_mcp_analysis_result

            db = SessionLocal()
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == int(analysis_id)).first()
            mcp_analysis = get_mcp_analysis_result(int(analysis_id))
            db.close()

            generation_context = {
                "repository": {
                    "name": analysis.repo_name if analysis else "repository",
                    "owner": analysis.repo_owner if analysis else "owner",
                    "url": analysis.repo_url if analysis else ""
                },
                "analysis_data": analysis.analysis_results if analysis else {},
                "mcp_analysis": mcp_analysis.__dict__ if hasattr(mcp_analysis, '__dict__') else (mcp_analysis if isinstance(mcp_analysis, dict) else {}),
                "chat_context": '\n'.join([msg.get('content', '') for msg in (request.conversation_history or [])]),
                "user_requirements": request.requirements,
                "server_name": request.server_name
            }

            try:
                generation_result = await generator.generate_server(**generation_context)

                # Format the response with generated code and download links
                formatted_response = f"""🎉 **MCP Server Generated Successfully!**

**{request.server_name}** has been created for your repository.

## Generated Code:
```python
{generation_result['server_code']}
```

## Installation Instructions:
{generation_result['installation_instructions']}

## Usage Example:
```bash
{generation_result['usage_example']}
```

The MCP server is ready to use! The files have been generated and are ready for download."""

                return MCPChatResponse(
                    success=True,
                    response=formatted_response,
                    generated_server=generation_result,
                    is_generation=True
                )
            except Exception as gen_error:
                print(f"Error generating MCP server: {gen_error}")
                error_response = f"❌ Sorry, I encountered an error while generating the MCP server: {str(gen_error)}\n\nPlease try again or provide more specific requirements."
                return MCPChatResponse(
                    success=True,
                    response=error_response
                )

        elif request.action == "continue":
            # Continue the chat conversation
            continue_response = """I understand you'd like to continue our conversation about MCP development.

What specific aspects of MCP server development would you like to explore further? I can help with:

- **Architecture planning** - How to structure your MCP server
- **Tool design** - What specific tools to implement
- **Integration strategies** - How to connect with your existing systems
- **Best practices** - Security, performance, and maintainability
- **Testing approaches** - How to validate your MCP server

What would you like to discuss next?"""

            return MCPChatResponse(
                success=True,
                response=continue_response
            )

        else:
            return MCPChatResponse(
                success=False,
                error="Invalid action. Use 'generate' or 'continue'."
            )

    except Exception as e:
        print(f"Error handling MCP button action: {e}")
        raise HTTPException(status_code=500, detail="Failed to handle button action")

@router.post("/analysis/{analysis_id}/mcp/action-button")
async def handle_action_button(analysis_id: str, request: ActionButtonRequest) -> MCPChatResponse:
    """Handle action button clicks (like 'Generate complete MCP server specification')."""
    try:
        # Send the action as a message to the chat service
        response = await mcp_chat_service.chat_with_context(
            int(analysis_id),
            request.action_text,
            request.conversation_history or []
        )

        # Clean up the response to remove empty code blocks
        response = clean_response(response)



        # Only check for MCP generation buttons
        if "SHOW_MCP_BUTTONS:" in response:
            # Handle MCP generation buttons
            lines = response.split('\n')
            server_name = ""
            requirements = ""

            # Find server name
            for line in lines:
                if line.startswith("SHOW_MCP_BUTTONS:"):
                    server_name = line.replace("SHOW_MCP_BUTTONS:", "").strip()
                    break

            # Extract requirements (everything after REQUIREMENTS: line)
            requirements_started = False
            requirements_lines = []
            for line in lines:
                if line.startswith("REQUIREMENTS:"):
                    requirements_started = True
                    # Include the content after REQUIREMENTS: on the same line if any
                    req_content = line.replace("REQUIREMENTS:", "").strip()
                    if req_content:
                        requirements_lines.append(req_content)
                elif requirements_started and line.strip():
                    # Stop collecting if we hit another marker
                    if line.startswith("GENERATE_MCP_SERVER:") or line.startswith("SHOW_MCP_BUTTONS:"):
                        break
                    requirements_lines.append(line.strip())

            requirements = "\n".join(requirements_lines).strip()

            # Clean the response by removing the markers
            cleaned_response = response
            for line in lines:
                if line.startswith("SHOW_MCP_BUTTONS:") or line.startswith("REQUIREMENTS:"):
                    cleaned_response = cleaned_response.replace(line, "").strip()

            return MCPChatResponse(
                success=True,
                response=cleaned_response,
                show_mcp_buttons=True,
                server_name=server_name,
                requirements=requirements
            )

        # No buttons unless explicitly requested by AI
        return MCPChatResponse(
            success=True,
            response=response
        )

    except Exception as e:
        print(f"Error handling action button: {e}")
        raise HTTPException(status_code=500, detail="Failed to handle action button")


@router.post("/analysis/{analysis_id}/mcp/generate-server")
async def generate_mcp_server(analysis_id: str, request: MCPServerGenerateRequest) -> MCPServerResponse:
    """Generate a complete MCP server based on user prompt and repository analysis."""
    try:
        from app.services.mcp_playground_service import MCPPlaygroundService
        playground_service = MCPPlaygroundService()

        server = await playground_service.generate_mcp_server(
            analysis_id=int(analysis_id),
            prompt=request.prompt
        )

        return MCPServerResponse(
            success=True,
            server=server
        )
    except Exception as e:
        print(f"Error generating MCP server: {e}")
        return MCPServerResponse(
            success=False,
            error=str(e)
        )


@router.post("/analysis/{analysis_id}/mcp/test-server")
async def test_mcp_server(analysis_id: str, request: MCPServerTestRequest) -> MCPTestResponse:
    """Test the generated MCP server code."""
    try:
        from app.services.mcp_playground_service import MCPPlaygroundService
        playground_service = MCPPlaygroundService()

        test_results = await playground_service.test_mcp_server(
            server_code=request.server_code,
            package_json=request.package_json,
            analysis_id=int(analysis_id),
            user_prompt=request.user_prompt
        )

        return MCPTestResponse(
            success=True,
            test_results=test_results
        )
    except Exception as e:
        print(f"Error testing MCP server: {e}")
        return MCPTestResponse(
            success=False,
            error=str(e)
        )


@router.post("/analysis/{analysis_id}/mcp/download-server")
async def download_mcp_server(analysis_id: str, request: MCPServerDownloadRequest):
    """Download the generated MCP server as a zip file."""
    try:
        from app.services.mcp_playground_service import MCPPlaygroundService
        from fastapi.responses import StreamingResponse
        import io

        playground_service = MCPPlaygroundService()

        zip_buffer = await playground_service.create_download_package(request.server)

        return StreamingResponse(
            io.BytesIO(zip_buffer),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename={request.server.get('name', 'mcp-server').lower().replace(' ', '-')}.zip"}
        )
    except Exception as e:
        print(f"Error creating download package: {e}")
        raise HTTPException(status_code=500, detail="Failed to create download package")


@router.get("/analysis/{analysis_id}/workflow/state")
async def get_workflow_state(analysis_id: str):
    """Get current workflow state for an analysis"""
    try:
        workflow_state = await workflow_integration_service.get_workflow_state(int(analysis_id))

        return {
            "success": True,
            "workflow_state": {
                "analysis_id": workflow_state.analysis_id,
                "current_stage": workflow_state.current_stage.value,
                "completed_stages": [stage.value for stage in workflow_state.completed_stages],
                "available_actions": workflow_state.available_actions,
                "ready_for_generation": workflow_state.ready_for_generation,
                "context_summary": workflow_state.context_data
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get workflow state: {str(e)}")


@router.post("/analysis/{analysis_id}/workflow/action")
async def execute_workflow_action(analysis_id: str, request: Dict[str, Any]):
    """Execute a workflow action"""
    try:
        action = request.get('action')
        parameters = request.get('parameters', {})

        result = await workflow_integration_service.execute_workflow_action(
            int(analysis_id), action, parameters
        )

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to execute workflow action: {str(e)}")


@router.post("/analysis/{analysis_id}/enhanced-chat")
async def enhanced_mcp_chat(analysis_id: str, request: MCPChatRequest):
    """Enhanced MCP chat with full context validation"""
    try:
        response = await enhanced_conversation_service.chat_with_full_context(
            int(analysis_id),
            request.message,
            request.conversation_history
        )

        return {
            "success": True,
            "response": response.message,
            "suggestions": response.suggestions,
            "validation_result": response.validation_result,
            "next_actions": [action.__dict__ for action in response.next_actions],
            "workflow_stage": response.workflow_stage,
            "context_used": response.context_used
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Enhanced chat failed: {str(e)}")


@router.post("/analysis/{analysis_id}/validate-requirements")
async def validate_requirements(analysis_id: str, request: Dict[str, Any]):
    """Validate user requirements against repository capabilities"""
    try:
        user_requirements = request.get('requirements', '')

        validation_result = await workflow_integration_service.validate_requirements_in_workflow(
            int(analysis_id), user_requirements
        )

        return validation_result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Requirements validation failed: {str(e)}")


@router.post("/analysis/{analysis_id}/prepare-playground")
async def prepare_playground(analysis_id: str, request: Dict[str, Any]):
    """Prepare context for MCP playground"""
    try:
        validated_requirements = request.get('validated_requirements', {})

        playground_result = await workflow_integration_service.prepare_for_playground(
            int(analysis_id), validated_requirements
        )

        return playground_result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Playground preparation failed: {str(e)}")


@router.post("/analysis/{analysis_id}/generate-from-workflow")
async def generate_mcp_from_workflow(analysis_id: str, request: Dict[str, Any]):
    """Generate MCP server using complete workflow context"""
    try:
        target_language = request.get('target_language', 'python')

        generation_result = await workflow_integration_service.generate_mcp_from_workflow(
            int(analysis_id), target_language
        )

        return generation_result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Workflow-based generation failed: {str(e)}")
