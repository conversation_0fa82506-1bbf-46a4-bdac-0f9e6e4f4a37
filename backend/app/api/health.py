"""
Health Check API
Provides comprehensive system health status for all services
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import asyncio
import aiohttp
import redis
import psycopg2
from sqlalchemy import text
from ..database import SessionLocal
from ..config import settings
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/status")
async def get_system_health() -> Dict[str, Any]:
    """
    Get comprehensive system health status
    Returns status of all critical services
    """
    health_status = {
        "overall_status": "healthy",
        "timestamp": None,
        "services": {},
        "critical_services_down": [],
        "warnings": []
    }
    
    # Import here to avoid circular imports
    from datetime import datetime
    health_status["timestamp"] = datetime.utcnow().isoformat()
    
    # Check all services
    services_to_check = [
        ("database", check_database_health),
        ("redis", check_redis_health),
        ("weaviate", check_weaviate_health),
        ("celery", check_celery_health),
        ("ai_services", check_ai_services_health)
    ]
    
    # Run all health checks concurrently
    for service_name, check_func in services_to_check:
        try:
            service_status = await check_func()
            health_status["services"][service_name] = service_status
            
            # Track critical service failures
            if not service_status["healthy"] and service_status.get("critical", True):
                health_status["critical_services_down"].append(service_name)
                health_status["overall_status"] = "unhealthy"
            elif not service_status["healthy"]:
                health_status["warnings"].append(f"{service_name}: {service_status.get('message', 'Service degraded')}")
                
        except Exception as e:
            logger.error(f"Health check failed for {service_name}: {e}")
            health_status["services"][service_name] = {
                "healthy": False,
                "message": f"Health check error: {str(e)}",
                "critical": True
            }
            health_status["critical_services_down"].append(service_name)
            health_status["overall_status"] = "unhealthy"
    
    # Set overall status
    if health_status["critical_services_down"]:
        health_status["overall_status"] = "unhealthy"
    elif health_status["warnings"]:
        health_status["overall_status"] = "degraded"
    
    return health_status


async def check_database_health() -> Dict[str, Any]:
    """Check PostgreSQL database health"""
    try:
        db = SessionLocal()
        # Test basic connectivity and query
        result = db.execute(text("SELECT 1 as test"))
        test_value = result.scalar()
        db.close()
        
        if test_value == 1:
            return {
                "healthy": True,
                "message": "Database connection successful",
                "critical": True,
                "details": {
                    "database_url": settings.database_url.split('@')[1] if '@' in settings.database_url else settings.database_url
                }
            }
        else:
            return {
                "healthy": False,
                "message": "Database query failed",
                "critical": True
            }
            
    except Exception as e:
        return {
            "healthy": False,
            "message": f"Database connection failed: {str(e)}",
            "critical": True
        }


async def check_redis_health() -> Dict[str, Any]:
    """Check Redis health"""
    try:
        # Parse Redis URL
        import redis
        r = redis.from_url(settings.redis_url, decode_responses=True, socket_timeout=5)
        
        # Test ping
        response = r.ping()
        if response:
            # Test set/get
            test_key = "health_check_test"
            r.set(test_key, "test_value", ex=10)  # Expire in 10 seconds
            test_value = r.get(test_key)
            r.delete(test_key)
            
            if test_value == "test_value":
                return {
                    "healthy": True,
                    "message": "Redis connection and operations successful",
                    "critical": True,
                    "details": {
                        "url": settings.redis_url
                    }
                }
        
        return {
            "healthy": False,
            "message": "Redis ping failed",
            "critical": True
        }
        
    except Exception as e:
        return {
            "healthy": False,
            "message": f"Redis connection failed: {str(e)}",
            "critical": True
        }


async def check_weaviate_health() -> Dict[str, Any]:
    """Check Weaviate vector database health"""
    try:
        # Use Docker network name for internal connections
        weaviate_url = settings.weaviate_url.replace('localhost', 'weaviate')
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
            # Check ready endpoint
            async with session.get(f"{weaviate_url}/v1/.well-known/ready") as response:
                if response.status == 200:
                    # Check meta endpoint for more details
                    async with session.get(f"{weaviate_url}/v1/meta") as meta_response:
                        if meta_response.status == 200:
                            meta_data = await meta_response.json()
                            return {
                                "healthy": True,
                                "message": "Weaviate is ready and responding",
                                "critical": False,  # Not critical for basic functionality
                                "details": {
                                    "version": meta_data.get("version", "unknown"),
                                    "hostname": meta_data.get("hostname", "unknown")
                                }
                            }
                
                return {
                    "healthy": False,
                    "message": f"Weaviate meta endpoint failed (status: {response.status})",
                    "critical": False
                }
                
    except Exception as e:
        return {
            "healthy": False,
            "message": f"Weaviate connection failed: {str(e)}",
            "critical": False  # Vector service is optional
        }


async def check_celery_health() -> Dict[str, Any]:
    """Check Celery worker health"""
    try:
        # Import here to avoid circular imports
        from ..tasks.celery_app import celery_app
        
        # Get active workers
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            worker_count = len(active_workers)
            return {
                "healthy": True,
                "message": f"Celery workers active: {worker_count}",
                "critical": True,
                "details": {
                    "active_workers": worker_count,
                    "worker_names": list(active_workers.keys())
                }
            }
        else:
            return {
                "healthy": False,
                "message": "No active Celery workers found",
                "critical": True
            }
            
    except Exception as e:
        return {
            "healthy": False,
            "message": f"Celery health check failed: {str(e)}",
            "critical": True
        }


async def check_ai_services_health() -> Dict[str, Any]:
    """Check AI services (OpenAI/Anthropic) configuration"""
    ai_services = []
    
    if settings.anthropic_api_key:
        ai_services.append("Claude (Anthropic)")
    
    if settings.openai_api_key:
        ai_services.append("OpenAI")
    
    if ai_services:
        return {
            "healthy": True,
            "message": f"AI services configured: {', '.join(ai_services)}",
            "critical": False,  # Not critical for basic functionality
            "details": {
                "configured_services": ai_services,
                "primary_service": "Claude" if settings.anthropic_api_key else "OpenAI"
            }
        }
    else:
        return {
            "healthy": False,
            "message": "No AI services configured",
            "critical": False,
            "details": {
                "note": "AI analysis features will be limited without API keys"
            }
        }


@router.get("/quick")
async def get_quick_health() -> Dict[str, Any]:
    """
    Quick health check for basic services only
    Faster response for simple status checks
    """
    try:
        # Quick database check
        db = SessionLocal()
        db.execute(text("SELECT 1"))
        db.close()
        
        # Quick Redis check
        r = redis.from_url(settings.redis_url, socket_timeout=2)
        r.ping()
        
        return {
            "status": "healthy",
            "message": "Core services operational",
            "timestamp": None
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Core services check failed: {str(e)}",
            "timestamp": None
        }
