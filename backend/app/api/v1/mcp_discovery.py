"""
MCP Discovery API endpoints

Endpoints for discovering and managing MCP servers
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Dict, Any, Optional
import logging

from app.database import get_db
from app.models.analysis import <PERSON>PServer, MCPCategory
from app.services.mcp_discovery import MCPDiscoveryService
from app.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/discover")
async def trigger_mcp_discovery(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Trigger MCP server discovery using Tavily web search
    
    This endpoint starts a background task to discover new MCP servers
    and update the database with the latest findings.
    """
    if not settings.tavily_api_key:
        raise HTTPException(
            status_code=400,
            detail="Tavily API key not configured. Cannot perform MCP discovery."
        )
    
    # Start discovery in background
    background_tasks.add_task(run_mcp_discovery)
    
    return {
        "message": "MCP discovery started in background",
        "status": "running"
    }


async def run_mcp_discovery():
    """Background task to run MCP discovery"""
    try:
        logger.info("Starting background MCP discovery task")
        
        discovery_service = MCPDiscoveryService()
        discovered_servers = await discovery_service.discover_active_mcp_servers()
        
        if discovered_servers:
            await discovery_service.update_database(discovered_servers)
            logger.info(f"MCP discovery completed. Found {len(discovered_servers)} servers")
        else:
            logger.warning("No MCP servers discovered")
            
    except Exception as e:
        logger.error(f"MCP discovery task failed: {str(e)}")


@router.get("/servers")
async def get_mcp_servers(
    category: Optional[str] = None,
    language: Optional[str] = None,
    min_confidence: Optional[float] = 0.0,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    Get discovered MCP servers with optional filtering
    
    Args:
        category: Filter by category (database, ai_chat, development, etc.)
        language: Filter by programming language
        min_confidence: Minimum confidence score (0.0 to 1.0)
        limit: Maximum number of results
        offset: Number of results to skip
    """
    query = db.query(MCPServer).filter(MCPServer.is_active == True)
    
    if category:
        query = query.filter(MCPServer.category == category)
    
    if language:
        query = query.filter(MCPServer.language == language)
    
    if min_confidence:
        query = query.filter(MCPServer.confidence_score >= min_confidence)
    
    # Order by confidence score and stars
    query = query.order_by(
        MCPServer.confidence_score.desc(),
        MCPServer.stars.desc().nullslast()
    )
    
    total = query.count()
    servers = query.offset(offset).limit(limit).all()
    
    return {
        "servers": [
            {
                "id": server.id,
                "name": server.name,
                "url": server.url,
                "description": server.description,
                "category": server.category,
                "github_url": server.github_url,
                "npm_url": server.npm_url,
                "documentation_url": server.documentation_url,
                "stars": server.stars,
                "language": server.language,
                "confidence_score": server.confidence_score,
                "last_updated": server.last_updated.isoformat() if server.last_updated else None
            }
            for server in servers
        ],
        "total": total,
        "limit": limit,
        "offset": offset
    }


@router.get("/categories")
async def get_mcp_categories(db: Session = Depends(get_db)):
    """Get all MCP categories with server counts"""
    categories = db.query(MCPCategory).all()
    
    result = []
    for category in categories:
        server_count = db.query(MCPServer).filter(
            MCPServer.category == category.name,
            MCPServer.is_active == True
        ).count()
        
        result.append({
            "id": category.id,
            "name": category.name,
            "description": category.description,
            "icon": category.icon,
            "color": category.color,
            "server_count": server_count
        })
    
    return {"categories": result}


@router.get("/stats")
async def get_mcp_stats(db: Session = Depends(get_db)):
    """Get MCP discovery statistics"""
    total_servers = db.query(MCPServer).filter(MCPServer.is_active == True).count()
    
    # Count by category
    category_stats = db.query(
        MCPServer.category,
        func.count(MCPServer.id).label('count')
    ).filter(MCPServer.is_active == True).group_by(MCPServer.category).all()

    # Count by language
    language_stats = db.query(
        MCPServer.language,
        func.count(MCPServer.id).label('count')
    ).filter(
        MCPServer.is_active == True,
        MCPServer.language.isnot(None)
    ).group_by(MCPServer.language).all()
    
    # Top servers by stars
    top_servers = db.query(MCPServer).filter(
        MCPServer.is_active == True,
        MCPServer.stars.isnot(None)
    ).order_by(MCPServer.stars.desc()).limit(10).all()
    
    return {
        "total_servers": total_servers,
        "category_distribution": [
            {"category": stat[0], "count": stat[1]}
            for stat in category_stats
        ],
        "language_distribution": [
            {"language": stat[0], "count": stat[1]}
            for stat in language_stats
        ],
        "top_servers": [
            {
                "name": server.name,
                "url": server.url,
                "stars": server.stars,
                "category": server.category,
                "language": server.language
            }
            for server in top_servers
        ]
    }


@router.get("/search")
async def search_mcp_servers(
    q: str,
    category: Optional[str] = None,
    language: Optional[str] = None,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """
    Search MCP servers by name or description
    
    Args:
        q: Search query
        category: Filter by category
        language: Filter by programming language
        limit: Maximum number of results
    """
    query = db.query(MCPServer).filter(
        MCPServer.is_active == True,
        db.or_(
            MCPServer.name.ilike(f"%{q}%"),
            MCPServer.description.ilike(f"%{q}%")
        )
    )
    
    if category:
        query = query.filter(MCPServer.category == category)
    
    if language:
        query = query.filter(MCPServer.language == language)
    
    servers = query.order_by(
        MCPServer.confidence_score.desc(),
        MCPServer.stars.desc().nullslast()
    ).limit(limit).all()
    
    return {
        "servers": [
            {
                "id": server.id,
                "name": server.name,
                "url": server.url,
                "description": server.description,
                "category": server.category,
                "github_url": server.github_url,
                "stars": server.stars,
                "language": server.language,
                "confidence_score": server.confidence_score
            }
            for server in servers
        ],
        "query": q,
        "total": len(servers)
    }


@router.get("/recommendations/{integration_type}")
async def get_mcp_recommendations(
    integration_type: str,
    service_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get MCP server recommendations for a specific integration type
    
    Args:
        integration_type: Type of integration (payment, email, database, etc.)
        service_name: Specific service name (stripe, sendgrid, etc.)
    """
    # Map integration types to MCP categories
    category_mapping = {
        "payment": "payment",
        "email": "communication",
        "sms": "communication",
        "database": "database",
        "cloud_storage": "cloud",
        "analytics": "monitoring",
        "ai_chat": "ai_chat",
        "development_tools": "development",
        "monitoring": "monitoring",
        "design_tools": "design",
        "ticketing": "ticketing",
        "scraping_search": "search"
    }
    
    category = category_mapping.get(integration_type, "general")
    
    query = db.query(MCPServer).filter(
        MCPServer.category == category,
        MCPServer.is_active == True
    )
    
    # If specific service name provided, prioritize matching servers
    if service_name:
        query = query.filter(
            db.or_(
                MCPServer.name.ilike(f"%{service_name}%"),
                MCPServer.description.ilike(f"%{service_name}%")
            )
        )
    
    servers = query.order_by(
        MCPServer.confidence_score.desc(),
        MCPServer.stars.desc().nullslast()
    ).limit(10).all()
    
    return {
        "integration_type": integration_type,
        "service_name": service_name,
        "category": category,
        "recommendations": [
            {
                "id": server.id,
                "name": server.name,
                "url": server.url,
                "description": server.description,
                "github_url": server.github_url,
                "stars": server.stars,
                "language": server.language,
                "confidence_score": server.confidence_score,
                "migration_benefits": [
                    "Standardized MCP interface",
                    "Better error handling",
                    "Enhanced debugging capabilities",
                    "Unified tool ecosystem"
                ]
            }
            for server in servers
        ]
    }
