from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from ..database import get_db
from ..services.github_service import GitHubService
from ..utils.auth import create_access_token, verify_token
from ..models import User
from ..schemas.user import UserResponse

router = APIRouter()
security = HTTPBearer()


@router.get("/login")
async def github_login():
    """Redirect to GitHub OAuth"""
    github_service = GitHubService()
    auth_url = github_service.get_oauth_url()
    return {"auth_url": auth_url}


@router.get("/callback")
async def github_callback(code: str, db: Session = Depends(get_db)):
    """Handle GitHub OAuth callback"""
    github_service = GitHubService()
    
    try:
        # Exchange code for access token
        access_token = github_service.exchange_code_for_token(code)
        
        # Get user info from GitHub
        github_user = github_service.get_user_info(access_token)
        
        # Check if user exists or create new one
        user = db.query(User).filter(User.github_id == str(github_user["id"])).first()
        
        if not user:
            user = User(
                github_id=str(github_user["id"]),
                username=github_user["login"],
                email=github_user.get("email"),
                full_name=github_user.get("name"),
                avatar_url=github_user.get("avatar_url"),
                github_token=access_token  # TODO: Encrypt this
            )
            db.add(user)
            db.commit()
            db.refresh(user)
        else:
            # Update user info
            user.username = github_user["login"]
            user.email = github_user.get("email")
            user.full_name = github_user.get("name")
            user.avatar_url = github_user.get("avatar_url")
            user.github_token = access_token  # TODO: Encrypt this
            db.commit()
        
        # Create JWT token
        jwt_token = create_access_token(data={"sub": str(user.id)})
        
        return {"access_token": jwt_token, "token_type": "bearer"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth callback failed: {str(e)}"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user(token: str = Depends(security), db: Session = Depends(get_db)):
    """Get current user info"""
    payload = verify_token(token.credentials)
    user_id = payload.get("sub")
    
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    user = db.query(User).filter(User.id == int(user_id)).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user


@router.post("/logout")
async def logout():
    """Logout user (client-side token removal)"""
    return {"message": "Successfully logged out"}