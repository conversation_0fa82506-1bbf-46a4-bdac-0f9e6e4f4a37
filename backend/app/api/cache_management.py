"""
Cache management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
import logging

from ..services.analysis_cache import analysis_cache
from ..utils.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/cache", tags=["cache"])

@router.get("/stats")
async def get_cache_stats(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """Get cache statistics"""
    try:
        stats = analysis_cache.get_cache_stats()
        return {
            "success": True,
            "cache_stats": stats
        }
    except Exception as e:
        logger.error(f"Failed to get cache stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve cache statistics")

@router.post("/cleanup")
async def cleanup_cache(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """Clean up expired cache entries"""
    try:
        cleaned_count = analysis_cache.cleanup_expired_entries()
        return {
            "success": True,
            "message": f"Cleaned up {cleaned_count} expired cache entries",
            "cleaned_entries": cleaned_count
        }
    except Exception as e:
        logger.error(f"Failed to cleanup cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to cleanup cache")

@router.delete("/repository/{repo_url:path}")
async def invalidate_repository_cache(
    repo_url: str,
    current_user: dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Invalidate cache for a specific repository"""
    try:
        success = analysis_cache.invalidate_repository_cache(repo_url)
        if success:
            return {
                "success": True,
                "message": f"Cache invalidated for repository: {repo_url}"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to invalidate repository cache")
    except Exception as e:
        logger.error(f"Failed to invalidate repository cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to invalidate repository cache")

@router.delete("/analysis/{analysis_id}")
async def invalidate_analysis_cache(
    analysis_id: int,
    current_user: dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Invalidate cache for a specific analysis"""
    try:
        # Delete suggestions cache
        suggestions_key = analysis_cache._generate_cache_key(
            analysis_cache.SUGGESTIONS_PREFIX, 
            str(analysis_id)
        )
        success = analysis_cache.delete_cache(suggestions_key)
        
        return {
            "success": True,
            "message": f"Cache invalidated for analysis: {analysis_id}"
        }
    except Exception as e:
        logger.error(f"Failed to invalidate analysis cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to invalidate analysis cache")

@router.get("/health")
async def cache_health_check() -> Dict[str, Any]:
    """Check cache health status"""
    try:
        stats = analysis_cache.get_cache_stats()
        is_healthy = stats.get("connected", False)
        
        return {
            "success": True,
            "healthy": is_healthy,
            "backend": stats.get("backend", "unknown"),
            "status": "healthy" if is_healthy else "unhealthy"
        }
    except Exception as e:
        logger.error(f"Cache health check failed: {str(e)}")
        return {
            "success": False,
            "healthy": False,
            "status": "error",
            "error": str(e)
        }
