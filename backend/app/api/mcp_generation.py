"""
MCP Generation API endpoints
Handles MCP tool suggestions and server code generation
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import logging
import os

from ..database import get_db
from ..models import User, RepoAnalysis
from ..utils.auth import get_current_user
from ..services.dynamic_mcp_generator import DynamicMCPGenerator
from ..services.updated_analysis_workflow import UpdatedAnalysisWorkflow
from ..services.enhanced_mcp_suggestions import EnhancedMCPSuggestionService, MCPTool
from ..services.real_mcp_generator import RealMCPGenerator
from ..services.mcp_assistant import MCPAssistantService
from ..services.mcp_marketplace_service import MCPMarketplaceService
from ..services.repository_mcp_generator import RepositoryMCPGenerator
from ..services.intelligent_repository_analyzer import IntelligentRepositoryAnalyzer
from ..services.domain_specific_prompts import DomainSpecificPrompts
from ..services.mcp_architect_service import MCPArchitectService
from ..services.requirements_driven_mcp_generator import RequirementsDrivenMCPGenerator

logger = logging.getLogger(__name__)


def _organize_tools_by_category(tools):
    """Organize tools by their implementation type for frontend display"""
    categories = {}

    type_to_category = {
        'api_proxy': 'API Integration',
        'function_wrapper': 'Business Logic',
        'cli_wrapper': 'Command Line',
        'data_processor': 'Data Management',
        'integration_proxy': 'External Services'
    }

    for tool in tools:
        category = type_to_category.get(tool.implementation_type, 'Other')
        if category not in categories:
            categories[category] = []

        categories[category].append({
            "tool_name": tool.name,
            "description": tool.description,
            "business_value": tool.business_value,
            "complexity_level": tool.implementation_effort,
            "estimated_effort_hours": 8 if tool.implementation_effort == 'low' else 24 if tool.implementation_effort == 'medium' else 48,
            "source_functions": tool.source_functions,
            "source_files": tool.source_files,
            "input_schema": tool.input_schema
        })

    return categories

# Optional import for Celery task
try:
    from ..tasks.enhanced_analysis import generate_mcp_suggestions_only
    HAS_CELERY_TASKS = True
except ImportError as e:
    logger.warning(f"Celery tasks not available: {e}")
    generate_mcp_suggestions_only = None
    HAS_CELERY_TASKS = False
router = APIRouter()

# Initialize services
mcp_generator = DynamicMCPGenerator()
mcp_assistant = MCPAssistantService()
mcp_marketplace = MCPMarketplaceService()

# Request models for chat
class ChatMessage(BaseModel):
    role: str
    content: str
    timestamp: str

class ChatRequest(BaseModel):
    message: str
    conversation_history: List[Dict[str, Any]] = []

class WorkflowRequest(BaseModel):
    user_goals: str

class WorkflowRefineRequest(BaseModel):
    workflow_id: str
    feedback: str

class WorkflowGenerateRequest(BaseModel):
    workflow_id: str
    target_language: str = 'python'

class RequirementsDrivenGenerateRequest(BaseModel):
    user_requirements: str
    target_language: str = "python"
    validate_requirements: bool = True


def transform_suggestions_for_frontend(ai_suggestions: Dict[str, Any]) -> Dict[str, Any]:
    """Transform enhanced AI suggestions to match frontend interface expectations"""

    try:
        # Initialize categories variable
        categories = {}

        # Handle enhanced analysis structure - check if it's already in the new format
        if "categories" in ai_suggestions and "repository_analysis" in ai_suggestions:
            # New enhanced format - return as-is with minimal transformation
            categories = ai_suggestions.get("categories", {})

            # Ensure all tools have required fields
            for category_name, tools in categories.items():
                if not isinstance(tools, list):
                    continue
                for tool in tools:
                    if not isinstance(tool, dict):
                        continue
                    # Ensure complexity_level is properly set
                    if "complexity_level" not in tool and "estimated_effort_hours" in tool:
                        hours = tool["estimated_effort_hours"]
                        if hours <= 8:
                            tool["complexity_level"] = "low"
                        elif hours <= 16:
                            tool["complexity_level"] = "medium"
                        else:
                            tool["complexity_level"] = "high"
                    elif "complexity_level" not in tool:
                        tool["complexity_level"] = "medium"

                    # Ensure required fields exist
                    tool.setdefault("tool_name", tool.get("name", "Unknown Tool"))
                    tool.setdefault("description", "No description available")
                    tool.setdefault("business_value", "No description available")
                    tool.setdefault("input_schema", {})
                    tool.setdefault("output_schema", {})
                    tool.setdefault("implementation_hints", "")
                    tool.setdefault("use_cases", [])
                    tool.setdefault("dependencies", [])
                    tool.setdefault("error_scenarios", [])

            # Include new strategic structure if available
            result = {
                "categories": categories,
                "prioritized_recommendations": ai_suggestions.get("prioritized_recommendations", []),
                "implementation_roadmap": ai_suggestions.get("implementation_roadmap", {}),
                "total_tools_suggested": ai_suggestions.get("total_tools_suggested", 0),
                "repository_analysis": ai_suggestions.get("repository_analysis", {}),
                "tool_generation_strategy": ai_suggestions.get("tool_generation_strategy", {})
            }

            # Add new strategic structure if available
            if "codebase_opportunities" in ai_suggestions:
                result["codebase_opportunities"] = ai_suggestions["codebase_opportunities"]
            if "marketplace_recommendations" in ai_suggestions:
                result["marketplace_recommendations"] = ai_suggestions["marketplace_recommendations"]
            if "strategic_recommendations" in ai_suggestions:
                result["strategic_recommendations"] = ai_suggestions["strategic_recommendations"]
            if "integration_architecture" in ai_suggestions:
                result["integration_architecture"] = ai_suggestions["integration_architecture"]
            if "success_metrics" in ai_suggestions:
                result["success_metrics"] = ai_suggestions["success_metrics"]

            return result

        # Legacy format transformation (for older analyses)
        def get_complexity_level(implementation_effort: str) -> str:
            """Convert implementation effort to complexity level"""
            effort_lower = implementation_effort.lower() if implementation_effort else "medium"
            if "low" in effort_lower or "easy" in effort_lower or "simple" in effort_lower:
                return "low"
            elif "high" in effort_lower or "hard" in effort_lower or "complex" in effort_lower:
                return "high"
            else:
                return "medium"

        # Transform categories with proper field mapping for legacy format
        transformed_categories = {}
        categories = ai_suggestions.get("categories", {})

        for category_name, tools in categories.items():
            transformed_tools = []

            for tool in tools:
                # Transform each tool to match frontend interface
                transformed_tool = {
                    "tool_name": tool.get("tool_name", tool.get("name", "Unknown Tool")),
                    "description": tool.get("description", tool.get("business_value", "No description available")),
                    "business_value": tool.get("business_value", "No description available"),
                    "complexity_level": tool.get("complexity_level", get_complexity_level(tool.get("implementation_effort", "medium"))),
                    "input_schema": tool.get("input_schema", {}),
                    "output_schema": tool.get("output_schema", {}),
                    "implementation_hints": tool.get("implementation_hints", ""),
                    "use_cases": tool.get("use_cases", []),
                    "dependencies": tool.get("dependencies", []),
                    "error_scenarios": tool.get("error_scenarios", [])
                }
                transformed_tools.append(transformed_tool)

            transformed_categories[category_name] = transformed_tools

        # Return transformed suggestions
        return {
            "categories": transformed_categories,
            "prioritized_recommendations": ai_suggestions.get("prioritized_recommendations", []),
            "implementation_roadmap": ai_suggestions.get("implementation_roadmap", {}),
            "total_tools_suggested": ai_suggestions.get("total_tools_suggested", 0)
        }

    except Exception as e:
        logger.error(f"Error transforming suggestions for frontend: {str(e)}")
        # Return empty structure on error
        return {
            "categories": {},
            "prioritized_recommendations": [],
            "implementation_roadmap": {},
            "total_tools_suggested": 0
        }


# Request/Response Models
class SelectedTool(BaseModel):
    tool_name: str
    description: str
    category: str

class GenerateMCPServerRequest(BaseModel):
    selected_tools: List[SelectedTool]  # List of tool objects
    target_language: str = "typescript"
    customization_options: Optional[Dict[str, Any]] = None
    github_token: Optional[str] = None

class UserMCPSuggestionRequest(BaseModel):
    analysis_id: int
    user_prompt: str
    focus_areas: Optional[List[str]] = None  # e.g., ["api_endpoints", "database", "file_processing"]


# Endpoints
@router.get("/{analysis_id}/suggestions")
async def get_mcp_suggestions(
    analysis_id: int,
    regenerate: bool = Query(False, description="Force regenerate suggestions"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get MCP tool suggestions for an analysis"""
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        logger.error(f"Analysis {analysis_id} not found for user {current_user.id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    logger.info(f"Analysis {analysis_id} status: {analysis.status}")
    logger.info(f"Analysis error_message: {analysis.error_message}")
    logger.info(f"User {current_user.id} requesting suggestions for analysis {analysis_id}")

    # Allow suggestions generation for failed analyses if they have basic analysis results
    # Also allow "processing" status for analyses that might be in progress
    allowed_statuses = ["completed", "failed", "processing"]
    if analysis.status not in allowed_statuses:
        logger.error(f"Analysis {analysis_id} has invalid status '{analysis.status}' for suggestion generation")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Analysis must be completed, failed, or processing before generating suggestions. Current status: {analysis.status}. Error: {analysis.error_message}"
        )

    # For failed analyses, check if we have enough data to generate suggestions
    if analysis.status == "failed":
        analysis_results = analysis.analysis_results or {}
        if not analysis_results.get("repository_info") and not analysis_results.get("dependencies"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed analysis does not have sufficient data for suggestion generation. Error: {analysis.error_message}"
            )
    
    # Check if suggestions already exist and user doesn't want to regenerate
    analysis_results = analysis.analysis_results or {}
    logger.info(f"Analysis results keys: {list(analysis_results.keys())}")
    
    if not regenerate and "ai_suggestions" in analysis_results:
        # Transform suggestions to frontend format
        ai_suggestions = analysis_results.get("ai_suggestions", {})
        logger.info(f"AI suggestions structure: {ai_suggestions.keys() if ai_suggestions else 'Empty'}")
        logger.info(f"AI suggestions sample: {str(ai_suggestions)[:500]}...")
        
        try:
            transformed_suggestions = transform_suggestions_for_frontend(ai_suggestions)
            logger.info(f"Transformed suggestions successfully")
            
            # Return existing suggestions with enhanced structure including detected integrations and MCP alternatives
            return {
                "analysis_id": analysis_id,
                "suggestions_available": True,
                "mcp_suggestions": transformed_suggestions,
                "detected_integrations": analysis_results.get("ai_analysis", {}).get("detected_integrations", []),
                "mcp_alternatives": analysis_results.get("ai_analysis", {}).get("mcp_alternatives", {}),
                "business_analysis": analysis_results.get("business_analysis", analysis_results.get("comprehensive_analysis", {})),
                "confidence_score": analysis_results.get("confidence_score", 0.0),
                "implementation_complexity": analysis_results.get("implementation_complexity", {})
            }
        except Exception as e:
            logger.error(f"Error transforming suggestions: {str(e)}")
            logger.exception("Full error details:")
            # Fall through to regeneration logic
    
    # Generate new suggestions
    try:
        try:
            # Use enhanced MCP suggestion service for repository-specific tools
            logger.info(f"Generating enhanced MCP suggestions for analysis {analysis_id}")

            # Initialize the enhanced suggestion service
            suggestion_service = EnhancedMCPSuggestionService()

            # Generate specific, actionable tools based on repository analysis
            analysis_results = analysis.analysis_results or {}
            specific_tools = await suggestion_service.generate_specific_tools(analysis_results)

            logger.info(f"Generated {len(specific_tools)} specific MCP tools for {analysis_id}")

            # Transform tools for frontend display
            enhanced_suggestions = {
                "categories": _organize_tools_by_category(specific_tools),
                "total_tools_suggested": len(specific_tools),
                "specific_tools": [
                    {
                        "name": tool.name,
                        "description": tool.description,
                        "implementation_type": tool.implementation_type,
                        "source_functions": tool.source_functions,
                        "source_files": tool.source_files,
                        "business_value": tool.business_value,
                        "implementation_effort": tool.implementation_effort,
                        "input_schema": tool.input_schema,
                        "code_references": tool.code_references
                    }
                    for tool in specific_tools
                ],
                "repository_analysis": {
                    "name": analysis_results.get("repository_info", {}).get("name", "unknown"),
                    "language": analysis_results.get("repository_info", {}).get("language", "unknown"),
                    "tools_extracted": len(specific_tools),
                    "implementation_types": list(set(tool.implementation_type for tool in specific_tools))
                }
            }

            # Store enhanced data in new fields
            analysis.mcp_suggestions = enhanced_suggestions
            analysis.specific_tools = enhanced_suggestions["specific_tools"]

            # Update analysis results with enhanced suggestions
            analysis_results["ai_suggestions"] = enhanced_suggestions
            analysis.analysis_results = analysis_results
            db.commit()

            # Transform for frontend
            transformed_suggestions = transform_suggestions_for_frontend(enhanced_suggestions)

            return {
                "analysis_id": analysis_id,
                "suggestions_available": True,
                "mcp_suggestions": transformed_suggestions,
                "detected_integrations": analysis_results.get("ai_analysis", {}).get("detected_integrations", []),
                "mcp_alternatives": analysis_results.get("ai_analysis", {}).get("mcp_alternatives", {}),
                "business_analysis": analysis_results.get("business_analysis", analysis_results.get("comprehensive_analysis", {})),
                "confidence_score": 0.9,  # Higher confidence for enhanced suggestions
                "implementation_complexity": {"overall": "medium"},
                "generation_method": "enhanced",
                "tools_count": len(specific_tools)
            }

        except Exception as e:
            logger.error(f"Enhanced MCP suggestion generation failed: {str(e)}")
            # Return error instead of fallback
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate enhanced MCP suggestions: {str(e)}"
            )
        
    except Exception as e:
        logger.error(f"Failed to start MCP suggestions generation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate MCP suggestions: {str(e)}"
        )


@router.post("/{analysis_id}/generate-functional-server")
async def generate_functional_mcp_server(
    analysis_id: int,
    request: GenerateMCPServerRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate a functional MCP server with actual implementations based on repository analysis"""

    # Get the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    if not analysis.analysis_results:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis results not available"
        )

    try:
        # Initialize services
        suggestion_service = EnhancedMCPSuggestionService()
        real_generator = RealMCPGenerator()

        # Generate specific tools
        analysis_results = analysis.analysis_results
        specific_tools = await suggestion_service.generate_specific_tools(analysis_results)

        if not specific_tools:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No tools could be extracted from the repository analysis"
            )

        # Generate real MCP server with actual implementations
        github_token = getattr(request, 'github_token', None)
        target_language = getattr(request, 'target_language', 'python')
        server_name = getattr(request, 'server_name', f"{analysis.repo_name}-mcp-server")

        functional_server = await real_generator.generate_mcp_server(
            analysis_id=analysis.id,
            repo_url=analysis.repo_url,
            repo_owner=analysis.repo_owner,
            repo_name=analysis.repo_name,
            selected_tools=[tool.__dict__ for tool in specific_tools],
            target_language=target_language,
            github_token=github_token,
            server_name=server_name
        )

        logger.info(f"Generated real MCP server with {functional_server['implementations_extracted']} implementations for analysis {analysis_id}")

        return {
            "message": "Real MCP server generated successfully with actual repository implementations",
            "server_name": functional_server['server_name'],
            "language": functional_server['language'],
            "tools_count": functional_server['tools_count'],
            "implementations_extracted": functional_server['implementations_extracted'],
            "zip_file_path": functional_server['zip_file_path'],
            "generated_at": functional_server['generated_at']
        }

    except Exception as e:
        logger.error(f"Error generating functional MCP server: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate functional MCP server: {str(e)}"
        )


@router.post("/{analysis_id}/generate-server")
async def generate_mcp_server(
    analysis_id: int,
    request: GenerateMCPServerRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate MCP server code based on selected tools"""
    
    # Extract hosting architecture from customization options
    hosting_architecture = "http-sse"  # default
    if request.customization_options and "hosting_type" in request.customization_options:
        hosting_architecture = request.customization_options["hosting_type"]

    logger.info(f"MCP Generation Request - Language: {request.target_language}, Architecture: {hosting_architecture}, Tools: {len(request.selected_tools)}")

    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    if analysis.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis must be completed before generating MCP server"
        )
    
    analysis_results = analysis.analysis_results or {}
    
    # Check if MCP suggestions are available (support both old and new formats)
    has_ai_suggestions = "ai_suggestions" in analysis_results
    has_enhanced_tools = analysis.specific_tools is not None and len(analysis.specific_tools) > 0

    if not has_ai_suggestions and not has_enhanced_tools:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="MCP suggestions not available. Please generate suggestions first."
        )

    # Get MCP suggestions - prefer enhanced format, fallback to legacy
    if has_enhanced_tools:
        # Use enhanced analysis format
        ai_suggestions = {
            "specific_tools": analysis.specific_tools,
            "categories": analysis.mcp_suggestions.get("categories", {}) if analysis.mcp_suggestions else {},
            "total_tools_suggested": len(analysis.specific_tools)
        }
    else:
        # Use legacy format
        ai_suggestions = analysis_results.get("ai_suggestions", {})
    
    # Handle both enhanced and legacy formats
    all_tools = {}

    if has_enhanced_tools:
        # Enhanced format - tools are already in the right structure
        for tool in ai_suggestions.get("specific_tools", []):
            tool_name = tool.get("name", tool.get("tool_name", ""))
            if tool_name:
                # Convert enhanced tool format to expected format
                all_tools[tool_name] = {
                    "tool_name": tool_name,
                    "name": tool_name,
                    "description": tool.get("description", ""),
                    "category": tool.get("implementation_type", "function_wrapper"),
                    "business_value": tool.get("business_value", ""),
                    "implementation_effort": tool.get("implementation_effort", "medium"),
                    "source_functions": tool.get("source_functions", []),
                    "source_files": tool.get("source_files", []),
                    "input_schema": tool.get("input_schema", {}),
                    "code_references": tool.get("code_references", {})
                }
    else:
        # Legacy format - transform suggestions to frontend format
        mcp_suggestions = transform_suggestions_for_frontend(ai_suggestions)
        # Collect all tools from all categories
        for tools in mcp_suggestions.get("categories", {}).values():
            for tool in tools:
                all_tools[tool["tool_name"]] = tool
    
    # Filter selected tools
    selected_tools = []
    for selected_tool in request.selected_tools:
        tool_name = selected_tool.tool_name
        if tool_name in all_tools:
            # Use the tool from suggestions but add category info from selection
            tool_data = all_tools[tool_name].copy()
            tool_data["selection_category"] = selected_tool.category
            selected_tools.append(tool_data)
        else:
            # Handle tools that might be from new strategic recommendations
            logger.info(f"Tool '{tool_name}' not found in legacy categories, treating as strategic recommendation")
            selected_tools.append({
                "tool_name": tool_name,
                "description": selected_tool.description,
                "category": selected_tool.category,
                "selection_category": selected_tool.category,
                "business_value": selected_tool.description,
                "complexity_level": "medium",
                "estimated_effort_hours": 24
            })
    
    if not selected_tools:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No valid tools selected for generation"
        )
    
    # Prepare repository context
    repo_context = {
        "name": analysis.repo_name,
        "owner": analysis.repo_owner,
        "url": analysis.repo_url,
        "language": analysis_results.get("repository_info", {}).get("language"),
        "description": analysis_results.get("repository_info", {}).get("description")
    }
    
    # Generate functional MCP server code using enhanced system
    try:
        # Initialize enhanced services
        suggestion_service = EnhancedMCPSuggestionService()
        real_generator = RealMCPGenerator()

        # Generate specific tools from analysis if not already available
        if not analysis.specific_tools:
            specific_tools = await suggestion_service.generate_specific_tools(analysis_results)
            # Store the tools for future use
            analysis.specific_tools = [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "implementation_type": tool.implementation_type,
                    "source_functions": tool.source_functions,
                    "source_files": tool.source_files,
                    "business_value": tool.business_value,
                    "implementation_effort": tool.implementation_effort,
                    "input_schema": tool.input_schema,
                    "code_references": tool.code_references
                }
                for tool in specific_tools
            ]
            db.commit()
        else:
            # Convert stored tools back to MCPTool objects
            from ..services.enhanced_mcp_suggestions import MCPTool
            specific_tools = [
                MCPTool(
                    name=tool["name"],
                    description=tool["description"],
                    implementation_type=tool["implementation_type"],
                    source_functions=tool["source_functions"],
                    source_files=tool["source_files"],
                    business_value=tool["business_value"],
                    implementation_effort=tool["implementation_effort"],
                    input_schema=tool["input_schema"],
                    code_references=tool["code_references"]
                )
                for tool in analysis.specific_tools
            ]

        # Filter tools based on selected tools if provided
        if selected_tools:
            selected_tool_names = [tool.get("tool_name", tool.get("name", "")) for tool in selected_tools]
            filtered_tools = [tool for tool in specific_tools if tool.name in selected_tool_names]
        else:
            # Use all available tools
            filtered_tools = specific_tools

        if not filtered_tools:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid tools found for MCP server generation"
            )

        # Debug logging
        logger.info(f"Repository context: {repo_context}")
        logger.info(f"Filtered tools count: {len(filtered_tools)}")
        for tool in filtered_tools:
            logger.info(f"Tool: {tool.name} - {tool.description[:100]}")

        # Ensure repository context is properly included in analysis_results
        enhanced_analysis_results = analysis_results.copy()
        if 'repository_info' not in enhanced_analysis_results:
            enhanced_analysis_results['repository_info'] = {}

        # Update repository_info with correct data from repo_context and analysis
        enhanced_analysis_results['repository_info'].update({
            'name': analysis.repo_name,
            'owner': analysis.repo_owner,
            'url': analysis.repo_url,
            'language': analysis_results.get("repository_info", {}).get("language"),
            'description': analysis_results.get("repository_info", {}).get("description")
        })

        # Generate real MCP server with actual implementations
        github_token = getattr(request, 'github_token', None)

        functional_server = await real_generator.generate_mcp_server(
            analysis_id=analysis.id,
            repo_url=analysis.repo_url,
            repo_owner=analysis.repo_owner,
            repo_name=analysis.repo_name,
            selected_tools=[tool.__dict__ for tool in filtered_tools],
            target_language=request.target_language,
            github_token=github_token,
            server_name=f"{analysis.repo_name}-mcp-server"
        )

        # The real generator already created the ZIP file
        zip_file_path = functional_server['zip_file_path']

        # Move to permanent location with analysis ID
        repo_name = analysis.repo_name
        clean_repo_name = repo_name.lower().replace(" ", "-").replace("_", "-")
        permanent_zip_path = f"/tmp/mcp-{clean_repo_name}-{analysis_id}.zip"

        import shutil
        shutil.move(zip_file_path, permanent_zip_path)

        # Store the generated server configuration
        analysis.functional_server_config = functional_server
        db.commit()

        return {
            "success": True,
            "analysis_id": analysis_id,
            "generation_result": {
                "server_name": functional_server['server_name'],
                "language": functional_server['language'],
                "tools_count": functional_server['tools_count'],
                "implementations_extracted": functional_server['implementations_extracted'],
                "zip_file_path": permanent_zip_path,
                "generated_at": functional_server['generated_at'],
                "repository_name": analysis.repo_name,
                "repository_url": analysis.repo_url
            },
            "download_ready": True,
            "real_implementations": True,
            "message": f"Generated real MCP server with {functional_server['implementations_extracted']} actual implementations from {analysis.repo_name}"
        }

    except Exception as e:
        logger.error(f"Functional MCP server generation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate functional MCP server: {str(e)}"
        )


@router.post("/{analysis_id}/user-suggestions")
async def get_user_driven_mcp_suggestions(
    analysis_id: int,
    request: UserMCPSuggestionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate MCP suggestions based on user prompt and indexed code"""

    logger.info(f"User-driven MCP suggestions request for analysis {analysis_id}: {request.user_prompt}")

    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    if analysis.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis must be completed before generating suggestions"
        )

    try:
        # Get analysis results
        analysis_results = analysis.analysis_results or {}

        # Generate user-driven suggestions
        from ..services.user_mcp_suggestion_service import UserMCPSuggestionService
        suggestion_service = UserMCPSuggestionService()

        suggestions = await suggestion_service.generate_user_driven_suggestions(
            user_prompt=request.user_prompt,
            analysis_data=analysis_results,
            focus_areas=request.focus_areas or []
        )

        return {
            "success": True,
            "analysis_id": analysis_id,
            "user_prompt": request.user_prompt,
            "suggestions": suggestions,
            "total_suggestions": len(suggestions.get("recommendations", []))
        }

    except Exception as e:
        logger.error(f"User-driven suggestions failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate user-driven suggestions: {str(e)}"
        )


@router.get("/{analysis_id}/download-server")
async def download_mcp_server(
    analysis_id: int,
    zip_file_path: str = Query(..., description="Path to generated ZIP file"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Download generated MCP server ZIP file"""
    
    # Verify user owns the analysis
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    # Verify ZIP file exists
    if not os.path.exists(zip_file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Generated MCP server file not found"
        )
    
    # Return file for download
    # Use clean repo name without analysis ID
    clean_repo_name = analysis.repo_name.lower().replace(" ", "-").replace("_", "-")
    filename = f"mcp-{clean_repo_name}.zip"
    
    return FileResponse(
        zip_file_path,
        media_type="application/zip",
        filename=filename,
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.get("/languages/supported")
async def get_supported_languages():
    """Get list of supported programming languages for MCP server generation"""
    
    return {
        "supported_languages": [
            {
                "code": "python",
                "name": "Python",
                "description": "Python MCP server using @modelcontextprotocol/server-python",
                "features": ["Type hints", "Pydantic validation", "Async support", "Rich ecosystem"],
                "recommended": True
            },
            {
                "code": "typescript", 
                "name": "TypeScript",
                "description": "TypeScript MCP server using @modelcontextprotocol/sdk",
                "features": ["Strong typing", "Modern JS features", "Node.js ecosystem", "IDE support"],
                "recommended": True
            },
            {
                "code": "javascript",
                "name": "JavaScript",
                "description": "JavaScript MCP server using @modelcontextprotocol/sdk", 
                "features": ["Quick development", "Node.js ecosystem", "Wide compatibility"],
                "recommended": False
            }
        ],
        "default_language": "python"
    }


@router.get("/analysis/{analysis_id}/language-recommendation")
async def get_language_recommendation(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get recommended MCP server language based on repository analysis"""

    logger.info(f"Language recommendation request for analysis_id: {analysis_id} (type: {type(analysis_id)})")

    # Get analysis record
    analysis = db.query(RepoAnalysis).filter(
        RepoAnalysis.id == analysis_id,
        RepoAnalysis.user_id == current_user.id
    ).first()

    logger.info(f"Found analysis: {analysis is not None}")

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    if not analysis.analysis_results:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Analysis not completed yet"
        )

    try:
        # Extract repository context and analysis data
        analysis_results = analysis.analysis_results
        repo_context = analysis_results.get('repository_info', {})

        logger.info(f"Getting language recommendation for analysis_id: {analysis_id}")
        logger.info(f"repo_context type: {type(repo_context)}, content: {repo_context}")

        # Get language recommendation
        recommended_language = mcp_generator.get_recommended_language(repo_context, analysis_results)
        logger.info(f"recommended_language: {recommended_language} (type: {type(recommended_language)})")

        # Ensure recommended_language is a string
        if not isinstance(recommended_language, str):
            logger.error(f"get_recommended_language returned non-string: {type(recommended_language)} = {recommended_language}")
            recommended_language = 'python'  # Safe fallback

        # Get primary language from repository
        primary_language = repo_context.get('language', '')
        logger.info(f"primary_language before processing: {primary_language} (type: {type(primary_language)})")

        if primary_language and isinstance(primary_language, str):
            primary_language = primary_language.lower()
        else:
            primary_language = ''

        logger.info(f"primary_language after processing: {primary_language}")

        # Get language distribution
        code_structure = analysis_results.get('code_structure', {})
        languages = code_structure.get('languages', {})

        # Calculate language percentages (handle mixed types in values)
        total_files = 0
        if languages:
            # Convert all values to integers, filtering out non-numeric values
            numeric_values = []
            for value in languages.values():
                try:
                    if isinstance(value, (int, float)):
                        numeric_values.append(int(value))
                    elif isinstance(value, str) and value.isdigit():
                        numeric_values.append(int(value))
                except (ValueError, TypeError):
                    continue
            total_files = sum(numeric_values) if numeric_values else 0

        language_percentages = {}
        if total_files > 0:
            language_percentages = {}
            for lang, count in languages.items():
                try:
                    if isinstance(count, (int, float)):
                        numeric_count = int(count)
                    elif isinstance(count, str) and count.isdigit():
                        numeric_count = int(count)
                    else:
                        continue
                    language_percentages[lang] = round((numeric_count / total_files) * 100, 1)
                except (ValueError, TypeError, ZeroDivisionError):
                    continue

        logger.info("Building response object...")

        # Build reasoning object safely
        primary_match = primary_language == recommended_language.replace('csharp', 'c#')
        logger.info(f"primary_match: {primary_match}")

        explanation = f"Recommended {recommended_language} because your repository is primarily {primary_language or 'unknown'}" if primary_language else f"Recommended {recommended_language} as the default choice"
        logger.info(f"explanation: {explanation}")

        benefits = _get_language_benefits(recommended_language, primary_language)
        logger.info(f"benefits: {benefits}")

        alternatives = _get_alternative_languages(recommended_language, primary_language)
        logger.info(f"alternatives: {alternatives}")

        response = {
            "recommended_language": recommended_language,
            "primary_language": primary_language,
            "language_distribution": language_percentages,
            "reasoning": {
                "primary_match": primary_match,
                "explanation": explanation,
                "benefits": benefits
            },
            "alternatives": alternatives
        }

        logger.info("Response built successfully")
        return response

    except Exception as e:
        import traceback
        logger.error(f"Error getting language recommendation: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get language recommendation: {str(e)}"
        )


def _get_language_benefits(recommended_lang: str, primary_lang: str) -> List[str]:
    """Get benefits of using the recommended language"""
    benefits = {
        'python': [
            "Fastest development cycle",
            "Best MCP ecosystem support",
            "Easy integration with AI/ML libraries",
            "Extensive documentation and examples"
        ],
        'typescript': [
            "Strong typing prevents runtime errors",
            "Excellent IDE support and tooling",
            "Large Node.js ecosystem",
            "Good for web developers"
        ],
        'go': [
            "High performance and low memory usage",
            "Excellent for concurrent operations",
            "Single binary deployment",
            "Strong standard library"
        ],
        'rust': [
            "Maximum performance and memory safety",
            "Zero-cost abstractions",
            "Excellent for system-level operations",
            "Growing ecosystem"
        ],
        'java': [
            "Enterprise-grade reliability",
            "Mature ecosystem and tooling",
            "Strong typing and performance",
            "Wide platform support"
        ],
        'csharp': [
            "Excellent .NET integration",
            "Strong typing and performance",
            "Rich development tools",
            "Good for Windows environments"
        ]
    }

    base_benefits = benefits.get(recommended_lang, ["Good general-purpose choice"])

    # Add native language benefit if matching
    if primary_lang and recommended_lang == primary_lang.replace('c#', 'csharp'):
        base_benefits.insert(0, f"Matches your repository's primary language ({primary_lang})")

    return base_benefits


def _get_alternative_languages(recommended_lang: str, primary_lang: str) -> List[Dict[str, Any]]:
    """Get alternative language options with explanations"""
    all_languages = {
        'python': {
            'name': 'Python',
            'popularity': 'Very High',
            'difficulty': 'Easy',
            'use_case': 'General purpose, AI/ML, rapid development'
        },
        'typescript': {
            'name': 'TypeScript',
            'popularity': 'High',
            'difficulty': 'Medium',
            'use_case': 'Web development, type-safe JavaScript'
        },
        'go': {
            'name': 'Go',
            'popularity': 'Medium',
            'difficulty': 'Medium',
            'use_case': 'High performance, microservices, CLI tools'
        },
        'rust': {
            'name': 'Rust',
            'popularity': 'Low',
            'difficulty': 'Hard',
            'use_case': 'System programming, maximum performance'
        },
        'java': {
            'name': 'Java',
            'popularity': 'Medium',
            'difficulty': 'Medium',
            'use_case': 'Enterprise applications, Android development'
        },
        'csharp': {
            'name': 'C#',
            'popularity': 'Medium',
            'difficulty': 'Medium',
            'use_case': 'Windows applications, .NET ecosystem'
        }
    }

    # Return alternatives excluding the recommended one
    alternatives = []
    for lang_code, info in all_languages.items():
        if lang_code != recommended_lang:
            alternatives.append({
                'code': lang_code,
                'name': info['name'],
                'popularity': info['popularity'],
                'difficulty': info['difficulty'],
                'use_case': info['use_case'],
                'matches_repo': primary_lang and isinstance(primary_lang, str) and lang_code == primary_lang.replace('c#', 'csharp')
            })

    # Sort by popularity and repo match
    alternatives.sort(key=lambda x: (not x['matches_repo'], x['popularity'] != 'Very High', x['popularity'] != 'High'))

    return alternatives[:4]  # Return top 4 alternatives


@router.post("/{analysis_id}/chat")
async def chat_with_mcp_assistant(
    analysis_id: int,
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Chat with MCP Assistant to understand what MCPs to create"""

    try:
        # Verify analysis exists and belongs to user
        analysis = db.query(RepoAnalysis).filter(
            RepoAnalysis.id == analysis_id,
            RepoAnalysis.user_id == current_user.id
        ).first()

        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )

        if analysis.status != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Analysis must be completed before using MCP Assistant"
            )

        # Chat with assistant
        response = await mcp_assistant.chat(
            analysis_id=analysis_id,
            user_message=request.message,
            conversation_history=request.conversation_history
        )

        return {
            "success": True,
            "response": response.response,
            "suggestions": [
                {
                    "type": suggestion.type,
                    "title": suggestion.title,
                    "description": suggestion.description,
                    "implementation": suggestion.implementation,
                    "marketplace_url": suggestion.marketplace_url
                }
                for suggestion in response.suggestions
            ],
            "workflow_step": response.workflow_step
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MCP Assistant chat failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to chat with MCP Assistant: {str(e)}"
        )


@router.get("/marketplace/{category}")
async def browse_marketplace_mcps(
    category: str,
    current_user: User = Depends(get_current_user)
):
    """Browse MCPs from marketplace by category"""

    try:
        mcps = await mcp_marketplace.get_category_mcps(category)

        return {
            "success": True,
            "category": category,
            "mcps": [
                {
                    "name": mcp.name,
                    "description": mcp.description,
                    "url": mcp.url,
                    "category": mcp.category,
                    "installation_method": mcp.installation_method,
                    "author": mcp.author,
                    "tags": mcp.tags,
                    "compatibility_score": mcp.compatibility_score
                }
                for mcp in mcps
            ]
        }

    except Exception as e:
        logger.error(f"Failed to browse marketplace MCPs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to browse marketplace MCPs: {str(e)}"
        )


@router.post("/{analysis_id}/workflow/start")
async def start_mcp_workflow(
    analysis_id: int,
    request: WorkflowRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a new MCP workflow"""

    try:
        # Verify analysis exists and belongs to user
        analysis = db.query(RepoAnalysis).filter(
            RepoAnalysis.id == analysis_id,
            RepoAnalysis.user_id == current_user.id
        ).first()

        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )

        # Build repository context
        repository_context = {
            'name': analysis.repo_name,
            'language': getattr(analysis, 'primary_language', 'Unknown'),
            'technologies': analysis.analysis_results.get('technologies', []) if analysis.analysis_results else []
        }

        # Start workflow
        workflow_result = await mcp_assistant.start_workflow(
            analysis_id=analysis_id,
            user_goals=request.user_goals,
            repository_context=repository_context
        )

        return {
            "success": True,
            **workflow_result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start workflow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start workflow: {str(e)}"
        )


@router.post("/workflow/refine")
async def refine_mcp_workflow(
    request: WorkflowRefineRequest,
    current_user: User = Depends(get_current_user)
):
    """Refine an existing MCP workflow"""

    try:
        workflow_result = await mcp_assistant.refine_workflow(
            workflow_id=request.workflow_id,
            user_feedback=request.feedback
        )

        return {
            "success": True,
            **workflow_result
        }

    except Exception as e:
        logger.error(f"Failed to refine workflow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to refine workflow: {str(e)}"
        )


@router.post("/workflow/{workflow_id}/finalize")
async def finalize_mcp_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_user)
):
    """Finalize MCP workflow for generation"""

    try:
        result = await mcp_assistant.finalize_workflow(workflow_id)

        return {
            "success": True,
            **result
        }

    except Exception as e:
        logger.error(f"Failed to finalize workflow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to finalize workflow: {str(e)}"
        )


@router.post("/{analysis_id}/workflow/generate")
async def generate_mcp_from_workflow(
    analysis_id: int,
    request: WorkflowGenerateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate MCP server from finalized workflow"""

    try:
        # Verify analysis exists and belongs to user
        analysis = db.query(RepoAnalysis).filter(
            RepoAnalysis.id == analysis_id,
            RepoAnalysis.user_id == current_user.id
        ).first()

        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )

        # Generate MCP server from workflow
        result = await mcp_assistant.generate_mcp_from_workflow(
            workflow_id=request.workflow_id,
            analysis_id=analysis_id,
            target_language=request.target_language
        )

        return result

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to generate MCP from workflow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate MCP from workflow: {str(e)}"
        )


@router.post("/{analysis_id}/intelligent-analysis")
async def get_intelligent_mcp_analysis(
    analysis_id: int,
    request: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive, intelligent MCP analysis for repository"""

    try:
        user_goals = request.get('user_goals', 'general development workflow automation')

        # Get analysis data
        analysis = db.query(RepoAnalysis).filter(
            RepoAnalysis.id == analysis_id,
            RepoAnalysis.user_id == current_user.id
        ).first()

        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")

        # Extract repository information
        repo_url = analysis.repo_url
        if not repo_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Repository URL not found in analysis"
            )

        # Parse GitHub URL to get owner and repo name
        url_parts = repo_url.replace('https://github.com/', '').split('/')
        if len(url_parts) < 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GitHub repository URL"
            )

        repo_owner = url_parts[0]
        repo_name = url_parts[1]

        # Initialize MCP Architect service
        architect_service = MCPArchitectService()

        # Perform comprehensive MCP Architect analysis
        architect_analysis = await architect_service.analyze_repository_comprehensive(
            repo_owner=repo_owner,
            repo_name=repo_name,
            github_token=current_user.github_token,
            repo_info=analysis.analysis_results.get('repository_info') if analysis.analysis_results else None
        )

        # Convert MCP Architect analysis to dict for JSON response
        analysis_dict = {
            'domain': architect_analysis.repository_summary.get('purpose', 'general'),
            'architecture': {
                'architecture_type': architect_analysis.repository_summary.get('architecture', 'unknown'),
                'primary_purpose': architect_analysis.repository_summary.get('purpose', 'Unknown purpose'),
                'core_capabilities': [cap.get('name', '') for cap in architect_analysis.capability_inventory[:5]],
                'technology_stack': architect_analysis.repository_summary.get('tech_stack', []),
                'integration_points': [cap.get('underlying_tech', '') for cap in architect_analysis.capability_inventory if cap.get('exposed_via_api')],
                'deployment_patterns': ['containerized', 'cloud-native'],
                'api_types': ['REST', 'CLI']
            },
            'custom_mcp_suggestions': [
                {
                    'name': cap.get('name', ''),
                    'description': cap.get('description', ''),
                    'category': 'custom',
                    'implementation_approach': 'direct_integration',
                    'parameters': cap.get('parameters', []),
                    'return_type': 'json',
                    'business_value': 'high',
                    'use_cases': [cap.get('candidate_tool_name', '')],
                    'integration_points': [cap.get('underlying_tech', '')],
                    'code_examples': [],
                    'estimated_effort': 'medium'
                }
                for cap in architect_analysis.capability_inventory[:5]
            ],
            'existing_mcp_recommendations': [
                {
                    'name': mcp.name,
                    'description': f"Marketplace MCP for {', '.join(mcp.overlapping_tools)}",
                    'url': mcp.url,
                    'category': mcp.category,
                    'domain_relevance': 'high',
                    'capabilities': mcp.overlapping_tools,
                    'installation_method': 'npm',
                    'author': 'community',
                    'tags': [mcp.category],
                    'use_cases': [mcp.when_to_reuse],
                    'integration_examples': []
                }
                for mcp in architect_analysis.existing_mcp_servers[:5]
            ],
            'workflow_integrations': architect_analysis.gap_analysis,
            'implementation_roadmap': architect_analysis.new_mcp_server_specs,
            'total_estimated_effort': f"{len(architect_analysis.capability_inventory)} tools identified",
            'executive_summary': architect_analysis.executive_summary,
            'implementation_starter': architect_analysis.implementation_starter,
            'client_config': architect_analysis.client_config
        }

        return {
            "success": True,
            "analysis": analysis_dict,
            "message": f"MCP Architect analysis completed for {analysis_dict['domain']} repository"
        }

    except Exception as e:
        logger.error(f"Failed to generate intelligent MCP analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.post("/{analysis_id}/generate-requirements-driven")
async def generate_requirements_driven_mcp(
    analysis_id: int,
    request: RequirementsDrivenGenerateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate MCP server based on specific user requirements and use cases"""

    logger.info(f"Requirements-driven MCP generation for analysis {analysis_id}")

    try:
        # Verify analysis exists and belongs to user
        analysis = db.query(RepoAnalysis).filter(
            RepoAnalysis.id == analysis_id,
            RepoAnalysis.user_id == current_user.id
        ).first()

        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )

        if analysis.status != 'completed':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Analysis must be completed before generating MCP server"
            )

        # Prepare repository context
        analysis_results = analysis.analysis_results or {}
        repository_context = {
            'name': analysis.repo_name,
            'owner': analysis.repo_owner,
            'url': analysis.repo_url,
            'language': analysis_results.get('repository_info', {}).get('language'),
            'description': analysis_results.get('repository_info', {}).get('description'),
            'technologies': analysis_results.get('technologies', [])
        }

        # Initialize requirements-driven generator
        requirements_generator = RequirementsDrivenMCPGenerator()

        # Generate MCP server based on requirements
        result = await requirements_generator.generate_requirements_based_mcp(
            analysis_id=analysis_id,
            user_requirements=request.user_requirements,
            repository_context=repository_context,
            analysis_results=analysis_results,
            target_language=request.target_language
        )

        if not result['success']:
            return {
                "success": False,
                "error": result['error'],
                "missing_capabilities": result.get('missing_capabilities', []),
                "suggestions": result.get('suggestions', []),
                "requirements_analysis": result.get('requirements_analysis', {})
            }

        return {
            "success": True,
            "message": "Requirements-driven MCP server generated successfully",
            "requirements_analysis": result['requirements_analysis'],
            "generated_server": result['generated_server'],
            "tools_generated": result['tools_generated'],
            "requirements_coverage": result['requirements_coverage']
        }

    except Exception as e:
        logger.error(f"Failed to generate requirements-driven MCP: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate requirements-driven MCP server: {str(e)}"
        )