from pydantic_settings import BaseSettings
from typing import List, Optional, Any
import os
import json
from pydantic import field_validator


class Settings(BaseSettings):
    # Database - Use PostgreSQL for all environments
    database_url: str = "**************************************/supermcp"
    
    # GitHub OAuth
    github_client_id: Optional[str] = None
    github_client_secret: Optional[str] = None
    github_redirect_url: str = "http://localhost:3000/auth/callback"

    # JWT
    jwt_secret: Optional[str] = "dev-secret-key-change-in-production"
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 30
    
    # Redis
    redis_url: str = "redis://localhost:6379/0"
    
    # External APIs
    tavily_api_key: Optional[str] = None

    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None

    # Vector Database (Redis - Legacy)
    redis_vector_url: str = "redis://localhost:6379/2"

    # Vector Database Configuration
    weaviate_url: str = "http://weaviate:8080"
    weaviate_enabled: bool = True  # Primary vector storage for unlimited capacity

    # Vector Storage Strategy - WEAVIATE ONLY
    vector_storage_mode: str = "weaviate_only"  # ONLY Weaviate for vector storage
    
    # API Configuration
    api_v1_str: str = "/api/v1"
    project_name: str = "SuperMCP"
    project_version: str = "1.0.0"
    
    # Security
    secret_key: Optional[str] = "dev-secret-key-change-in-production"
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:3001"]

    @field_validator('cors_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v: Any) -> Any:
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("CORS_ORIGINS must be a valid JSON array string")
        return v
    
    # Celery
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"
    
    # Development
    debug: bool = False
    environment: str = "development"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra environment variables


settings = Settings()