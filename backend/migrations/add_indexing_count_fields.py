#!/usr/bin/env python3
"""
Add files_indexed and total_chunks fields to repo_analyses table
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy import text
from app.database import engine


def add_indexing_count_fields():
    """Add files_indexed and total_chunks fields to repo_analyses table"""
    
    print("🔧 Adding indexing count fields to repo_analyses table...")
    
    try:
        with engine.connect() as connection:
            # Check if fields already exist
            result = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'repo_analyses' 
                AND column_name IN ('files_indexed', 'total_chunks')
            """))
            
            existing_fields = [row[0] for row in result.fetchall()]
            
            # Add files_indexed field if it doesn't exist
            if 'files_indexed' not in existing_fields:
                print("  Adding files_indexed field...")
                connection.execute(text("""
                    ALTER TABLE repo_analyses 
                    ADD COLUMN files_indexed INTEGER DEFAULT 0
                """))
                print("  ✅ files_indexed field added")
            else:
                print("  ✅ files_indexed field already exists")
            
            # Add total_chunks field if it doesn't exist
            if 'total_chunks' not in existing_fields:
                print("  Adding total_chunks field...")
                connection.execute(text("""
                    ALTER TABLE repo_analyses 
                    ADD COLUMN total_chunks INTEGER DEFAULT 0
                """))
                print("  ✅ total_chunks field added")
            else:
                print("  ✅ total_chunks field already exists")
            
            # Commit the changes
            connection.commit()
            
        print("🎉 Indexing count fields migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = add_indexing_count_fields()
    sys.exit(0 if success else 1)
