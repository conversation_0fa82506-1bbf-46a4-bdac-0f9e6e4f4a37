"""Add parallel processing fields

Revision ID: 003_add_parallel_processing_fields
Revises: 002_remove_feasibility_score
Create Date: 2025-07-31 13:15:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003_parallel_fields'
down_revision = '002_remove_feasibility_score'
branch_labels = None
depends_on = None


def upgrade():
    # Add parallel processing fields to repo_analyses table
    op.add_column('repo_analyses', sa.Column('intermediate_data', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('repo_analyses', sa.Column('parallel_results', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('repo_analyses', sa.Column('parallel_mode', sa.<PERSON>an(), nullable=True, default=False))
    op.add_column('repo_analyses', sa.Column('workers_used', sa.Integer(), nullable=True))
    op.add_column('repo_analyses', sa.Column('processing_time_parallel', sa.Float(), nullable=True))


def downgrade():
    # Remove parallel processing fields
    op.drop_column('repo_analyses', 'processing_time_parallel')
    op.drop_column('repo_analyses', 'workers_used')
    op.drop_column('repo_analyses', 'parallel_mode')
    op.drop_column('repo_analyses', 'parallel_results')
    op.drop_column('repo_analyses', 'intermediate_data')
