"""Remove MCP feasibility score column

Revision ID: 002_remove_feasibility_score
Revises: 001_consolidated_supermcp_schema
Create Date: 2025-01-29 18:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002_remove_feasibility_score'
down_revision = '001_consolidated_supermcp_schema'
branch_labels = None
depends_on = None


def upgrade():
    """Remove mcp_feasibility_score column from repo_analyses table"""
    # Drop the column
    op.drop_column('repo_analyses', 'mcp_feasibility_score')


def downgrade():
    """Add back mcp_feasibility_score column to repo_analyses table"""
    # Add the column back
    op.add_column('repo_analyses', sa.Column('mcp_feasibility_score', sa.Float(), nullable=True))
