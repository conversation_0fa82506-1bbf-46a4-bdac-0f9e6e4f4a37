"""Consolidated SuperMCP Database Schema - Beta Version

Revision ID: 001_consolidated_supermcp_schema
Revises:
Create Date: 2025-07-29 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_consolidated_supermcp_schema'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### SuperMCP Consolidated Schema - All Tables ###
    
    # Create users table
    op.create_table('users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('github_id', sa.String(), nullable=False),
        sa.Column('username', sa.String(), nullable=False),
        sa.Column('email', sa.String(), nullable=True),
        sa.Column('full_name', sa.String(), nullable=True),
        sa.Column('avatar_url', sa.String(), nullable=True),
        sa.Column('github_token', sa.String(), nullable=False),
        sa.Column('is_active', sa.Bo<PERSON>an(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_github_id'), 'users', ['github_id'], unique=True)
    
    # Create repo_analyses table with ALL fields (consolidated from all migrations)
    op.create_table('repo_analyses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('repo_url', sa.String(), nullable=False),
        sa.Column('repo_name', sa.String(), nullable=False),
        sa.Column('repo_owner', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('mcp_feasibility_score', sa.Float(), nullable=True),
        sa.Column('analysis_results', sa.JSON(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        
        # Indexing and integration fields
        sa.Column('indexing_status', sa.String(), default='not_started'),
        sa.Column('indexing_progress', sa.Integer(), default=0),
        sa.Column('vector_db_id', sa.String(), nullable=True),
        sa.Column('integration_analysis', sa.JSON(), nullable=True),
        sa.Column('last_indexed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('code_hash', sa.String(), nullable=True),
        sa.Column('files_indexed', sa.Integer(), default=0),
        sa.Column('total_chunks', sa.Integer(), default=0),
        
        # Enhanced MCP analysis fields
        sa.Column('mcp_suggestions', sa.JSON(), nullable=True),
        sa.Column('specific_tools', sa.JSON(), nullable=True),
        sa.Column('functional_server_config', sa.JSON(), nullable=True),
        sa.Column('implementation_roadmap', sa.JSON(), nullable=True),
        sa.Column('enhanced_metrics', sa.JSON(), nullable=True),
        sa.Column('tools_confidence_score', sa.Float(), nullable=True),
        
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_repo_analyses_id'), 'repo_analyses', ['id'], unique=False)
    
    # Create dependencies table
    op.create_table('dependencies',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('version', sa.String(), nullable=True),
        sa.Column('language', sa.String(), nullable=False),
        sa.Column('dependency_type', sa.String(), nullable=False),
        sa.Column('file_path', sa.String(), nullable=False),
        sa.Column('mcp_potential', sa.Float(), nullable=True),
        sa.Column('existing_mcp_servers', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.ForeignKeyConstraint(['analysis_id'], ['repo_analyses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_dependencies_id'), 'dependencies', ['id'], unique=False)
    
    # Create mcp_categories table
    op.create_table('mcp_categories',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('icon', sa.String(), nullable=True),
        sa.Column('color', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_mcp_categories_id'), 'mcp_categories', ['id'], unique=False)
    op.create_index(op.f('ix_mcp_categories_name'), 'mcp_categories', ['name'], unique=False)
    
    # Create mcp_servers table
    op.create_table('mcp_servers',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('url', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category', sa.String(), nullable=False),
        sa.Column('github_url', sa.String(), nullable=True),
        sa.Column('npm_url', sa.String(), nullable=True),
        sa.Column('documentation_url', sa.String(), nullable=True),
        sa.Column('stars', sa.Integer(), nullable=True),
        sa.Column('language', sa.String(), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=False, default=0.0),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['category'], ['mcp_categories.name'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('url')
    )
    op.create_index(op.f('ix_mcp_servers_id'), 'mcp_servers', ['id'], unique=False)
    op.create_index(op.f('ix_mcp_servers_name'), 'mcp_servers', ['name'], unique=False)
    op.create_index(op.f('ix_mcp_servers_category'), 'mcp_servers', ['category'], unique=False)
    
    # Create detected_integrations table
    op.create_table('detected_integrations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.Integer(), nullable=False),
        sa.Column('integration_type', sa.String(), nullable=False),
        sa.Column('service_name', sa.String(), nullable=False),
        sa.Column('detection_method', sa.String(), nullable=False),
        sa.Column('confidence', sa.Float(), nullable=False, default=0.0),
        sa.Column('file_locations', sa.JSON(), nullable=True),
        sa.Column('package_names', sa.JSON(), nullable=True),
        sa.Column('code_patterns', sa.JSON(), nullable=True),
        sa.Column('env_variables', sa.JSON(), nullable=True),
        sa.Column('migration_complexity', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('mcp_alternatives', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['analysis_id'], ['repo_analyses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_detected_integrations_id'), 'detected_integrations', ['id'], unique=False)
    
    # Create extracted_tools table
    op.create_table('extracted_tools',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('implementation_type', sa.String(), nullable=False),
        sa.Column('source_functions', sa.JSON(), nullable=True),
        sa.Column('source_files', sa.JSON(), nullable=True),
        sa.Column('business_value', sa.Text(), nullable=True),
        sa.Column('implementation_effort', sa.String(), nullable=False),
        sa.Column('input_schema', sa.JSON(), nullable=True),
        sa.Column('output_schema', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['analysis_id'], ['repo_analyses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extracted_tools_id'), 'extracted_tools', ['id'], unique=False)
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_extracted_tools_id'), table_name='extracted_tools')
    op.drop_table('extracted_tools')
    op.drop_index(op.f('ix_detected_integrations_id'), table_name='detected_integrations')
    op.drop_table('detected_integrations')
    op.drop_index(op.f('ix_mcp_servers_category'), table_name='mcp_servers')
    op.drop_index(op.f('ix_mcp_servers_name'), table_name='mcp_servers')
    op.drop_index(op.f('ix_mcp_servers_id'), table_name='mcp_servers')
    op.drop_table('mcp_servers')
    op.drop_index(op.f('ix_mcp_categories_name'), table_name='mcp_categories')
    op.drop_index(op.f('ix_mcp_categories_id'), table_name='mcp_categories')
    op.drop_table('mcp_categories')
    op.drop_index(op.f('ix_dependencies_id'), table_name='dependencies')
    op.drop_table('dependencies')
    op.drop_index(op.f('ix_repo_analyses_id'), table_name='repo_analyses')
    op.drop_table('repo_analyses')
    op.drop_index(op.f('ix_users_github_id'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
