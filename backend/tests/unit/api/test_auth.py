"""
Unit tests for authentication API endpoints
"""
import pytest
from unittest.mock import patch, MagicMock
from fastapi import status
from app.services.github_service import GitHubService


@pytest.mark.unit
@pytest.mark.auth
class TestAuthEndpoints:
    """Test authentication API endpoints."""
    
    def test_login_endpoint_returns_github_url(self, client):
        """Test that login endpoint returns GitHub OAuth URL."""
        with patch.object(GitHubService, 'get_oauth_url') as mock_oauth:
            mock_oauth.return_value = "https://github.com/login/oauth/authorize?client_id=test"
            
            response = client.get("/api/v1/auth/login")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "auth_url" in data
            assert "github.com" in data["auth_url"]
            mock_oauth.assert_called_once()
    
    @patch('app.api.auth.GitHubService')
    def test_callback_success(self, mock_github_service, client, db_session):
        """Test successful OAuth callback."""
        # Mock GitHub service responses
        mock_service = MagicMock()
        mock_github_service.return_value = mock_service
        mock_service.exchange_code_for_token.return_value = "test_access_token"
        mock_service.get_user_info.return_value = {
            "id": 12345,
            "login": "testuser",
            "email": "<EMAIL>",
            "name": "Test User",
            "avatar_url": "https://avatars.githubusercontent.com/u/12345"
        }
        
        response = client.get("/api/v1/auth/callback?code=test_code")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "access_token" in data
        assert "user" in data
        assert data["user"]["username"] == "testuser"
    
    @patch('app.api.auth.GitHubService')
    def test_callback_invalid_code(self, mock_github_service, client):
        """Test OAuth callback with invalid code."""
        mock_service = MagicMock()
        mock_github_service.return_value = mock_service
        mock_service.exchange_code_for_token.side_effect = Exception("Invalid code")
        
        response = client.get("/api/v1/auth/callback?code=invalid_code")
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "detail" in data
    
    def test_callback_missing_code(self, client):
        """Test OAuth callback without code parameter."""
        response = client.get("/api/v1/auth/callback")
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "Code parameter is required" in data["detail"]
    
    def test_me_endpoint_authenticated(self, authenticated_client, test_user):
        """Test /me endpoint with authenticated user."""
        response = authenticated_client.get("/api/v1/auth/me")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == test_user.id
        assert data["username"] == test_user.username
        assert data["email"] == test_user.email
    
    def test_me_endpoint_unauthenticated(self, client):
        """Test /me endpoint without authentication."""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_logout_authenticated(self, authenticated_client):
        """Test logout with authenticated user."""
        response = authenticated_client.post("/api/v1/auth/logout")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
    
    def test_logout_unauthenticated(self, client):
        """Test logout without authentication."""
        response = client.post("/api/v1/auth/logout")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED