"""
Unit tests for analysis API endpoints
"""
import pytest
from unittest.mock import patch, MagicMock
from fastapi import status


@pytest.mark.unit
@pytest.mark.api
class TestAnalysisEndpoints:
    """Test analysis API endpoints."""
    
    def test_create_analysis_success(self, authenticated_client, test_user):
        """Test successful analysis creation."""
        analysis_data = {
            "repo_url": "https://github.com/test/repo",
            "repo_name": "repo",
            "repo_owner": "test"
        }
        
        with patch('app.api.analysis.start_repository_analysis') as mock_task:
            mock_task.delay.return_value = MagicMock(id="test_task_id")
            
            response = authenticated_client.post("/api/v1/analysis/", json=analysis_data)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["repo_name"] == "repo"
            assert data["repo_owner"] == "test"
            assert data["status"] == "pending"
            assert data["user_id"] == test_user.id
    
    def test_create_analysis_unauthenticated(self, client):
        """Test analysis creation without authentication."""
        analysis_data = {
            "repo_url": "https://github.com/test/repo",
            "repo_name": "repo",
            "repo_owner": "test"
        }
        
        response = client.post("/api/v1/analysis/", json=analysis_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_create_analysis_duplicate_recent(self, authenticated_client, test_user, db_session):
        """Test analysis creation with recent duplicate."""
        # Create existing analysis first
        from app.models.analysis import RepoAnalysis
        from datetime import datetime, timedelta
        
        existing_analysis = RepoAnalysis(
            user_id=test_user.id,
            repo_url="https://github.com/test/repo",
            repo_name="repo",
            repo_owner="test",
            status="completed",
            created_at=datetime.utcnow() - timedelta(hours=1)  # Recent
        )
        db_session.add(existing_analysis)
        db_session.commit()
        
        analysis_data = {
            "repo_url": "https://github.com/test/repo",
            "repo_name": "repo",
            "repo_owner": "test"
        }
        
        response = authenticated_client.post("/api/v1/analysis/", json=analysis_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == existing_analysis.id  # Returns existing analysis
    
    def test_get_analysis_success(self, authenticated_client, test_analysis):
        """Test successful analysis retrieval."""
        response = authenticated_client.get(f"/api/v1/analysis/{test_analysis.id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == test_analysis.id
        assert data["repo_name"] == test_analysis.repo_name
        assert data["status"] == test_analysis.status
    
    def test_get_analysis_not_found(self, authenticated_client):
        """Test analysis retrieval with non-existent ID."""
        response = authenticated_client.get("/api/v1/analysis/99999")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_get_analysis_wrong_user(self, client, test_analysis, db_session):
        """Test analysis retrieval by different user."""
        # Create another user
        from app.models.user import User
        from app.utils.auth import create_access_token
        
        other_user = User(
            github_id=54321,
            username="otheruser",
            email="<EMAIL>",
            github_token="other_token"
        )
        db_session.add(other_user)
        db_session.commit()
        
        other_token = create_access_token(data={"sub": str(other_user.id)})
        client.headers = {"Authorization": f"Bearer {other_token}"}
        
        response = client.get(f"/api/v1/analysis/{test_analysis.id}")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_get_user_analyses(self, authenticated_client, test_analysis):
        """Test getting user's analyses."""
        response = authenticated_client.get("/api/v1/analysis/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        assert any(analysis["id"] == test_analysis.id for analysis in data)
    
    def test_get_analysis_status(self, authenticated_client, test_analysis):
        """Test getting analysis status."""
        response = authenticated_client.get(f"/api/v1/analysis/{test_analysis.id}/status")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "analysis_id" in data
        assert "status" in data
        assert data["analysis_id"] == test_analysis.id
        assert data["status"] == test_analysis.status
    
    def test_get_analysis_dependencies(self, authenticated_client, test_analysis, test_dependency):
        """Test getting analysis dependencies."""
        response = authenticated_client.get(f"/api/v1/analysis/{test_analysis.id}/dependencies")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        assert any(dep["id"] == test_dependency.id for dep in data)
    
    def test_get_analysis_dependencies_with_filters(self, authenticated_client, test_analysis, test_dependency):
        """Test getting analysis dependencies with filters."""
        response = authenticated_client.get(
            f"/api/v1/analysis/{test_analysis.id}/dependencies",
            params={"language": "python", "min_mcp_potential": 80.0}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        # Should include test_dependency (Python, 85.0 potential)
        assert any(dep["id"] == test_dependency.id for dep in data)
    
    def test_get_analysis_stats(self, authenticated_client, test_analysis):
        """Test getting analysis statistics."""
        response = authenticated_client.get("/api/v1/analysis/stats")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "total_analyses" in data
        assert "completed_analyses" in data
        assert "failed_analyses" in data
        assert "pending_analyses" in data
        assert data["total_analyses"] >= 1
    
    def test_retry_analysis(self, authenticated_client, test_user, db_session):
        """Test retrying a failed analysis."""
        from app.models.analysis import RepoAnalysis
        
        failed_analysis = RepoAnalysis(
            user_id=test_user.id,
            repo_url="https://github.com/test/failed",
            repo_name="failed",
            repo_owner="test",
            status="failed",
            error_message="Test error"
        )
        db_session.add(failed_analysis)
        db_session.commit()
        
        with patch('app.api.analysis.start_repository_analysis') as mock_task:
            mock_task.delay.return_value = MagicMock(id="retry_task_id")
            
            response = authenticated_client.post(f"/api/v1/analysis/{failed_analysis.id}/retry")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "message" in data
            assert "task_id" in data
    
    def test_delete_analysis(self, authenticated_client, test_analysis):
        """Test deleting an analysis."""
        response = authenticated_client.delete(f"/api/v1/analysis/{test_analysis.id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
        
        # Verify analysis is deleted
        get_response = authenticated_client.get(f"/api/v1/analysis/{test_analysis.id}")
        assert get_response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_export_analysis_json(self, authenticated_client, test_analysis, test_dependency):
        """Test exporting analysis as JSON."""
        response = authenticated_client.get(f"/api/v1/analysis/{test_analysis.id}/export?format=json")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "analysis" in data
        assert "dependencies" in data
        assert data["analysis"]["id"] == test_analysis.id
        assert len(data["dependencies"]) >= 1
    
    def test_export_analysis_csv(self, authenticated_client, test_analysis, test_dependency):
        """Test exporting analysis as CSV."""
        response = authenticated_client.get(f"/api/v1/analysis/{test_analysis.id}/export?format=csv")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "csv_data" in data
        assert "Analysis ID" in data["csv_data"]  # CSV header
    
    def test_export_analysis_incomplete(self, authenticated_client, test_user, db_session):
        """Test exporting incomplete analysis."""
        from app.models.analysis import RepoAnalysis
        
        pending_analysis = RepoAnalysis(
            user_id=test_user.id,
            repo_url="https://github.com/test/pending",
            repo_name="pending",
            repo_owner="test",
            status="pending"
        )
        db_session.add(pending_analysis)
        db_session.commit()
        
        response = authenticated_client.get(f"/api/v1/analysis/{pending_analysis.id}/export")
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST