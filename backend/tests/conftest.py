"""
Pytest configuration and fixtures for SuperMCP testing
"""
import asyncio
import pytest
import tempfile
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Import application components
from app.main import app
from app.database import get_db, Base
from app.config import settings
from app.models.user import User
from app.models.analysis import RepoAnalysis, Dependency
from app.utils.auth import create_access_token

# Test database URL
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def test_db():
    """Create a fresh database for each test."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(test_db):
    """Create a database session for testing."""
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
async def async_client(db_session):
    """Create an async test client."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    async with AsyncClient(app=app, base_url="http://test") as async_test_client:
        yield async_test_client
    app.dependency_overrides.clear()


@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    user = User(
        github_id=12345,
        username="testuser",
        email="<EMAIL>",
        name="Test User",
        avatar_url="https://avatars.githubusercontent.com/u/12345",
        github_token="test_token_123"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_user_token(test_user):
    """Create an access token for the test user."""
    return create_access_token(data={"sub": str(test_user.id)})


@pytest.fixture
def authenticated_client(client, test_user_token):
    """Create a client with authentication headers."""
    client.headers = {"Authorization": f"Bearer {test_user_token}"}
    return client


@pytest.fixture
async def authenticated_async_client(async_client, test_user_token):
    """Create an async client with authentication headers."""
    async_client.headers = {"Authorization": f"Bearer {test_user_token}"}
    return async_client


@pytest.fixture
def test_analysis(db_session, test_user):
    """Create a test analysis."""
    analysis = RepoAnalysis(
        user_id=test_user.id,
        repo_url="https://github.com/test/repo",
        repo_name="repo",
        repo_owner="test",
        status="completed",
        # mcp_feasibility_score removed - using conversational analysis instead
        analysis_results={
            "complexity_score": 80,
            "api_endpoints": 15,
            "cli_interface": True,
            "relevant_dependencies": 10
        }
    )
    db_session.add(analysis)
    db_session.commit()
    db_session.refresh(analysis)
    return analysis


@pytest.fixture
def test_dependency(db_session, test_analysis):
    """Create a test dependency."""
    dependency = Dependency(
        analysis_id=test_analysis.id,
        name="fastapi",
        version="0.104.1",
        language="python",
        dependency_type="production",
        file_path="requirements.txt",
        mcp_potential=85.0,
        existing_mcp_servers=[
            {"name": "FastAPI MCP Server", "url": "https://github.com/example/fastapi-mcp"}
        ]
    )
    db_session.add(dependency)
    db_session.commit()
    db_session.refresh(dependency)
    return dependency


@pytest.fixture
def mock_github_user_data():
    """Mock GitHub user data."""
    return {
        "id": 12345,
        "login": "testuser",
        "email": "<EMAIL>",
        "name": "Test User",
        "avatar_url": "https://avatars.githubusercontent.com/u/12345"
    }


@pytest.fixture
def mock_github_repo_data():
    """Mock GitHub repository data."""
    return {
        "id": 67890,
        "name": "test-repo",
        "full_name": "testuser/test-repo",
        "owner": {"login": "testuser"},
        "description": "A test repository",
        "html_url": "https://github.com/testuser/test-repo",
        "clone_url": "https://github.com/testuser/test-repo.git",
        "language": "Python",
        "stargazers_count": 100,
        "forks_count": 25,
        "size": 1024,
        "private": False,
        "topics": ["python", "api", "test"],
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-12-01T00:00:00Z",
        "default_branch": "main"
    }


@pytest.fixture
def temp_file():
    """Create a temporary file for testing."""
    fd, path = tempfile.mkstemp()
    try:
        yield path
    finally:
        os.close(fd)
        os.unlink(path)


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


# Pytest markers for different test types
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.e2e = pytest.mark.e2e
pytest.mark.slow = pytest.mark.slow
pytest.mark.auth = pytest.mark.auth
pytest.mark.api = pytest.mark.api
pytest.mark.database = pytest.mark.database
pytest.mark.celery = pytest.mark.celery