"""
Integration tests for authentication flow
"""
import pytest
from unittest.mock import patch, MagicMock
from fastapi import status


@pytest.mark.integration
@pytest.mark.auth
class TestAuthenticationFlow:
    """Test complete authentication workflow."""
    
    @patch('app.api.auth.GitHubService')
    async def test_complete_oauth_flow(self, mock_github_service, async_client, db_session):
        """Test complete OAuth authentication flow."""
        # Mock GitHub service
        mock_service = MagicMock()
        mock_github_service.return_value = mock_service
        mock_service.get_oauth_url.return_value = "https://github.com/login/oauth/authorize?client_id=test&redirect_uri=http://localhost:3000/auth/callback&scope=user:email,repo,read:org&response_type=code"
        mock_service.exchange_code_for_token.return_value = "github_access_token_123"
        mock_service.get_user_info.return_value = {
            "id": 12345,
            "login": "testuser",
            "email": "<EMAIL>",
            "name": "Test User",
            "avatar_url": "https://avatars.githubusercontent.com/u/12345"
        }
        
        # Step 1: Get OAuth URL
        response = await async_client.get("/api/v1/auth/login")
        assert response.status_code == status.HTTP_200_OK
        auth_data = response.json()
        assert "auth_url" in auth_data
        assert "github.com" in auth_data["auth_url"]
        
        # Step 2: Simulate OAuth callback
        response = await async_client.get("/api/v1/auth/callback?code=oauth_code_123")
        assert response.status_code == status.HTTP_200_OK
        callback_data = response.json()
        assert "access_token" in callback_data
        assert "user" in callback_data
        
        access_token = callback_data["access_token"]
        user_data = callback_data["user"]
        
        # Verify user data
        assert user_data["username"] == "testuser"
        assert user_data["email"] == "<EMAIL>"
        assert user_data["github_id"] == 12345
        
        # Step 3: Use access token to access protected endpoint
        headers = {"Authorization": f"Bearer {access_token}"}
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == status.HTTP_200_OK
        me_data = response.json()
        assert me_data["username"] == "testuser"
        assert me_data["id"] == user_data["id"]
        
        # Step 4: Logout
        response = await async_client.post("/api/v1/auth/logout", headers=headers)
        assert response.status_code == status.HTTP_200_OK
        logout_data = response.json()
        assert "message" in logout_data
    
    @patch('app.api.auth.GitHubService')
    async def test_oauth_error_handling(self, mock_github_service, async_client):
        """Test OAuth error handling."""
        mock_service = MagicMock()
        mock_github_service.return_value = mock_service
        
        # Test invalid code
        mock_service.exchange_code_for_token.side_effect = Exception("The code passed is incorrect or expired")
        
        response = await async_client.get("/api/v1/auth/callback?code=invalid_code")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        error_data = response.json()
        assert "detail" in error_data
        assert "incorrect or expired" in error_data["detail"]
    
    async def test_token_validation(self, async_client, test_user_token):
        """Test JWT token validation."""
        headers = {"Authorization": f"Bearer {test_user_token}"}
        
        # Valid token should work
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == status.HTTP_200_OK
        
        # Invalid token should fail
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        response = await async_client.get("/api/v1/auth/me", headers=invalid_headers)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
        # Missing token should fail
        response = await async_client.get("/api/v1/auth/me")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    @patch('app.api.auth.GitHubService')
    async def test_user_creation_and_update(self, mock_github_service, async_client, db_session):
        """Test user creation and profile updates."""
        mock_service = MagicMock()
        mock_github_service.return_value = mock_service
        mock_service.exchange_code_for_token.return_value = "github_token"
        
        # First login - user creation
        mock_service.get_user_info.return_value = {
            "id": 98765,
            "login": "newuser",
            "email": "<EMAIL>",
            "name": "New User",
            "avatar_url": "https://avatars.githubusercontent.com/u/98765"
        }
        
        response = await async_client.get("/api/v1/auth/callback?code=new_user_code")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        user_id = data["user"]["id"]
        
        # Second login - user update
        mock_service.get_user_info.return_value = {
            "id": 98765,
            "login": "newuser",
            "email": "<EMAIL>",  # Updated email
            "name": "Updated User",  # Updated name
            "avatar_url": "https://avatars.githubusercontent.com/u/98765"
        }
        
        response = await async_client.get("/api/v1/auth/callback?code=update_user_code")
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should be same user ID but updated info
        assert data["user"]["id"] == user_id
        assert data["user"]["email"] == "<EMAIL>"
        assert data["user"]["name"] == "Updated User"
    
    async def test_concurrent_authentication(self, async_client):
        """Test concurrent authentication requests."""
        import asyncio
        from unittest.mock import patch, MagicMock
        
        async def authenticate_user(user_id):
            with patch('app.api.auth.GitHubService') as mock_github_service:
                mock_service = MagicMock()
                mock_github_service.return_value = mock_service
                mock_service.exchange_code_for_token.return_value = f"token_{user_id}"
                mock_service.get_user_info.return_value = {
                    "id": user_id,
                    "login": f"user{user_id}",
                    "email": f"user{user_id}@example.com",
                    "name": f"User {user_id}",
                    "avatar_url": f"https://avatars.githubusercontent.com/u/{user_id}"
                }
                
                response = await async_client.get(f"/api/v1/auth/callback?code=code_{user_id}")
                return response
        
        # Simulate concurrent authentication
        tasks = [authenticate_user(i) for i in range(1, 6)]
        responses = await asyncio.gather(*tasks)
        
        # All should succeed
        for response in responses:
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "access_token" in data
            assert "user" in data