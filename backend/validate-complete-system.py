#!/usr/bin/env python3
"""
Complete System Validation Script

Validates all the fixes made to the SuperMCP system:
1. Repository analysis data structure
2. Weaviate indexing and vector storage
3. Frontend data display compatibility
4. Database integrity
"""

import sys
import asyncio
sys.path.append('/app')

from app.database import SessionLocal
from app.models.analysis import RepoAnalysis
from app.services.weaviate_vector_service import WeaviateVectorService
from app.services.indexing_service import IndexingService

def validate_database_structure():
    """Validate database has correct analysis structure"""
    print("=== DATABASE STRUCTURE VALIDATION ===")

    db = SessionLocal()
    try:
        # Get the most recent completed analysis instead of hardcoding ID 1
        analysis = db.query(RepoAnalysis).filter(
            RepoAnalysis.status == "completed"
        ).order_by(RepoAnalysis.id.desc()).first()

        if not analysis:
            print("❌ No completed analysis found")
            return False
            
        print(f"✅ Analysis found: {analysis.repo_owner}/{analysis.repo_name}")
        print(f"✅ Status: {analysis.status}")
        
        # Check analysis_results structure
        if not analysis.analysis_results:
            print("❌ analysis_results is None")
            return False
            
        results = analysis.analysis_results
        required_keys = ['repository_info', 'technology_stack', 'code_structure']
        missing_keys = [key for key in required_keys if key not in results]
        
        if missing_keys:
            print(f"❌ Missing keys in analysis_results: {missing_keys}")
            return False
            
        print("✅ analysis_results has required structure")
        
        # Check repository_info
        repo_info = results.get('repository_info', {})
        if repo_info.get('language') and repo_info.get('description'):
            print(f"✅ Repository info: {repo_info.get('language')} - {repo_info.get('description')[:50]}...")
        else:
            print("❌ Repository info missing language or description")
            return False
            
        # Check technology_stack
        tech_stack = results.get('technology_stack', {})
        if tech_stack.get('primary_language') and tech_stack.get('total_files'):
            print(f"✅ Technology stack: {tech_stack.get('primary_language')} - {tech_stack.get('total_files')} files")
        else:
            print("❌ Technology stack missing primary_language or total_files")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Database validation error: {e}")
        return False
    finally:
        db.close()

async def validate_weaviate_integration():
    """Validate Weaviate vector storage"""
    print("\n=== WEAVIATE INTEGRATION VALIDATION ===")
    
    try:
        service = WeaviateVectorService()
        await service.connect()
        print("✅ Connected to Weaviate")
        
        # Check schema
        schema = service.client.schema.get()
        classes = [cls["class"] for cls in schema.get("classes", [])]
        if "CodeChunk" in classes:
            print("✅ CodeChunk schema exists")
        else:
            print("❌ CodeChunk schema missing")
            return False
            
        # Get the most recent completed analysis ID
        db = SessionLocal()
        try:
            latest_analysis = db.query(RepoAnalysis).filter(
                RepoAnalysis.status == "completed"
            ).order_by(RepoAnalysis.id.desc()).first()

            if not latest_analysis:
                print("❌ No completed analysis found for Weaviate validation")
                return False

            analysis_id = latest_analysis.id
            print(f"✅ Validating Weaviate data for analysis {analysis_id} ({latest_analysis.repo_owner}/{latest_analysis.repo_name})")
        finally:
            db.close()

        # Check data for the latest analysis
        stats = await service.get_repository_stats(analysis_id)
        total_chunks = stats.get('total_chunks', 0)
        
        if total_chunks > 0:
            print(f"✅ Found {total_chunks} code chunks in Weaviate")
            print(f"✅ Languages: {list(stats.get('languages', {}).keys())}")
        else:
            print("❌ No code chunks found in Weaviate")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Weaviate validation error: {e}")
        return False

async def validate_indexing_service():
    """Validate indexing service reports correct data"""
    print("\n=== INDEXING SERVICE VALIDATION ===")

    try:
        # Get the most recent completed analysis ID
        db = SessionLocal()
        try:
            latest_analysis = db.query(RepoAnalysis).filter(
                RepoAnalysis.status == "completed"
            ).order_by(RepoAnalysis.id.desc()).first()

            if not latest_analysis:
                print("❌ No completed analysis found for indexing validation")
                return False

            analysis_id = latest_analysis.id
            print(f"✅ Validating indexing service for analysis {analysis_id} ({latest_analysis.repo_owner}/{latest_analysis.repo_name})")
        finally:
            db.close()

        service = IndexingService()
        status = await service.get_indexing_status(analysis_id)
        
        print(f"✅ Indexing status: {status.get('indexing_status')}")
        print(f"✅ Files indexed: {status.get('files_indexed')}")
        print(f"✅ Total chunks: {status.get('total_chunks')}")
        print(f"✅ Validation status: {status.get('validation_status')}")
        
        if status.get('indexing_status') == 'completed' and status.get('total_chunks', 0) > 0:
            print("✅ Indexing service reports successful completion")
            return True
        else:
            print("❌ Indexing service reports issues")
            return False
            
    except Exception as e:
        print(f"❌ Indexing service validation error: {e}")
        return False

def validate_frontend_compatibility():
    """Validate data structure is compatible with frontend expectations"""
    print("\n=== FRONTEND COMPATIBILITY VALIDATION ===")

    db = SessionLocal()
    try:
        # Get the most recent completed analysis instead of hardcoding ID 1
        analysis = db.query(RepoAnalysis).filter(
            RepoAnalysis.status == "completed"
        ).order_by(RepoAnalysis.id.desc()).first()

        if not analysis:
            print("❌ No completed analysis found")
            return False

        print(f"✅ Validating frontend compatibility for analysis {analysis.id} ({analysis.repo_owner}/{analysis.repo_name})")
            
        # Check if frontend can find primary language
        results = analysis.analysis_results or {}
        intermediate = analysis.intermediate_data or {}
        
        # Frontend logic simulation
        enhanced_tech_stack = intermediate.get('enhanced_results', {}).get('technology_stack', {})
        primary_lang_enhanced = enhanced_tech_stack.get('primary_language')
        
        repo_info = results.get('repository_info', {})
        primary_lang_repo = repo_info.get('language')
        
        if primary_lang_enhanced or primary_lang_repo:
            primary_lang = primary_lang_enhanced or primary_lang_repo
            print(f"✅ Frontend can find primary language: {primary_lang}")
        else:
            print("❌ Frontend cannot find primary language")
            return False
            
        # Check language percentages
        languages_enhanced = enhanced_tech_stack.get('languages', {})
        languages_repo = repo_info.get('language_percentages', {})
        
        if languages_enhanced or languages_repo:
            languages = languages_enhanced or languages_repo
            print(f"✅ Frontend can find language breakdown: {len(languages)} languages")
        else:
            print("❌ Frontend cannot find language breakdown")
            return False
            
        # Check repository structure
        repo_structure = enhanced_tech_stack.get('repository_structure', {})
        code_structure = results.get('code_structure', {})
        
        if repo_structure or code_structure:
            total_files = repo_structure.get('total_files') or code_structure.get('file_count')
            if total_files:
                print(f"✅ Frontend can find repository structure: {total_files} files")
            else:
                print("❌ Frontend cannot find file count")
                return False
        else:
            print("❌ Frontend cannot find repository structure")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Frontend compatibility validation error: {e}")
        return False
    finally:
        db.close()

async def main():
    """Run all validations"""
    print("🔍 Starting comprehensive system validation...\n")
    
    validations = [
        ("Database Structure", validate_database_structure()),
        ("Weaviate Integration", await validate_weaviate_integration()),
        ("Indexing Service", await validate_indexing_service()),
        ("Frontend Compatibility", validate_frontend_compatibility())
    ]
    
    print("\n=== VALIDATION SUMMARY ===")
    all_passed = True
    for name, result in validations:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
        if not result:
            all_passed = False
    
    print(f"\n{'🎉 ALL VALIDATIONS PASSED!' if all_passed else '⚠️  SOME VALIDATIONS FAILED'}")
    
    if all_passed:
        print("\n✅ The SuperMCP system is working correctly:")
        print("   - Repository analysis data is properly structured")
        print("   - Weaviate vector storage is functional")
        print("   - Code indexing is working correctly")
        print("   - Frontend can display all data properly")
        print("\n🚀 The system is ready for production use!")
    else:
        print("\n❌ Please review the failed validations above")

if __name__ == "__main__":
    asyncio.run(main())
