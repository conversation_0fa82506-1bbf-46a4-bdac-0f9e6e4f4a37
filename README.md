<div align="center">
  <img src="supermcp.png" alt="SuperMCP Logo" width="200" height="200">

  # SuperMCP

  **Requirements-driven MCP generation with complete workflow integration and zero fallbacks policy.**

  [![MIT License](https://img.shields.io/badge/License-MIT-green.svg)](https://choosealicense.com/licenses/mit/)
  [![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
  [![FastAPI](https://img.shields.io/badge/FastAPI-Backend-green.svg)](https://fastapi.tiangolo.com)
  [![Next.js](https://img.shields.io/badge/Next.js-Frontend-black.svg)](https://nextjs.org)
</div>

## Overview

SuperMCP is a comprehensive, open source tool that provides complete workflow integration for MCP generation:
- **Requirements-driven generation**: Validates user requirements against real repository capabilities
- **Real code extraction**: Uses actual repository functions and implementations (zero fallbacks)
- **Complete workflow**: Six-stage process from analysis to functional MCP deployment
- **Enhanced conversation**: Context-aware chat with repository knowledge and validation
- **Functional MCP servers**: Generates working tools with real business logic

## Architecture

<div align="center">
  <img src="SuperMCP_arch.png" alt="SuperMCP Architecture" width="800">
  <p><em>SuperMCP system architecture showing the complete analysis and generation pipeline</em></p>
</div>

## Features

### 🔄 **Complete Workflow Integration**
- **Six-Stage Workflow**: Repository Analysis → Code Indexing → MCP Analysis → Conversation → Validation → Generation
- **Real-time State Tracking**: Live workflow progress with stage completion status
- **Enhanced Conversation**: Context-aware chat with full repository knowledge
- **Requirements Validation**: Real-time feasibility checking against repository capabilities

### 🚫 **Zero Fallbacks Policy**
- **Real Code Only**: Uses actual repository functions and implementations
- **Fail-Fast Design**: System fails immediately when real data isn't available
- **No Mock Data**: Zero placeholder, template, or mock implementations
- **Functional Tools**: Generated MCP servers use real business logic from repository

### 🔐 **Security & Authentication**
- **GitHub OAuth Integration**: Secure authentication with repository access
- **JWT Token Management**: Secure session management with automatic token refresh
- **Private Repository Support**: Full access to private repositories with proper authentication

### 📊 **Comprehensive Reporting**
- **Detailed Analysis Results**: In-depth reports with intelligent recommendations and implementation guidance
- **Dependency Analysis**: Complete dependency tree analysis with MCP potential assessment
- **API Endpoint Detection**: Automatic discovery of REST APIs, GraphQL endpoints, and CLI interfaces
- **Implementation Guides**: Step-by-step guides for MCP server implementation

### 🎨 **Modern User Experience**
- **Clean, Professional UI**: Modern interface designed for developers using shadcn/ui components
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Real-time Updates**: Live progress tracking and status updates during analysis
- **Error Handling**: Comprehensive error handling with helpful user feedback

### 🔓 **Open Source & Deployment**
- **MIT License**: Completely open source - deploy and customize your own instance
- **Docker Ready**: Complete Docker Compose setup for easy deployment
- **Environment Configuration**: Flexible configuration via environment variables
- **Production Ready**: Includes production deployment configurations with Nginx and SSL

## 🆕 Latest Updates

### Complete Workflow Integration (v2.0.0)
- **✅ Six-Stage Workflow**: Complete integration from repository analysis to MCP deployment
- **✅ Enhanced Conversation**: Context-aware chat with full repository knowledge and validation
- **✅ Requirements-Driven Generation**: Validates user requirements against real repository capabilities
- **✅ Real Code Extraction**: Uses actual repository functions and implementations
- **✅ Zero Fallbacks Policy**: Removed all mock data, placeholders, and fallback implementations

### Functional MCP Generation
- **✅ Real Repository Functions**: Extracts and uses actual functions from repository code
- **✅ Capability Indexing**: Complete code indexing with searchable capability database
- **✅ Feasibility Validation**: Real-time scoring of requirement feasibility (0-100%)
- **✅ Fail-Fast Design**: System fails immediately when real data isn't available

### Enhanced Architecture
- **✅ Unified Context Service**: Centralized repository data and capability management
- **✅ Workflow State Tracking**: Real-time progress through all workflow stages
- **✅ Enhanced API Endpoints**: New endpoints for conversation and workflow management
- **✅ Database Integration**: Proper session management and analysis persistence

## Tech Stack

### Backend
- **Framework**: FastAPI
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Task Queue**: Celery with Redis
- **Authentication**: GitHub OAuth + JWT

### Frontend
- **Framework**: Next.js 14 with TypeScript
- **UI Library**: shadcn/ui components
- **Styling**: Tailwind CSS with clean, modern design

### Infrastructure
- **Development**: Docker Compose
- **Database**: PostgreSQL 15
- **Cache/Queue**: Redis 7

## 🔄 Complete Workflow Process

SuperMCP provides a comprehensive six-stage workflow for generating functional MCP servers:

### **Stage 1: Repository Analysis**
- Deep analysis of repository structure, dependencies, and technologies
- Extraction of API endpoints, functions, and business logic
- Technology stack identification and compatibility assessment

### **Stage 2: Code Indexing**
- Complete indexing of all code files and functions
- Capability extraction with searchable database
- Function signature and parameter mapping

### **Stage 3: MCP Analysis**
- Repository-specific MCP suggestions generation
- Marketplace integration and existing MCP identification
- Implementation roadmap with effort estimates

### **Stage 4: Enhanced Conversation**
- Context-aware chat with full repository knowledge
- Real-time requirement validation against capabilities
- Interactive refinement of MCP requirements

### **Stage 5: Requirements Validation**
- Feasibility scoring (0-100%) for user requirements
- Missing capability identification with suggestions
- Scope validation to prevent out-of-scope generations

### **Stage 6: MCP Generation**
- Functional MCP server generation using real repository code
- Actual function extraction and implementation
- Complete deployment package with Docker configuration

## Quick Start

### 🚀 Fresh Development Environment

**Complete Environment Reset (includes database, cache, and services):**
```bash
./reset-env.sh
```

The environment reset will:
- 🛑 Stop all Docker services
- 🗑️ Remove containers, volumes, and networks
- 🧹 Clean database and Redis cache
- 🚀 Start fresh services with clean state
- 🔄 Run database migrations
- ✅ Ready for development in ~2 minutes

After running either script, visit `http://localhost:3000` and sign up with your GitHub account.

### 🐳 **Unified Docker Configuration**

We use a single `docker-compose.yml` file that works for both development and production:

**Development (default):**
```bash
docker-compose up -d
```

**Production:**
```bash
# Copy and customize production config
cp .env.example .env
# Edit .env with your production values (see production section in .env.example)
docker-compose --profile production up -d
```

**With monitoring (Flower):**
```bash
docker-compose --profile monitoring up -d
```

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/surenganne/supermcp.git
   cd supermcp
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the development environment**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/api/v1/docs
   - Flower (Celery monitoring): http://localhost:5555 (with `--profile monitoring`)

5. **Start analyzing repositories**
   - Sign up/Login with your GitHub account
   - Use the Repository Browser to select a repository or enter a GitHub URL manually
   - Start the analysis and monitor progress in real-time
   - Review detailed results and generate MCP servers

## Configuration

### GitHub OAuth Setup

1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App with:
   - Application name: SuperMCP
   - Homepage URL: http://localhost:3000
   - Authorization callback URL: http://localhost:3000/auth/callback
3. Copy the Client ID and Client Secret to your `.env` file

### External Services (Required for MCP Discovery)

- **Tavily API**: Sign up at tavily.com for real-time MCP server discovery
- **Context7 MCP**: Configure your Context7 MCP server URL for documentation search

**Important**: SuperMCP uses real-time search to discover MCP servers dynamically. Set up these API keys to enable MCP recommendations.

## 🚫 Zero Fallbacks Policy

SuperMCP enforces a strict **zero fallbacks policy** to ensure only real, functional MCP servers are generated:

### **What This Means**
- **No Mock Data**: Zero placeholder, template, or sample implementations
- **No Fallback Logic**: System fails fast when real repository data isn't available
- **Real Code Only**: Generated MCP servers use actual repository functions and implementations
- **Functional Tools**: Every generated tool calls real business logic from the repository

### **How It Works**
- **Requirements Validation**: User requirements are validated against actual repository capabilities
- **Feasibility Scoring**: Real-time confidence scoring (0-100%) based on repository analysis
- **Fail-Fast Design**: System immediately fails with clear errors when real data is unavailable
- **Code Extraction**: Only actual functions, dependencies, and implementations are used

### **Benefits**
- **Guaranteed Functionality**: Generated MCP servers work with real repository code
- **No Surprises**: What you see in analysis is what you get in the generated server
- **Production Ready**: Generated servers are immediately usable with actual business logic
- **Quality Assurance**: Prevents generation of non-functional or placeholder implementations

## 📁 Repository Browser

SuperMCP includes an intuitive repository browser that makes it easy to select repositories for analysis:

### Features
- **🔍 Browse Your Repositories**: View all your GitHub repositories in a clean, organized interface
- **🔎 Smart Search**: Real-time search through repository names and descriptions
- **📊 Repository Information**: See language, stars, privacy status, and last updated date
- **🎯 One-Click Selection**: Click any repository to automatically populate the analysis form
- **✅ Visual Feedback**: Clear indication when a repository is selected from the browser
- **🔐 Authentication Aware**: Seamless integration with GitHub OAuth authentication

### How to Use
1. **Navigate to New Analysis**: Go to the "New Analysis" page
2. **Expand Repository Browser**: Click "▼ Browse" to open the repository browser
3. **Search (Optional)**: Use the search box to filter repositories
4. **Select Repository**: Click on any repository card to select it
5. **Start Analysis**: The repository URL will be auto-populated - click "Start Analysis"

### Authentication
The repository browser requires GitHub authentication to access your repositories. If you're not logged in, you'll see a helpful message with a login button.

## API Documentation

The API documentation is automatically generated and available at:
- Development: http://localhost:8000/docs
- Interactive API explorer with request/response examples

## Project Structure

```
supermcp/
├── backend/           # FastAPI backend application
├── frontend/          # Next.js frontend application
├── docker-compose.yml # Unified Docker Compose configuration
├── .env               # Environment variables (working copy)
├── .env.example       # Environment template with all options
├── reset-env.sh         # Complete environment reset
└── README.md          # Project documentation
```

## UI/UX Design

SuperMCP features a clean, modern interface designed for professional developers:

- **Streamlined Authentication**: Direct login flow without unnecessary intermediate pages
- **Clean Dashboard**: Focus on essential information and actions
- **Professional Design**: Minimal use of gradients and animations for a business-ready appearance
- **Consistent Layout**: Unified design language across all pages
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Development Scripts

### 🧹 `reset-env.sh`
Complete development environment reset script:
- Stops and removes all Docker containers/volumes
- Cleans database and Redis cache
- Starts fresh services with clean state
- Runs database migrations
- **Usage**: `./reset-env.sh`

## Open Source & Customization

SuperMCP is open source (MIT license) and designed for easy customization:

- **Environment Configuration**: Update API keys and settings via environment variables
- **Custom Deployment**: Deploy to your own infrastructure
- **UI Customization**: Modify the clean, professional design to match your brand
- **Feature Extensions**: Add new analysis capabilities or integrations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or feature requests, please open an issue on GitHub.

---

**Built with ❤️ using FastAPI, Next.js, and the power of MCP servers**